import { useState, useEffect } from 'react';
import { useParams } from 'react-router-dom';
import { FaChevronLeft, FaRedo, FaInfoCircle, FaUser, FaGavel, FaFileAlt } from 'react-icons/fa';
import TopBar from '../components/topbar/TopBar';
import LoadingSpinner from '../components/ui/LoadingSpinner';
import CaseInfoGroups from './CentralDetailsPage/CaseInfoGroups';
import ReportHistory from './CentralDetailsPage/ReportHistory';
import AddDeferral from './CentralDetailsPage/AddDeferral';
import AddAction from './CentralDetailsPage/AddAction';
import CaseFollowUpModal from './CentralDetailsPage/CaseFollowUpModal';
import styles from './CentralDetailsPage/CaseDetailsNew.module.css';
import { handleAddAction } from './CentralDetailsPage/ReportDetailsLogic';
import { notifyTaskCreated } from '../utils/CacheManager';
import useCaseDetails from './CentralDetailsPage/useCaseDetails';

const CaseDetails = ({ currentUser }) => {
  const { caseNumber } = useParams();
  const caseId = caseNumber;

  const {
    caseData, setCaseData,
    loading, error,
    deferrals, setDeferrals,
    actions, setActions,
    history, setHistory,
    refetch
  } = useCaseDetails(caseId, currentUser);

  const [showForm, setShowForm] = useState(null);
  const [isMobile, setIsMobile] = useState(window.innerWidth <= 768);

  const handleBack = () => {
    window.location.href = '/reports';
  };

  const handleRetry = () => {
    refetch();
  };

  const handleCancelForm = () => {
    setShowForm(null);
  };

  // دالة معالجة منطوق الأحكام
  const handleJudgmentDetected = (deferralData) => {
    if (window.showJudgmentVerdictModal) {
      window.showJudgmentVerdictModal(deferralData);
    }
  };

  const handleSaveDeferral = async (reportDate, selectedReasons, deferralDescription, setError) => {
    try {
      if (!reportDate || isNaN(new Date(reportDate).getTime()) || selectedReasons.length === 0) {
        setError("يرجى إدخال تاريخ التأجيلة واختيار سبب واحد على الأقل");
        return;
      }
      const newDeferral = {
        id: `${caseData.id}-defer-${Date.now()}`,
        date: reportDate,
        reasons: selectedReasons,
        description: deferralDescription || '',
        createdAt: new Date().toISOString(),
        isDeleted: false,
        isArchived: false,
      };
      const updatedDeferrals = [...deferrals, newDeferral];
      setDeferrals(updatedDeferrals);
      await import('../services/StorageService').then(({ updateCase }) =>
        updateCase(currentUser.uid, caseData.id, { deferrals: updatedDeferrals })
      );
      alert('تم إضافة التنبيه بتاريخ الجلسة بنجاح');
      notifyTaskCreated(currentUser.uid);
      handleCancelForm();
    } catch (err) {
      setError(err.message);
    }
  };

  const handleSaveAction = async (newAction, actionDeadline, linkType, linkedDeferralId, linkedActionId, reminderType, setError) => {
    try {
      const updatedCaseData = { ...caseData, deferrals };
      await handleAddAction(
        newAction,
        actionDeadline,
        linkType,
        linkedDeferralId,
        linkedActionId,
        reminderType,
        updatedCaseData,
        actions,
        setActions,
        () => {},
        () => {},
        () => {},
        () => {},
        () => {},
        () => {},
        setHistory
      );
      notifyTaskCreated(currentUser.uid);
    } catch (err) {
      setError(err.message);
    }
  };

  useEffect(() => {
    const handleResize = () => setIsMobile(window.innerWidth <= 768);
    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  if (loading) {
    return <LoadingSpinner message="جاري تحميل تفاصيل القضية..." />;
  }

  if (error) {
    return (
      <div className={styles.pageWrapper}>
        <TopBar currentUser={currentUser} casesList={[]} />
        <div className={styles.mainContainer}>
          <div className={styles.errorMessage}>
            <FaInfoCircle className={styles.errorIcon} />
            {error}
          </div>
          <div className={styles.buttonsSection}>
            <button onClick={handleBack} className={styles.backButton}>
              <FaChevronLeft className={styles.buttonIcon} />
              العودة
            </button>
            <button onClick={handleRetry} className={styles.editButton}>
              <FaRedo className={styles.buttonIcon} />
              إعادة المحاولة
            </button>
          </div>
        </div>
      </div>
    );
  }

  let pageTitle = 'تفاصيل القضية';
  let showCaseNumber = true;
  if (caseData.caseStatus === 'قيد النظر') {
    pageTitle = 'التفاصيل';
    showCaseNumber = false;
  } else if (caseData.caseStatus === 'محضر') {
    pageTitle = 'تفاصيل المحضر';
    showCaseNumber = false;
  } else if (caseData.caseStatus === 'دعوى قضائية') {
    pageTitle = 'تفاصيل الدعوى';
  }
  const isUnderConsideration = caseData?.caseStatus === 'قيد النظر';

  return (
    <div className={styles.pageWrapper} id="case-details-page">
      <TopBar currentUser={currentUser} casesList={[]} />
      <div className={styles.mainContainer}>
        <div className={styles.titleSection}>
          {caseData.clientName && (
            <p className={styles.caseSubtitle}>
              <FaUser className={styles.subtitleIcon} /> الموكل: {caseData.clientName}
            </p>
          )}
        </div>
        <div className={styles.twoColumnLayout}>
          <div className={styles.rightColumn}>
            <div className={styles.caseInfoGrid}>
              <CaseInfoGroups
                caseData={caseData}
                currentUser={currentUser}
                onCaseDataUpdate={setCaseData}
                onJudgmentDetected={handleJudgmentDetected}
              />
            </div>
            <div
              style={{
                display: 'flex',
                gap: isMobile ? '1.5rem' : '2.5rem',
                alignItems: 'stretch',
                marginTop: '1.5rem',
                flexDirection: isMobile ? 'column' : 'row'
              }}
            >
              <div style={{ flex: 2, width: '100%' }}>
                <ReportHistory currentUser={currentUser} actions={actions} deferrals={deferrals} history={history} caseItem={caseData} />
              </div>
              <div style={{ flex: 1, width: '100%' }}>
                <CaseFollowUpModal
                  caseId={caseId}
                  userId={currentUser.uid}
                  savedNotes={caseData?.notes || []}
                  onClose={() => {}}
                  onSave={async (data) => {
                    try {
                      let updatedNotes;
                      
                      // التحقق من نوع العملية (إضافة أم حذف)
                      if (data.deleted) {
                        // عملية حذف
                        updatedNotes = data.notes;
                      } else {
                        // عملية إضافة ملاحظة جديدة
                        updatedNotes = [...(caseData.notes || []), data];
                      }
                      
                      // تحديث البيانات المحلية
                      setCaseData(prevData => ({
                        ...prevData,
                        notes: updatedNotes
                      }));
                      
                      // حفظ البيانات في قاعدة البيانات/التخزين المحلي
                      const { updateCase } = await import('../services/StorageService');
                      await updateCase(currentUser.uid, caseData.id, { 
                        notes: updatedNotes,
                        updatedAt: new Date().toISOString()
                      });
                      
                    } catch (error) {
                      console.error('خطأ في حفظ الملاحظة:', error);
                      alert('حدث خطأ أثناء حفظ الملاحظة. يرجى المحاولة مرة أخرى.');
                    }
                  }}
                />
              </div>
            </div>
          </div>
          <div className={styles.leftColumn}></div>
        </div>
        {showForm === 'deferral' && (
          <AddDeferral
            currentUser={currentUser}
            onSave={handleSaveDeferral}
            onCancel={handleCancelForm}
            isUnderConsideration={isUnderConsideration}
          />
        )}
        {showForm === 'action' && (
          <AddAction
            currentUser={currentUser}
            caseItem={caseData}
            deferrals={deferrals}
            actions={actions}
            setActions={setActions}
            history={history}
            setHistory={setHistory}
            onSave={handleSaveAction}
            onCancel={handleCancelForm}
          />
        )}
      </div>
    </div>
  );
};

export default CaseDetails;
