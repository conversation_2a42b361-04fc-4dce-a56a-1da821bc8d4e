import React from 'react';
import { useTheme } from './ThemeProvider';
import { <PERSON><PERSON>un, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>heck, FiX } from 'react-icons/fi';
import styles from './ThemeTestComponent.module.css';

/**
 * مكون اختبار الثيمات - لاختبار جميع العناصر مع الثيمات المختلفة
 */
const ThemeTestComponent = () => {
  const { theme, changeTheme, getThemeInfo } = useTheme();
  const themeInfo = getThemeInfo();

  const testElements = [
    {
      title: 'الأزرار',
      content: (
        <div className={styles.buttonGroup}>
          <button className={styles.primaryButton}>زر أساسي</button>
          <button className={styles.secondaryButton}>زر ثانوي</button>
          <button className={styles.dangerButton}>زر خطر</button>
        </div>
      )
    },
    {
      title: 'حقول الإدخال',
      content: (
        <div className={styles.inputGroup}>
          <input 
            type="text" 
            placeholder="اكتب هنا..." 
            className={styles.textInput}
          />
          <input 
            type="email" 
            placeholder="البريد الإلكتروني" 
            className={styles.emailInput}
          />
          <textarea 
            placeholder="نص طويل..." 
            className={styles.textArea}
            rows={3}
          />
        </div>
      )
    },
    {
      title: 'البطاقات',
      content: (
        <div className={styles.cardGroup}>
          <div className={styles.testCard}>
            <h4>بطاقة اختبار</h4>
            <p>هذا نص تجريبي لاختبار البطاقة</p>
            <div className={styles.cardActions}>
              <button className={styles.cardButton}>
                <FiCheck /> موافق
              </button>
              <button className={styles.cardButton}>
                <FiX /> إلغاء
              </button>
            </div>
          </div>
        </div>
      )
    },
    {
      title: 'الإحصائيات',
      content: (
        <div className={styles.statsGroup}>
          <div className={styles.statItem}>
            <div className={styles.statValue}>125</div>
            <div className={styles.statLabel}>إجمالي القضايا</div>
          </div>
          <div className={styles.statItem}>
            <div className={styles.statValue}>45</div>
            <div className={styles.statLabel}>قضايا نشطة</div>
          </div>
          <div className={styles.statItem}>
            <div className={styles.statValue}>80</div>
            <div className={styles.statLabel}>قضايا مكتملة</div>
          </div>
        </div>
      )
    }
  ];

  return (
    <div className={styles.themeTest}>
      <div className={styles.header}>
        <h2 className={styles.title}>اختبار الثيمات</h2>
        <div className={styles.currentTheme}>
          <span>الثيم الحالي: </span>
          <strong>{themeInfo.name}</strong>
          <span className={styles.themeIcon}>{themeInfo.icon}</span>
        </div>
      </div>

      <div className={styles.themeSelector}>
        <h3>تغيير الثيم:</h3>
        <div className={styles.themeButtons}>
          <button 
            className={`${styles.themeButton} ${theme === 'light' ? styles.active : ''}`}
            onClick={() => changeTheme('light')}
          >
            <FiSun /> مضيء
          </button>
          <button 
            className={`${styles.themeButton} ${theme === 'dark' ? styles.active : ''}`}
            onClick={() => changeTheme('dark')}
          >
            <FiMoon /> مظلم
          </button>
          <button 
            className={`${styles.themeButton} ${theme === 'liquid' ? styles.active : ''}`}
            onClick={() => changeTheme('liquid')}
          >
            <FiDroplet /> شفاف
          </button>
        </div>
      </div>

      <div className={styles.testSections}>
        {testElements.map((section, index) => (
          <div key={index} className={styles.testSection}>
            <h3 className={styles.sectionTitle}>{section.title}</h3>
            <div className={styles.sectionContent}>
              {section.content}
            </div>
          </div>
        ))}
      </div>

      <div className={styles.colorPalette}>
        <h3>لوحة الألوان الحالية:</h3>
        <div className={styles.colorGrid}>
          <div className={styles.colorItem}>
            <div className={styles.colorSwatch} style={{backgroundColor: 'var(--current-bg-primary)'}}></div>
            <span>خلفية أساسية</span>
          </div>
          <div className={styles.colorItem}>
            <div className={styles.colorSwatch} style={{backgroundColor: 'var(--current-bg-secondary)'}}></div>
            <span>خلفية ثانوية</span>
          </div>
          <div className={styles.colorItem}>
            <div className={styles.colorSwatch} style={{backgroundColor: 'var(--current-text-primary)'}}></div>
            <span>نص أساسي</span>
          </div>
          <div className={styles.colorItem}>
            <div className={styles.colorSwatch} style={{backgroundColor: 'var(--primary-color)'}}></div>
            <span>لون أساسي</span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ThemeTestComponent;
