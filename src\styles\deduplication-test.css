/* 
 * ملف اختبار إزالة التكرار - Deduplication Test File
 * هذا الملف لاختبار أن جميع المتغيرات الموحدة تعمل بشكل صحيح
 */

@import './variables.css';

/* اختبار الأزرار الموحدة - Test Unified Buttons */
.test-button-primary {
  /* يجب أن يستخدم المتغيرات الموحدة */
  padding: var(--btn-padding);
  border-radius: var(--btn-border-radius);
  font-size: var(--btn-font-size);
  background: var(--btn-primary-gradient);
  color: var(--btn-primary-color);
  transition: var(--btn-transition);
}

.test-button-secondary {
  padding: var(--btn-padding);
  border-radius: var(--btn-border-radius);
  background-color: var(--btn-secondary-bg);
  color: var(--btn-secondary-color);
}

.test-button-icon {
  width: var(--btn-icon-size);
  height: var(--btn-icon-size);
  border-radius: var(--radius-full);
  background-color: var(--btn-icon-bg);
  border: var(--btn-icon-border);
}

/* اختبار حقول الإدخال الموحدة - Test Unified Form Inputs */
.test-input {
  padding: var(--input-padding);
  border-radius: var(--input-border-radius);
  font-size: var(--input-font-size);
  background-color: var(--input-bg);
  border: var(--input-border);
  color: var(--input-color);
  transition: var(--input-transition);
}

.test-input:focus {
  border: var(--input-border-focus);
  box-shadow: var(--input-shadow-focus);
}

/* اختبار Loading Spinner الموحد - Test Unified Loading Spinner */
.test-spinner {
  width: var(--loading-spinner-size);
  height: var(--loading-spinner-size);
  border: var(--loading-spinner-border);
  border-top: var(--loading-spinner-border-top);
  border-radius: 50%;
  animation: var(--loading-animation);
}

/* اختبار التحويلات الموحدة - Test Unified Transforms */
.test-hover-up:hover {
  transform: var(--transform-hover-up);
}

.test-hover-scale:hover {
  transform: var(--transform-hover-scale);
}

/* اختبار الثيمات - Test Themes */
.test-theme-colors {
  background-color: var(--current-bg-primary);
  color: var(--current-text-primary);
  border: 1px solid var(--current-border-primary);
}

/* اختبار الفئات الموحدة - Test Unified Classes */
.test-unified-button {
  /* يجب أن تعمل الفئات الموحدة */
  @extend .btn, .btn-primary;
}

/* ملاحظة: @extend لا يعمل في CSS عادي، لكن هذا للاختبار فقط */
