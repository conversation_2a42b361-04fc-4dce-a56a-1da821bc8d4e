@import '../../styles/variables.css';

/* حاوية سجل المهام */
.historyContainer {
  background: var(--page-background);
  border-radius: 0;
  box-shadow: none;
  padding: 0;
}

/* المحتوى الداخلي لسجل المهام */
.historyContent {
  padding: 0;
  background: var(--page-background);
  border-radius: 0 0 15px 15px;
}

/* Items List */
.itemsList {
  display: flex;
  flex-direction: column;
  gap: 0;
}

.simpleHistoryHeader {
  font-size: 1.3rem;
  font-weight: 600;
  color: #333;
  padding: 0.7em 0 0.5em 0;
  border-bottom: 1px solid #eee;
  margin-bottom: 1em;
  background: none;
  letter-spacing: 0.01em;
}

/* عناصر المهام - تصميم متجانس */
.historyItem {
  background: var(--page-background);
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  border: 1px solid #f0f0f0;
  padding: 1.1rem 1.2rem;
  margin-bottom: 8px;
  transition: all 0.2s ease;
  position: relative;
  border-color: #7d84cc;
}

.historyItem:last-child {
  margin-bottom: 0;
}

.historyItem:hover {
  background: #fafbfc;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  transform: translateY(-1px);
}

/* المحتوى الداخلي للعناصر - التاريخ أولاً ثم البيانات بجانبه */
.itemContent {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 0;
  margin: 0;
}

.itemDateAndBadge {
  display: flex;
  align-items: center;
  gap: 12px;
  min-width: fit-content;
}

.itemDate {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 0.9rem;
  color: #014871;
  background: rgba(1, 72, 113, 0.12);
  padding: 8px 12px;
  border-radius: 16px;
  min-width: fit-content;
  font-weight: 600;
  border: 1px solid rgba(1, 72, 113, 0.25);
  box-shadow: 0 1px 2px rgba(1, 72, 113, 0.15);
}

.itemBadge {
  flex-shrink: 0;
}

.typeBadge {
  display: inline-block;
  padding: 6px 12px;
  border-radius: 16px;
  font-size: 0.75rem;
  font-weight: 700;
  text-align: center;
  min-width: 55px;
  border: none;
  letter-spacing: 0.5px;
  text-transform: uppercase;
}

.deferralBadge {
  background: linear-gradient(135deg, #ff8f00, #ff6f00);
  color: white;
  box-shadow: 0 2px 6px rgba(255, 143, 0, 0.25);
}

.actionBadge {
  background: linear-gradient(135deg, #43a047, #2e7d32);
  color: white;
  box-shadow: 0 2px 6px rgba(67, 160, 71, 0.25);
}

.itemMain {
  flex: 1;
  display: flex;
  align-items: center;
  gap: 12px;
}

.itemText {
  font-size: 1rem;
  color: #1a1a1a;
  line-height: 1.5;
  font-weight: 600;
  flex: 1;
}

.dateIcon {
  font-size: 14px;
  color: #014871;
  font-weight: bold;
}

/* Actions */
.itemActions {
  display: flex;
  gap: 8px;
}

/* الأزرار - تصميم متجانس وأنيق */
.completeButton,
.deleteButton {
  width: 42px;
  height: 42px;
  padding: 0;
  border: none;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  box-sizing: border-box;
  font-size: 16px;
  font-weight: 600;
}

.completeButton {
  color: #00b894;
  background: rgba(0, 184, 148, 0.1);
  border: 1px solid rgba(0, 184, 148, 0.3);
}

.completeButton:hover {
  background: #00b894;
  color: white;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 184, 148, 0.3);
}

.deleteButton {
  color: #e74c3c;
  background: rgba(231, 76, 60, 0.1);
  border: 1px solid rgba(231, 76, 60, 0.3);
}

.deleteButton:hover {
  background: #e74c3c;
  color: white;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(231, 76, 60, 0.3);
}

/* Link Indicator */
.linkIndicator {
  display: inline-flex;
  align-items: center;
  gap: 4px;
  font-size: 0.8rem;
  color: #0984e3;
  background: #e3f2fd;
  padding: 4px 8px;
  border-radius: var(--border-radius-md);
  margin-right: 8px;
}

.linkIcon {
  font-size: 12px;
}

/* حالة فارغة */
.noReports {
  text-align: center;
  padding: var(--padding-section);
  background: var(--page-background);
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
  color: #1a1a1a;
  font-size: 1rem;
  font-weight: 600;
  line-height: 1.5;
  position: relative;
  overflow: hidden;
  border-radius: var(--border-radius-md);
}

.noReports::before {
  content: '📋';
  font-size: 2rem;
  display: block;
  opacity: 0.6;
  filter: none;
}

/* Action Button Styles */
.actionButton {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  border: none;
  border-radius: 20px;
  background: rgba(1, 72, 113, 0.1); /* لون خلفية خفيف */
  color: #014871; /* لون الخط الرئيسي */
  font-size: 0.9rem;
  font-weight: 600;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.actionButton:hover {
  background: rgba(1, 72, 113, 0.2); /* لون خلفية أغمق عند التحويم */
}

.actionButton .buttonIcon {
  font-size: 1rem;
}

/* إزالة تنسيق الحاوية لأزرار الإضافة */
.historyItem:has(.actionButton) {
  background: transparent !important;
  border: none !important;
  box-shadow: none !important;
  padding: 0 !important;
  margin-bottom: 0 !important;
  border-radius: 0 !important;
}

.historyItem:has(.actionButton):hover {
  background: transparent !important;
  box-shadow: none !important;
  transform: none !important;
}
.pageCardContainer {
  background: var(--page-background);
  border-radius: 20px;
  box-shadow: 
    0 20px 60px var(--shadow-medium),
    0 10px 30px var(--shadow-light);
  border: none;
  padding: var(--card-padding);
  margin: 0 auto;
  max-width: 900px;
}

/* تخطيط الهاتف - تصميم متجانس ومنظم */
@media (max-width: 768px) {
  .historyItem:not(.actionItem) {
    padding: 1.2rem;
    border-radius: 12px;
    margin-bottom: 12px;
      border-color: #d6d6d6;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    background: var(--page-background);
     border-color: #7d84cc;
  }
  
  .historyItem:last-child:not(.actionItem) {
    margin-bottom: 0;
    border-bottom: 1px solid #f0f0f0;
  }
  
  /* أزرار الإضافة في الهاتف ترث الإعدادات من القاعدة العامة */
  
  .itemContent {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
  }
  
  /* الصف الأول: التاريخ والشارة */
  .itemDateAndBadge {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 12px;
    margin-bottom: 8px;
    order: 1;
  }
  
  .itemDate {
    flex-shrink: 0;
    font-size: 0.85rem;
    padding: 6px 10px;
  }
  
  .itemBadge {
    flex-shrink: 0;
  }
  
  .typeBadge {
    padding: 4px 8px;
    font-size: 0.7rem;
    min-width: 45px;
    font-weight: 700;
  }
  
  /* الصف الثاني: النص الرئيسي */
  .itemMain {
    order: 2;
    margin-bottom: 12px;
  }
  
  .itemText {
    font-size: 1rem;
    line-height: 1.4;
    text-align: right;
    color: #2c3e50;
    font-weight: 600;
  }
  
  /* الصف الثالث: الأزرار */
  .itemActions {
    order: 3;
    display: flex;
    justify-content: space-evenly;
    gap: 8px;
    padding: 8px 0 0 0;
    margin: 0;
    border-top: 1px solid #f8f9fa;
    padding-top: 12px;
  }
  
  .completeButton,
  .deleteButton {
    width: 40px;
    height: 40px;
    border-radius: 10px;
    font-size: 16px;
    transition: all 0.2s ease;
  }
  
  .completeButton:hover,
  .deleteButton:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  }
  
  /* تخصيص أزرار الإضافة للهاتف - أحجام متساوية */
  .actionButton {
    flex: 1;
    min-height: 50px;
    max-width: none;
    width: auto;
    justify-content: center;
    text-align: center;
    font-size: 0.85rem;
    padding: 12px 8px;
    margin: 4px;
    border-radius: 25px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    box-sizing: border-box;
  }
  
  .actionButton .buttonIcon {
    font-size: 0.9rem;
    flex-shrink: 0;
    margin-left: 4px;
    
  }
  
  /* تخصيص حاوية الأزرار في الهاتف */
  .historyItem:has(.actionButton) .itemContent {
    flex-direction: row !important;
    align-items: stretch;
    gap: 0px;
    
  }
  
  .historyItem:has(.actionButton) .itemMain {
    display: flex;
    width: 100%;
    gap: 0px;
    align-items: stretch;
    
  }
}

/* نافذة تأكيد الحذف */
.deleteModalOverlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  backdrop-filter: blur(4px);
}

.deleteModal {
  background: var(--page-background);
  border-radius: 20px;
  padding: 24px;
  box-shadow: 
    0 25px 80px var(--shadow-medium),
    0 15px 50px var(--shadow-light);
  max-width: 500px;
  width: 90%;
  max-height: 80vh;
  overflow-y: auto;
  position: relative;
  direction: rtl;
  text-align: right;
}

.deleteModalHeader {
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 1px solid var(--neutral-200);
}

.deleteModalHeader h3 {
  margin: 0;
  font-size: 1.3rem;
  color: var(--danger-color);
  font-weight: 600;
}

.deleteModalContent {
  margin-bottom: 24px;
}

.deleteModalContent p {
  margin: 0 0 16px 0;
  font-size: 1.1rem;
  color: var(--neutral-700);
  line-height: 1.5;
}

.itemPreview {
  background: var(--neutral-100);
  border: 1px solid var(--neutral-200);
  border-radius: 12px;
  padding: 16px;
  font-size: 0.95rem;
  color: var(--neutral-700);
  line-height: 1.4;
  max-height: 150px;
  overflow-y: auto;
}

.previewHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  gap: 12px;
}

.previewBadge {
  padding: 4px 12px;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 600;
  flex-shrink: 0;
}

.deferralPreviewBadge {
  background: var(--deferral-light);
  color: var(--deferral-color);
}

.actionPreviewBadge {
  background: var(--action-light);
  color: var(--action-color);
}

.previewDate {
  font-size: 0.85rem;
  color: var(--neutral-600);
  font-weight: 500;
}

.previewContent {
  font-size: 0.9rem;
  line-height: 1.5;
}

.previewContent strong {
  color: var(--primary-color);
  font-weight: 600;
}

.deleteModalActions {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
}

.cancelButton,
.confirmDeleteButton {
  padding: 12px 24px;
  border-radius: 12px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  border: none;
  transition: all var(--transition-normal);
  min-width: 80px;
}

.cancelButton {
  background: var(--neutral-200);
  color: var(--neutral-700);
}

.cancelButton:hover:not(:disabled) {
  background: var(--neutral-300);
  transform: translateY(-1px);
}

.confirmDeleteButton {
  background: var(--danger-color);
  color: white;
}

.confirmDeleteButton:hover:not(:disabled) {
  background: var(--danger-dark);
  transform: translateY(-1px);
}

.cancelButton:disabled,
.confirmDeleteButton:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

/* تحسينات للشاشات الصغيرة */
@media (max-width: 768px) {
  .deleteModal {
    max-width: 95%;
    padding: 20px;
  }
  
  .deleteModalActions {
    flex-direction: column-reverse;
    gap: 8px;
  }
  
  .cancelButton,
  .confirmDeleteButton {
    width: 100%;
    padding: 14px;
  }
  
  .itemPreview {
    padding: 12px;
  }
  
  .previewHeader {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
}

/* أنماط للصلاحيات */
.itemActions {
  transition: all var(--transition-normal);
}

/* إضافة مؤشر بصري عند عدم وجود صلاحيات */
.itemActions:has(.deleteButton):not(:has(.deleteButton:not([style*="display: none"]))) {
  position: relative;
}

.itemActions:has(.deleteButton):not(:has(.deleteButton:not([style*="display: none"])))::after {
  content: "🔒";
  position: absolute;
  top: 50%;
  right: -20px;
  transform: translateY(-50%);
  font-size: 14px;
  opacity: 0.5;
  pointer-events: none;
}

/* نمط خاص للمهام بدون صلاحيات */
.itemActions:empty::after {
  content: "🔒 لا توجد صلاحيات";
  font-size: 12px;
  color: var(--neutral-500);
  font-style: italic;
}

/* رسالة عدم وجود صلاحية إضافة البيانات */
.noPermissionsItem {
  opacity: 0.7;
}

.noPermissionsMessage {
  text-align: center;
  padding: 20px;
  color: var(--neutral-600);
  font-style: italic;
  background: var(--neutral-100);
  border: 2px dashed var(--neutral-300);
  border-radius: 12px;
  margin: 10px 0;
}
.noPermissionsItem{
  padding: 0px 8px !important;
}