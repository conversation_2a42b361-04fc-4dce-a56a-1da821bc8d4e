import{r as x,j as e}from"./index-Bd3HN_hN.js";const X="_pageWrapper_1xn22_3",V="_mainContainer_1xn22_17",q="_noEvents_1xn22_45",J="_radarContainer_1xn22_69",K="_radarSvg_1xn22_91",Q="_radarRingCircle_1xn22_113",Z="_centerGroup_1xn22_129",I="_centerLabelSvg_1xn22_131",ee="_eventGroup_1xn22_149",te="_eventDotCircle_1xn22_153",ne="_clientNameText_1xn22_203",ie="_connectionLine_1xn22_225",oe="_simpleTooltip_1xn22_239",se="_visible_1xn22_273",f={pageWrapper:X,mainContainer:V,noEvents:q,radarContainer:J,radarSvg:K,radarRingCircle:Q,centerGroup:Z,centerLabelSvg:I,eventGroup:ee,eventDotCircle:te,clientNameText:ne,connectionLine:ie,simpleTooltip:oe,visible:se},k=(w=new Set)=>{const g=["#1976d2","#f06292","#4caf50","#ff9800","#ab47bc","#26c6da","#ef5350","#ffca28"];let v;if(w.size>=g.length){const s=g.filter(D=>!w.has(D));s.length>0?v=s[Math.floor(Math.random()*s.length)]:v=g[Math.floor(Math.random()*g.length)]}else do v=g[Math.floor(Math.random()*g.length)];while(w.has(v));return w.add(v),v},ae=({casesList:w})=>{const[g,v]=x.useState([]),[s,D]=x.useState({visible:!1,event:null,position:{top:0,left:0}}),[L,W]=x.useState({width:800,height:700}),R=x.useRef(null),_=x.useRef(null),G=x.useRef(null),B=new Date;x.useEffect(()=>{const r=()=>{if(_.current){const c=_.current.offsetWidth,t=window.innerWidth;let i,d;t<=480?(i=c*1.3,d=i*1.4):t<=768?(i=c*1.1,d=i*1.2):(i=Math.min(c,800),d=i*.875),W({width:i,height:d})}},l=setTimeout(()=>{r()},100);return window.addEventListener("resize",r),window.addEventListener("load",r),r(),()=>{clearTimeout(l),window.removeEventListener("resize",r),window.removeEventListener("load",r)}},[]);const A=x.useMemo(()=>{const r=[],l=new Map,c=new Set;return w.forEach(t=>{const i=t.fullCaseNumber||"غير محدد",d=t.clientName||"غير محدد";if(t.lastReport&&t.lastReportDate&&!isNaN(new Date(t.lastReportDate).getTime())){const o=`${i}-defer-${t.id}`,a=l.get(o)||k(new Set([...Array.from(l.values()),...Array.from(c)]));l.set(o,a),r.push({type:"تأجيل",date:new Date(t.lastReportDate).toISOString().split("T")[0],reasons:t.lastReport,clientName:d,caseNumber:i,courtLocation:t.courtLocation||"غير محدد",eventId:o,color:a,positionAngle:Math.random()*360})}if(t.lastAction&&t.lastActionDate&&!isNaN(new Date(t.lastActionDate).getTime())){const o=`${i}-action-${t.id}`;let a;if(t.linkedReport){const p=`${i}-defer-${t.id}`,y=r.find(j=>j.eventId===p&&j.type==="تأجيل");y&&(a=y.color,l.set(o,a))}a||(a=k(new Set([...Array.from(l.values()),...Array.from(c)])),c.add(a),l.set(o,a)),r.push({type:"إجراء",description:t.lastAction,deadline:new Date(t.lastActionDate).toISOString().split("T")[0],priority:"عادي",timestamp:t.lastActionDate,clientName:d,caseNumber:i,courtLocation:t.courtLocation||"غير محدد",eventId:o,color:a,linkedDeferralId:t.linkedReport?`${i}-defer-${t.id}`:"",linkedActionId:"",positionAngle:Math.random()*360})}}),r.sort((t,i)=>{const d=new Date(t.type==="تأجيل"?t.date:t.deadline),o=new Date(i.type==="تأجيل"?i.date:i.deadline);return d-o})},[w]);x.useEffect(()=>{v(A),D({visible:!1,event:null,position:{top:0,left:0}})},[A]);const F=(r,l)=>{var y;if(!l||!R.current||!_.current)return;if(s.visible&&((y=s.event)==null?void 0:y.eventId)===r.eventId){D({visible:!1,event:null,position:{top:0,left:0}});return}const c=R.current.getBoundingClientRect(),t=_.current.getBoundingClientRect(),i=l.getBoundingClientRect();let d=(i.left+i.right)/2-t.left,o=(i.top+i.bottom)/2-t.top;const a=200,p=100;d+a>c.width&&(d=c.width-a-10),d<0&&(d=10),o+p>c.height&&(o=i.top-t.top-p-10),o<0&&(o=i.bottom-t.top+10),o<0&&(o=10),o+p>c.height&&(o=c.height-p-10),D({visible:!0,event:r,position:{left:Math.max(10,Math.min(d,c.width-a-10)),top:Math.max(10,Math.min(o,c.height-p-10))}})},Y=()=>{D({visible:!1,event:null,position:{top:0,left:0}})},O=()=>{const{width:r,height:l}=L,c=`0 0 ${r} ${l}`,t=r/2,i=l/2+30,o=Math.min(r,l)/2- -60,a=10,p=o/(a+.5),y=1,j=g.map(n=>{const h=new Date(n.type==="تأجيل"?n.date:n.deadline),u=new Date(B),b=new Date(u.getFullYear(),u.getMonth(),u.getDate()),m=new Date(h.getFullYear(),h.getMonth(),h.getDate())-b,M=Math.floor(m/(1e3*60*60*24))+y;return{...n,daysDiff:M,eventDate:h}}).filter(n=>Math.abs(n.daysDiff)<=a),N=new Map,z=e.jsx("defs",{children:e.jsxs("filter",{id:"glow",x:"-50%",y:"-50%",width:"200%",height:"200%",children:[e.jsx("feGaussianBlur",{in:"SourceGraphic",stdDeviation:"1",result:"blur"}),e.jsx("feColorMatrix",{in:"blur",mode:"matrix",values:"1 0 0 0 0  0 1 0 0 0  0 0 1 0 0  0 0 0 18 -7",result:"glow"}),e.jsx("feBlend",{in:"SourceGraphic",in2:"glow",mode:"normal"})]})},"glow-filter"),H=e.jsx("defs",{children:e.jsxs("pattern",{id:"dots-pattern",x:"0",y:"0",width:"50",height:"50",patternUnits:"userSpaceOnUse",children:[e.jsx("rect",{x:"0",y:"0",width:"50",height:"50",fill:"transparent"}),e.jsx("circle",{cx:"10",cy:"10",r:"1",fill:"#aaaaaa",opacity:"0.4",filter:"url(#glow)"}),e.jsx("circle",{cx:"40",cy:"15",r:"1",fill:"#aaaaaa",opacity:"0.4",filter:"url(#glow)"}),e.jsx("circle",{cx:"20",cy:"35",r:"1",fill:"#aaaaaa",opacity:"0.4",filter:"url(#glow)"}),e.jsx("circle",{cx:"45",cy:"40",r:"1",fill:"#aaaaaa",opacity:"0.4",filter:"url(#glow)"})]})},"dot-pattern"),P=e.jsx("g",{children:e.jsx("rect",{x:"0",y:"0",width:r,height:l,fill:"url(#dots-pattern)"})},"bg-group"),E=[];for(let n=-10;n<=a;n++){if(n===0)continue;const h=Math.abs(n)*p;E.push(e.jsx("circle",{cx:t,cy:i,r:h,className:f.radarRingCircle},`ring-${n}`))}const T=[];j.forEach(n=>{const h=n.daysDiff,u=Math.abs(h)*p,C=n.positionAngle*Math.PI/180,m=t+u*Math.cos(C),S=i+u*Math.sin(C);N.set(n.eventId,{x:m,y:S});const M=n.type==="تأجيل"?8:6;T.push(e.jsxs("g",{className:f.eventGroup,transform:`translate(${m}, ${S})`,children:[e.jsx("circle",{cx:"0",cy:"0",r:M,fill:n.color,className:f.eventDotCircle,onClick:U=>F(n,U.target)}),e.jsx("text",{x:"0",y:-(M+5),className:f.clientNameText,children:n.clientName})]},n.eventId))});const $=[];return j.forEach(n=>{const h=N.get(n.eventId);if(!h)return;const u=(b,C)=>{const m=N.get(b);m&&$.push(e.jsx("line",{x1:h.x,y1:h.y,x2:m.x,y2:m.y,stroke:n.color,className:f.connectionLine},`${C}-${n.eventId}`))};n.type==="إجراء"&&n.linkedDeferralId&&u(n.linkedDeferralId,"conn-deferral"),n.type==="إجراء"&&n.linkedActionId&&u(n.linkedActionId,"conn-action")}),e.jsxs("div",{className:f.radarContainer,ref:_,style:{backgroundColor:"transparent"},children:[e.jsxs("svg",{ref:R,width:"100%",height:l,viewBox:c,preserveAspectRatio:"xMidYMid meet",className:f.radarSvg,children:[z,H,P,e.jsx("g",{children:$}),e.jsx("g",{children:E}),e.jsx("g",{transform:`translate(${t}, ${i})`,className:f.centerGroup,children:e.jsx("text",{x:"0",y:"0",dominantBaseline:"middle",textAnchor:"middle",className:f.centerLabelSvg,children:"اليوم"})}),e.jsx("g",{children:T})]}),s.visible&&s.event&&e.jsx("div",{ref:G,className:`${f.simpleTooltip} ${f.visible}`,style:{position:"absolute",left:`${s.position.left}px`,top:`${s.position.top}px`,backgroundColor:s.event.color},onClick:Y,children:s.event.type==="تأجيل"?e.jsxs(e.Fragment,{children:[e.jsxs("div",{children:[s.event.clientName," (",s.event.caseNumber,")"]}),e.jsx("div",{children:s.event.reasons}),e.jsxs("div",{children:["التاريخ: ",s.event.date]})]}):e.jsxs(e.Fragment,{children:[e.jsxs("div",{children:[s.event.clientName," (",s.event.caseNumber,")"]}),e.jsx("div",{children:s.event.description}),e.jsxs("div",{children:["الموعد: ",s.event.deadline]})]})})]})};return e.jsx("div",{className:f.pageWrapper,children:e.jsx("div",{className:f.mainContainer,children:g.length>0?O():e.jsx("div",{className:f.noEvents,children:"لا توجد أحداث لعرضها ضمن النطاق الزمني المحدد"})})})};export{ae as default};
