import { auth, db } from '../../config/firebaseConfig';
import { doc, collection, updateDoc, getDoc, writeBatch } from 'firebase/firestore';
import { getActiveAccount, getCase, updateCase, updateCaseOptimized } from '../../services/StorageService';

// تحسين: استيراد جميع Firebase modules المطلوبة مرة واحدة لتجنب الاستيراد المتكرر

// دالة مساعدة موحدة للتحقق من صحة البيانات
const validateCaseAndUser = (caseItem, currentUser) => {
  if (!caseItem?.id) {
    throw new Error('بيانات القضية غير متوفرة أو معرف القضية مفقود');
  }
  if (!currentUser?.uid) {
    throw new Error('بيانات المستخدم غير متوفرة أو المستخدم غير مسجل الدخول');
  }
  return true;
};

// دالة لتنظيف السجل التاريخي من الأحداث غير المكتملة
export const cleanupHistoryEntries = (history) => {
  if (!history || !Array.isArray(history)) return [];

  // الاحتفاظ فقط بالأحداث المكتملة والأحداث المهمة الأخرى
  return history.filter(item => {
    // الاحتفاظ بالأحداث المكتملة
    if (item.type === 'completed_action' || item.type === 'completed_deferral') {
      return true;
    }

    // الاحتفاظ بأحداث إنشاء القضية والأحداث المهمة الأخرى
    if (item.type === 'case_created' || item.type === 'case_updated' || item.type === 'expert_referral') {
      return true;
    }

    // الاحتفاظ بأحداث تحويل الحالة وتحويل الدرجة
    if (item.type === 'status_transfer' || item.type === 'degree_transfer') {
      return true;
    }

    // الاحتفاظ بأحداث إنشاء المحضر والدعوى
    if (item.type === 'report_created' || item.type === 'lawsuit_created') {
      return true;
    }

    // إزالة أحداث الإضافة والتعديل والأحداث الأخرى غير المكتملة
    return false;
  });
};

const MAX_COMBINED_ITEMS = 4;

const archiveOldCombinedItems = async (deferrals, actions, caseId, batch) => {
  const combinedItems = [
    ...deferrals.map((deferral, index) => ({
      ...deferral,
      type: 'deferral',
      originalIndex: index,
    })),
    ...actions.map((action, index) => ({
      ...action,
      type: 'action',
      originalIndex: index,
    })),
  ];

  if (combinedItems.length <= MAX_COMBINED_ITEMS) {
    return { deferrals, actions };
  }

  combinedItems.sort((a, b) => new Date(a.createdAt) - new Date(b.createdAt));
  const itemsToArchive = combinedItems.slice(0, combinedItems.length - MAX_COMBINED_ITEMS);
  const remainingItems = combinedItems.slice(combinedItems.length - MAX_COMBINED_ITEMS);

  for (const item of itemsToArchive) {
    const collectionName = item.type === 'action' ? 'actions_archive' : 'deferrals_archive';
    const archiveRef = collection(db, `cases/${caseId}/${collectionName}`);
    const archiveDoc = {
      ...item,
      userId: auth.currentUser?.uid || 'unknown',
      archivedAt: new Date().toISOString(),
    };
    const newDocRef = doc(archiveRef);
    batch.set(newDocRef, archiveDoc);
  }

  const remainingDeferrals = remainingItems
    .filter(item => item.type === 'deferral')
    .map(item => ({
      id: item.id || `${caseId}-defer-${item.originalIndex}`,
      date: item.date,
      reasons: item.reasons,
      content: item.content,
      createdAt: item.createdAt,
      updatedAt: item.updatedAt,
      isDeleted: item.isDeleted,
      userId: item.userId,
    }));

  const remainingActions = remainingItems
    .filter(item => item.type === 'action')
    .map(item => ({
      id: item.id || `${caseId}-action-${item.originalIndex}`,
      description: item.description,
      deadline: item.deadline,
      reminderType: item.reminderType,
      timestamp: item.timestamp,
      isDeleted: item.isDeleted,
      struckAt: item.struckAt,
      linkedDeferralId: item.linkedDeferralId,
      linkedActionId: item.linkedActionId,
      userId: item.userId,
      createdAt: item.createdAt,
    }));

  return { deferrals: remainingDeferrals, actions: remainingActions };
};

const moveToArchive = (batch, item, type, caseId) => {
  const collectionName = type === 'action' ? 'actions_archive' : 'deferrals_archive';
  const archiveRef = collection(db, `cases/${caseId}/${collectionName}`);
  const archiveDoc = {
    ...item,
    userId: auth.currentUser?.uid || 'unknown',
    struckAt: new Date().toISOString(),
  };
  const newDocRef = doc(archiveRef);
  batch.set(newDocRef, archiveDoc);
};

export const fetchCaseData = async (caseId) => {
  if (!auth.currentUser || !caseId) return null;

  const userId = auth.currentUser.uid;

  try {
    // استخدام خدمة التخزين المحلي للحصول على بيانات القضية حسب الحساب النشط
    const caseData = await getCase(userId, caseId);

    if (caseData) {
      // تحقق من الصلاحية: منشئ القضية أو عضو في المجموعة المرتبطة بالقضية
      let userIsOwner = caseData.userId === userId;
      let userIsGroupMember = false;
      if (caseData.groupId) {
        try {
          const { getGroups } = await import('../../services/GroupsService');
          const userGroups = await getGroups(userId);
          const groupIds = Array.isArray(userGroups) ? userGroups.map(g => g.id) : [];
          userIsGroupMember = groupIds.includes(caseData.groupId);
        } catch (e) {
          userIsGroupMember = false;
        }
      }
      if (userIsOwner || userIsGroupMember) {
        return caseData;
      } else {
        return null;
      }
    } else {
      return null;
    }
  } catch (e) {
    console.error('خطأ في جلب بيانات القضية:', e);
    throw e;
  }
};

export const fetchData = async (caseItem, setUpdates, setActions, setHistory) => {
  if (!auth.currentUser || !caseItem) return;

  try {
    const deferralsData = (caseItem.deferrals || []).map((deferral, index) => ({
      ...deferral,
      id: deferral.id || `${caseItem.id}-defer-${index}`,
    }));
    const actionsData = caseItem.actions || [];
    const historyData = caseItem.history || [];

    setUpdates(deferralsData);
    setActions(actionsData);
    setHistory(historyData);
  } catch (e) {
    console.error('خطأ في جلب البيانات:', e);
  }
};

export const handleSaveReport = async (
  caseItem, reportDate, selectedReasons, editingId, updates,
  setUpdates, setReportDate, setSelectedReasons, setShowAddForm,
  setEditingId, history, setHistory, onAddActionsPrompt
) => {
  if (caseItem?.caseStatus === 'قيد النظر') {
    alert('لا يمكن إضافة تأجيلة لأن حالة القضية "قيد النظر"');
    return;
  }

  if (reportDate && selectedReasons.length > 0) {
    const today = new Date();
    const selectedDate = new Date(reportDate);
    
    // تعيين الوقت إلى منتصف الليل للمقارنة الصحيحة للتواريخ فقط
    today.setHours(0, 0, 0, 0);
    selectedDate.setHours(0, 0, 0, 0);

    if (selectedDate < today) {
      alert('لا يمكن إضافة تأجيلة بتاريخ مضى');
      return;
    }

    const reasonsText = selectedReasons.join('، ');
    const newUpdate = `${reportDate} - ${reasonsText}`;
    const identifier = caseItem.id;
    const userId = auth.currentUser.uid;

    try {
      // التحقق من الحساب النشط
      const activeAccount = getActiveAccount();

      // نسخة من البيانات الحالية
      let deferrals = [...(caseItem.deferrals || [])];
      let actions = [...(caseItem.actions || [])];
      let historyData = [...(caseItem.history || [])];

      if (editingId !== null) {
        const deferralIndex = updates.findIndex((u, index) => index === editingId);
        if (deferralIndex !== -1) {
          const oldDeferral = deferrals[deferralIndex];
          deferrals[deferralIndex] = {
            id: oldDeferral.id || `${identifier}-defer-${deferralIndex}`,
            date: reportDate,
            reasons: selectedReasons,
            content: newUpdate,
            updatedAt: new Date().toISOString(),
            isDeleted: false,
            userId,
            createdAt: oldDeferral.createdAt || new Date().toISOString(),
          };
          // لا نضيف أي أحداث للأرشيف الزمني عند التعديل
          // التعديلات تؤثر فقط على قائمة المهام وليس الأرشيف
        }
      } else {
        const newDeferral = {
          id: `${identifier}-defer-${deferrals.length}`,
          date: reportDate,
          reasons: selectedReasons,
          content: newUpdate,
          createdAt: new Date().toISOString(),
          isDeleted: false,
          userId,
        };
        deferrals.push(newDeferral);

        // لا نضيف التأجيلة للأرشيف الزمني عند الإنشاء
        // سيتم إضافتها للأرشيف فقط عند الإكمال من صفحة الإشعارات
      }

      // حفظ البيانات حسب الحساب النشط (بدون تحديث الأرشيف)
      if (activeAccount === 'online') {
        // حفظ البيانات في Firestore - فقط التأجيلات والإجراءات
        const caseRef = doc(db, 'cases', identifier);
        await updateDoc(caseRef, {
          deferrals,
          actions,
          updatedAt: new Date().toISOString(),
        });
      } else {
        // حفظ البيانات في التخزين المحلي - فقط التأجيلات والإجراءات
        const updatedCaseData = {
          ...caseItem,
          deferrals,
          actions,
          updatedAt: new Date().toISOString(),
        };

        // استخدام خدمة التخزين المحلي لتحديث القضية
        await updateCase(userId, identifier, updatedCaseData);
      }

      setUpdates(deferrals);
      // لا نحدث الأرشيف الزمني في الواجهة عند إضافة التأجيلات
      setReportDate('');
      setSelectedReasons([]);
      setShowAddForm(false);
      setEditingId(null);

      // إشعار مدير التخزين المؤقت بإضافة مهمة جديدة (فقط للتأجيلات الجديدة، ليس التعديلات)
      if (editingId === null) {
        const { notifyTaskCreated } = await import('../../utils/CacheManager');
        notifyTaskCreated(userId);
      }

      alert('تم حفظ التأجيلة بنجاح');

      // إذا كان هناك سبب واحد فقط، اسأل عن إضافة الإجراءات الافتراضية
      if (selectedReasons.length === 1 && onAddActionsPrompt) {
        onAddActionsPrompt();
      }
    } catch (e) {
      alert('خطأ في حفظ التأجيلة: ' + e.message);
    }
  } else {
    alert('يرجى إدخال تاريخ التأجيلة واختيار سبب واحد على الأقل');
  }
};

export const handleAddAction = async (
  description,
  deadline,
  linkType,
  linkedDeferralId,
  linkedActionId,
  reminderType,
  caseItem,
  actions,
  setActions,
  setDescription,
  setDeadline,
  setLinkedDeferralId,
  setLinkedActionId,
  setLinkType,
  setShowAddForm,
  setHistory,
  templateActions = []
) => {


  // التحقق من وجود البيانات المطلوبة
  if (!caseItem || !caseItem.id) {
    console.error('❌ خطأ: بيانات القضية غير متوفرة في handleAddAction');
    alert('خطأ: بيانات القضية غير متوفرة');
    return;
  }
  
  if (!auth.currentUser || !auth.currentUser.uid) {
    console.error('❌ خطأ: بيانات المستخدم غير متوفرة في handleAddAction');
    alert('خطأ: المستخدم غير مسجل الدخول');
    return;
  }

  if (!description) {
    alert('يرجى إدخال وصف التذكير.');
    return;
  }

  let finalDeadline = deadline || new Date().toISOString().split('T')[0]; // استخدام تاريخ اليوم كقيمة افتراضية لو مفيش deadline

  if (linkType === 'custom') {
    if (!deadline) {
      alert('يرجى إدخال تاريخ الموعد النهائي.');
      return;
    }
    const selectedActionDate = new Date(deadline);
    const today = new Date();
    // تعيين الوقت إلى منتصف الليل للمقارنة الصحيحة للتواريخ فقط
    today.setHours(0, 0, 0, 0);
    selectedActionDate.setHours(0, 0, 0, 0);

    if (selectedActionDate < today) {
      alert('لا يمكن إضافة إجراء بموعد نهائي مضى');
      return;
    }
    finalDeadline = deadline;
  } else if (linkType === 'deferral') {
    if (!linkedDeferralId) {
      alert('يرجى اختيار تأجيل للربط.');
      return;
    }
    // استخدام caseItem.deferrals المحدّثة
    const deferral = caseItem.deferrals.find(
      (deferral, index) => deferral.id === linkedDeferralId || `${caseItem.id}-defer-${index}` === linkedDeferralId
    );
    if (!deferral) {
      console.log('Deferrals available:', caseItem.deferrals);
      console.log('Looking for linkedDeferralId:', linkedDeferralId);
      alert('التأجيل المحدد غير موجود.');
      return;
    }
    finalDeadline = deferral.date;

    // التحقق من أن تاريخ الإجراء قبل تاريخ التأجيل إذا كان إجراءً افتراضياً
    if (templateActions.length > 0 && new Date(finalDeadline) > new Date(deferral.date)) {
      alert(`تاريخ الإجراء (${finalDeadline}) يجب أن يكون قبل أو في نفس تاريخ التأجيل (${deferral.date})`);
      return;
    }
  } else if (linkType === 'action') {
    if (!linkedActionId) {
      alert('يرجى اختيار إجراء للربط.');
      return;
    }
    const linkedAction = actions.find(action => action.id === linkedActionId);
    if (!linkedAction) {
      alert('الإجراء المحدد غير موجود.');
      return;
    }
    finalDeadline = linkedAction.deadline;
  }

  const identifier = caseItem.id;
  const userId = auth.currentUser.uid;

  const newAction = {
    id: `${identifier}-action-${actions.length}`,
    description,
    deadline: finalDeadline,
    reminderType: reminderType || '',
    timestamp: new Date().toLocaleString(),
    isDeleted: false,
    struckAt: null,
    linkedDeferralId: linkType === 'deferral' ? linkedDeferralId : '',
    linkedActionId: linkType === 'action' ? linkedActionId : '',
    userId,
    createdAt: new Date().toISOString(),
  };

  try {
    // التحقق من الحساب النشط
    const activeAccount = getActiveAccount();

    // نسخة من البيانات الحالية
    let actionsData = [...(caseItem.actions || [])];
    let deferrals = [...(caseItem.deferrals || [])];
    let history = [...(caseItem.history || [])];

    actionsData.push(newAction);

    // لا نضيف الإجراء للأرشيف الزمني عند الإنشاء
    // سيتم إضافته للأرشيف فقط عند الإكمال من صفحة الإشعارات

    // حفظ البيانات حسب الحساب النشط (بدون تحديث الأرشيف)
    if (activeAccount === 'online') {
      // حفظ البيانات في Firestore - فقط التأجيلات والإجراءات
      const caseRef = doc(db, 'cases', identifier);
      await updateDoc(caseRef, {
        deferrals,
        actions: actionsData,
        updatedAt: new Date().toISOString(),
      });
    } else {
      // حفظ البيانات في التخزين المحلي - فقط التأجيلات والإجراءات
      const updatedCaseData = {
        ...caseItem,
        deferrals,
        actions: actionsData,
        updatedAt: new Date().toISOString(),
      };

      // استخدام خدمة التخزين المحلي لتحديث القضية
      await updateCase(userId, identifier, updatedCaseData);
    }

    setActions(actionsData);
    
    setDescription('');
    setDeadline('');
    setLinkedDeferralId('');
    setLinkedActionId('');
    setLinkType('');
    setShowAddForm(false);

    // لا نحدث الأرشيف الزمني عند إضافة الإجراءات
    // سيتم تحديثه فقط عند الإكمال

    // إشعار مدير التخزين المؤقت بإضافة مهمة جديدة
    const { notifyTaskCreated } = await import('../../utils/CacheManager');
    notifyTaskCreated(userId);

    alert('تم إضافة التذكير بنجاح');
  } catch (e) {
    console.error('خطأ في إضافة التذكير:', e);
    alert('فشل في إضافة التذكير. حاول مرة أخرى.');
  }
};

// دالة للتحقق من كون التأجيلة جلسة حكم
const checkForJudgmentDeferral = (completedDeferral) => {
  console.log('🔍 فحص نوع التأجيلة:', completedDeferral);

  if (!completedDeferral || !completedDeferral.reasons) {
    console.log('❌ لا توجد أسباب للتأجيلة');
    return false;
  }

  // التحقق من وجود "للحكم" في أسباب التأجيلة
  const isJudgmentDeferral = completedDeferral.reasons.some(reason =>
    reason && (
      reason.includes('للحكم') ||
      reason.includes('حكم') ||
      reason.includes('قرار')
    )
  );

  console.log('🎯 هل هي جلسة حكم؟', isJudgmentDeferral);
  console.log('📋 أسباب التأجيلة:', completedDeferral.reasons);

  return isJudgmentDeferral;
};

// دالة لتسجيل إكمال التأجيلة (حذف التنبيه وإضافة حدث في الأرشيف) - محسنة للسرعة
export const handleCompleteDeferral = async (deferralIndex, deferrals, setDeferrals, caseItem, setHistory, onJudgmentDetected) => {
  console.log('🚀 بدء handleCompleteDeferral مع المعاملات:', {
    deferralIndex,
    deferralsLength: deferrals?.length,
    caseId: caseItem?.id,
    hasOnJudgmentDetected: !!onJudgmentDetected
  });

  // التحقق من وجود البيانات المطلوبة
  if (!caseItem || !caseItem.id) {
    console.error('❌ خطأ: بيانات القضية غير متوفرة في handleCompleteDeferral');
    alert('خطأ: بيانات القضية غير متوفرة');
    return;
  }
  
  if (!auth.currentUser || !auth.currentUser.uid) {
    console.error('❌ خطأ: بيانات المستخدم غير متوفرة في handleCompleteDeferral');
    alert('خطأ: المستخدم غير مسجل الدخول');
    return;
  }

  const identifier = caseItem.id;
  const userId = auth.currentUser.uid;

  try {
    const activeAccount = getActiveAccount();
    let deferralsData = [...deferrals];
    let history = [...(caseItem.history || [])];

    // العثور على التأجيلة
    console.log('🔍 محاولة العثور على التأجيلة بالفهرس:', deferralIndex);
    console.log('📊 إجمالي التأجيلات المتاحة:', deferralsData.length);
    console.log('📋 قائمة التأجيلات:', deferralsData.map((d, i) => ({ index: i, date: d.date, reasons: d.reasons })));
    
    if (deferralIndex >= 0 && deferralIndex < deferralsData.length) {
      const completedDeferral = deferralsData[deferralIndex];
      console.log('✅ تم العثور على التأجيلة بنجاح');
      console.log('📋 التأجيلة المكتملة:', JSON.stringify(completedDeferral, null, 2));

      // فحص ما إذا كانت التأجيلة جلسة حكم
      const isJudgmentDeferral = checkForJudgmentDeferral(completedDeferral);
      console.log('⚖️ هل هي جلسة حكم؟', isJudgmentDeferral);
      console.log('🔧 دالة معالجة منطوق الأحكام متوفرة؟', !!onJudgmentDetected);

      if (isJudgmentDeferral && onJudgmentDetected) {
        console.log('🎯 تفعيل نافذة منطوق الأحكام...');
        // إذا كانت جلسة حكم، استدعاء دالة معالجة منطوق الأحكام
        onJudgmentDetected({
          deferralIndex,
          deferrals: deferralsData,
          setDeferrals,
          caseItem,
          setHistory,
          originalDate: completedDeferral.date
        });
        console.log('✅ تم استدعاء onJudgmentDetected - إيقاف التنفيذ');
        return 'judgment_detected'; // إيقاف التنفيذ هنا حتى يتم اختيار منطوق الحكم
      } else {
        console.log('⏭️ متابعة العملية العادية (ليست جلسة حكم أو لا توجد دالة معالجة)');
      }

      // إضافة حدث في الأرشيف الزمني بالتاريخ الأصلي للجلسة
      let eventDescription = `تم حضور جلسة: ${completedDeferral.reasons?.join('، ') || 'غير محدد'}`;

      // إضافة الوصف إذا كان موجوداً
      if (completedDeferral.description && completedDeferral.description.trim()) {
        eventDescription += ` - ${completedDeferral.description}`;
      }

      const completionEvent = {
        deferralId: deferralIndex,
        timestamp: new Date().toLocaleString(),
        action: eventDescription,
        type: 'completed_deferral',
        userId,
        description: completedDeferral.reasons?.join('، ') || 'غير محدد',
        deferralDescription: completedDeferral.description || '', // حفظ الوصف منفصلاً
        completedAt: completedDeferral.date, // استخدام التاريخ الأصلي للجلسة وليس تاريخ الحضور
        originalDate: completedDeferral.date,
        attendanceDate: new Date().toISOString(), // تاريخ الحضور الفعلي
      };
      history.push(completionEvent);

      // حذف التأجيلة من القائمة النشطة (التنبيهات)
      deferralsData.splice(deferralIndex, 1);

      // تحديث الحالة فوراً قبل حفظ قاعدة البيانات
      if (setDeferrals) setDeferrals(deferralsData);
      if (setHistory) setHistory(history);

      // حفظ البيانات في قاعدة البيانات باستخدام التحديث المحسن
      await updateCaseOptimized(userId, identifier, {
        deferrals: deferralsData,
        history: history,
      });

      console.log('✅ تم إكمال التأجيلة وتحديث قاعدة البيانات');
      return 'completed'; // إرجاع قيمة تشير إلى إكمال العملية العادية
    } else {
      console.error('❌ لم يتم العثور على التأجيلة في القائمة');
      console.error('📊 الفهرس المطلوب:', deferralIndex, 'طول القائمة:', deferralsData.length);
      throw new Error('لم يتم العثور على التأجيلة المطلوبة في قائمة المهام');
    }
  } catch (error) {
    console.error('خطأ في تسجيل إكمال التأجيلة:', error);
    throw error; // إعادة رمي الخطأ للتعامل معه في المكون المستدعي
  }
};

// دالة لتسجيل إكمال الإجراء (حذف التنبيه وإضافة حدث في الأرشيف)
export const handleCompleteAction = async (actionId, actions, setActions, caseItem, setHistory) => {
  // التحقق من وجود البيانات المطلوبة
  if (!caseItem || !caseItem.id) {
    console.error('❌ خطأ: بيانات القضية غير متوفرة في handleCompleteAction');
    alert('خطأ: بيانات القضية غير متوفرة');
    return;
  }
  
  if (!auth.currentUser || !auth.currentUser.uid) {
    console.error('❌ خطأ: بيانات المستخدم غير متوفرة في handleCompleteAction');
    alert('خطأ: المستخدم غير مسجل الدخول');
    return;
  }

  const identifier = caseItem.id;
  const userId = auth.currentUser.uid;

  try {
    // التحقق من الحساب النشط
    const activeAccount = getActiveAccount();

    // نسخة من البيانات الحالية
    let actionsData = [...(caseItem.actions || [])];
    let deferrals = [...(caseItem.deferrals || [])];
    let history = [...(caseItem.history || [])];

    // العثور على الإجراء
    console.log('🔍 البحث عن الإجراء بالمعرف:', actionId);
    console.log('📋 قائمة الإجراءات المتاحة:', actionsData.map(a => ({ id: a.id, description: a.description })));
    
    let actionIndex = actionsData.findIndex(action => action.id === actionId);
    
    // إذا لم يتم العثور على الإجراء بالمعرف، حاول البحث بالوصف والموعد النهائي
    if (actionIndex === -1) {
      console.log('⚠️ لم يتم العثور على الإجراء بالمعرف، البحث بطريقة بديلة...');
      // البحث عن آخر إجراء مضاف (في حالة عدم وجود ID صحيح)
      if (actionsData.length > 0) {
        actionIndex = actionsData.length - 1;
        console.log('🎯 استخدام آخر إجراء في القائمة، الفهرس:', actionIndex);
      }
    }
    
    if (actionIndex !== -1) {
      const completedAction = actionsData[actionIndex];

      // إضافة حدث في الأرشيف الزمني
      const completionEvent = {
        actionId: actionId,
        timestamp: new Date().toLocaleString(),
        action: `تم تنفيذ إجراء: "${completedAction.description}"`,
        type: 'completed_action',
        userId,
        description: completedAction.description,
        completedAt: new Date().toISOString(),
        originalDeadline: completedAction.deadline,
      };
      history.push(completionEvent);

      // حذف الإجراء من القائمة النشطة (التنبيهات)
      actionsData.splice(actionIndex, 1);

      // تحديث الحالة فوراً قبل حفظ قاعدة البيانات
      if (setActions) setActions(actionsData);
      if (setHistory) setHistory(history);

      // حفظ البيانات حسب الحساب النشط
      if (activeAccount === 'online') {
        // حفظ البيانات في Firestore
        const caseRef = doc(db, 'cases', identifier);
        await updateDoc(caseRef, {
          deferrals,
          actions: actionsData,
          history,
          updatedAt: new Date().toISOString(),
        });
      } else {
        // حفظ البيانات في التخزين المحلي
        const updatedCaseData = {
          ...caseItem,
          deferrals,
          actions: actionsData,
          history,
          updatedAt: new Date().toISOString(),
        };

        // استخدام خدمة التخزين المحلي لتحديث القضية
        await updateCaseOptimized(userId, identifier, updatedCaseData);
      }

      console.log('✅ تم إكمال الإجراء وتحديث قاعدة البيانات');
    } else {
      console.error('❌ لم يتم العثور على الإجراء في القائمة');
      throw new Error('لم يتم العثور على الإجراء المطلوب في قائمة المهام');
    }
  } catch (e) {
    console.error('خطأ في تسجيل إكمال الإجراء:', e);
    throw e; // إعادة رمي الخطأ للتعامل معه في المكون المستدعي
  }
};



// دالة حذف الإجراء نهائياً
export const handleStrikeAction = async (actionId, actions, setActions, caseItem, setHistory) => {
  // التحقق من صحة البيانات باستخدام الدالة الموحدة
  validateCaseAndUser(caseItem, auth.currentUser);

  const identifier = caseItem.id;
  const userId = auth.currentUser.uid;

  try {
    // التحقق من الحساب النشط
    const activeAccount = getActiveAccount();

    // نسخة من البيانات الحالية
    let actionsData = [...actions]; // استخدام القائمة المحلية المحدثة
    let deferrals = [...(caseItem.deferrals || [])];
    let history = [...(caseItem.history || [])];

    const actionIndex = actionsData.findIndex((a) => a.id === actionId);
    if (actionIndex !== -1) {
      const actionToDelete = actionsData[actionIndex];
      console.log('🗑️ حذف الإجراء:', actionToDelete);

      // إضافة حدث الحذف في الأرشيف
      const deletionEvent = {
        timestamp: new Date().toLocaleString(),
        action: `تم حذف إجراء: "${actionToDelete.description}"`,
        type: 'action_deleted',
        userId,
        description: actionToDelete.description,
        deletedAt: new Date().toISOString(),
        originalDeadline: actionToDelete.deadline,
      };
      history.push(deletionEvent);

      // حذف الإجراء نهائياً من القائمة
      actionsData.splice(actionIndex, 1);

      // تحديث الحالة المحلية أولاً
      if (setActions) setActions(actionsData);
      if (setHistory) setHistory(history);

      // حفظ البيانات حسب الحساب النشط
      if (activeAccount === 'online') {
        // حفظ البيانات في Firestore
        const caseRef = doc(db, 'cases', identifier);
        await updateDoc(caseRef, {
          deferrals,
          actions: actionsData,
          history,
          updatedAt: new Date().toISOString(),
        });
      } else {
        // حفظ البيانات في التخزين المحلي
        const updatedCaseData = {
          ...caseItem,
          deferrals,
          actions: actionsData,
          history,
          updatedAt: new Date().toISOString(),
        };

        // استخدام خدمة التخزين المحلي لتحديث القضية
        await updateCaseOptimized(userId, identifier, updatedCaseData);
      }

      console.log('✅ تم حذف الإجراء وتحديث قاعدة البيانات');
    }
  } catch (e) {
    alert('خطأ في شطب الإجراء: ' + e.message);
  }
};

export const handleEditReport = (deferralId, updates, setEditingDeferralId, setEditingDeferralData) => {
  const deferralIndex = updates.findIndex((u, index) => index === deferralId);
  const deferral = updates[deferralIndex];
  const today = new Date();
  const reportDateObj = new Date(deferral.date);

  // تعيين الوقت إلى منتصف الليل للمقارنة الصحيحة للتواريخ فقط
  today.setHours(0, 0, 0, 0);
  reportDateObj.setHours(0, 0, 0, 0);

  if (reportDateObj < today) {
    alert('لا يمكن تعديل تأجيلة مضى تاريخها');
    return;
  }

  setEditingDeferralId(deferralIndex);
  setEditingDeferralData({
    date: deferral.date,
    reasons: deferral.reasons || [],
  });
};

export const handleEditAction = (actionId, actions, setEditingActionId, setEditingActionData) => {
  const actionIndex = actions.findIndex((a) => a.id === actionId);
  const action = actions[actionIndex];
  const today = new Date();
  const actionDateObj = new Date(action.deadline);

  // تعيين الوقت إلى منتصف الليل للمقارنة الصحيحة للتواريخ فقط
  today.setHours(0, 0, 0, 0);
  actionDateObj.setHours(0, 0, 0, 0);

  if (actionDateObj < today) {
    alert('لا يمكن تعديل إجراء مضى موعده النهائي');
    return;
  }

  setEditingActionId(actionId);
  setEditingActionData({
    description: action.description,
    deadline: action.deadline,
    linkedDeferralId: action.linkedDeferralId || '',
    linkedActionId: action.linkedActionId || '',
  });
};

export const handleSaveEditedReport = async (
  caseItem, editingId, updatedDate, updatedReasons, updatedDescription, updates,
  setUpdates, setEditingDeferralId, setEditingDeferralData, history, setHistory
) => {
  if (!updatedDate || updatedReasons.length === 0) {
    alert('يرجى إدخال تاريخ التأجيلة واختيار سبب واحد على الأقل');
    return;
  }

  const today = new Date();
  const selectedDate = new Date(updatedDate);
  
  // تعيين الوقت إلى منتصف الليل للمقارنة الصحيحة للتواريخ فقط
  today.setHours(0, 0, 0, 0);
  selectedDate.setHours(0, 0, 0, 0);

  if (selectedDate < today) {
    alert('لا يمكن تعديل التأجيلة بتاريخ مضى');
    return;
  }

  const reasonsText = updatedReasons.join('، ');
  let newUpdate = `${updatedDate} - ${reasonsText}`;
  // إضافة الوصف إذا كان موجوداً
  if (updatedDescription && updatedDescription.trim()) {
    newUpdate += ` - ${updatedDescription}`;
  }
  const identifier = caseItem.id;
  const userId = auth.currentUser.uid;

  try {
    // التحقق من الحساب النشط
    const activeAccount = getActiveAccount();

    // نسخة من البيانات الحالية
    let deferrals = [...(caseItem.deferrals || [])];
    let actions = [...(caseItem.actions || [])];
    let historyData = [...(caseItem.history || [])];

    const deferralIndex = updates.findIndex((u, index) => index === editingId);
    if (deferralIndex !== -1) {
      const oldDeferral = deferrals[deferralIndex];
      deferrals[deferralIndex] = {
        id: oldDeferral.id || `${identifier}-defer-${deferralIndex}`,
        date: updatedDate,
        reasons: updatedReasons,
        description: updatedDescription || '', // إضافة الوصف
        content: newUpdate,
        updatedAt: new Date().toISOString(),
        isDeleted: false,
        userId,
        createdAt: oldDeferral.createdAt || new Date().toISOString(),
      };
      // لا نضيف أي أحداث للأرشيف الزمني عند التعديل
      // التعديلات تؤثر فقط على قائمة المهام وليس الأرشيف
    }

    // حفظ البيانات حسب الحساب النشط (بدون تحديث الأرشيف)
    if (activeAccount === 'online') {
      // حفظ البيانات في Firestore - فقط التأجيلات والإجراءات
      const caseRef = doc(db, 'cases', identifier);
      await updateDoc(caseRef, {
        deferrals,
        actions,
        updatedAt: new Date().toISOString(),
      });
    } else {
      // حفظ البيانات في التخزين المحلي - فقط التأجيلات والإجراءات
      const updatedCaseData = {
        ...caseItem,
        deferrals,
        actions,
        updatedAt: new Date().toISOString(),
      };

      // استخدام خدمة التخزين المحلي لتحديث القضية
      await updateCase(userId, identifier, updatedCaseData);
    }

    setUpdates(deferrals);
    // لا نحدث الأرشيف الزمني عند التعديل
    setEditingDeferralId(null);
    setEditingDeferralData(null);
    alert('تم تعديل التأجيلة بنجاح');
  } catch (e) {
    alert('خطأ في تعديل التأجيلة: ' + e.message);
  }
};

export const handleSaveEditedAction = async (
  caseItem, actionId, updatedDescription, updatedDeadline, updatedLinkedDeferralId, updatedLinkedActionId,
  actions, setActions, setEditingActionId, setEditingActionData, history, setHistory
) => {
  if (!updatedDescription) {
    alert('يرجى إدخال وصف الإجراء');
    return;
  }

  const today = new Date();
  const selectedDate = new Date(updatedDeadline);

  // تعيين الوقت إلى منتصف الليل للمقارنة الصحيحة للتواريخ فقط
  today.setHours(0, 0, 0, 0);
  selectedDate.setHours(0, 0, 0, 0);

  if (selectedDate < today) {
    alert('لا يمكن تعديل الإجراء بموعد نهائي مضى');
    return;
  }

  const identifier = caseItem.id;
  const userId = auth.currentUser.uid;

  try {
    // التحقق من الحساب النشط
    const activeAccount = getActiveAccount();

    // نسخة من البيانات الحالية
    let actionsData = [...(caseItem.actions || [])];
    let deferrals = [...(caseItem.deferrals || [])];
    let historyData = [...(caseItem.history || [])];

    const actionIndex = actions.findIndex(a => a.id === actionId);
    if (actionIndex !== -1) {
      const oldAction = actions[actionIndex];
      actionsData[actionIndex] = {
        ...oldAction,
        description: updatedDescription,
        deadline: updatedDeadline,
        linkedDeferralId: updatedLinkedDeferralId || '',
        linkedActionId: updatedLinkedActionId || '',
        updatedAt: new Date().toISOString(),
      };
      // لا نضيف أي أحداث للأرشيف الزمني عند التعديل
      // التعديلات تؤثر فقط على قائمة المهام وليس الأرشيف
    }

    // حفظ البيانات حسب الحساب النشط (بدون تحديث الأرشيف)
    if (activeAccount === 'online') {
      // حفظ البيانات في Firestore - فقط التأجيلات والإجراءات
      const caseRef = doc(db, 'cases', identifier);
      await updateDoc(caseRef, {
        deferrals,
        actions: actionsData,
        updatedAt: new Date().toISOString(),
      });
    } else {
      // حفظ البيانات في التخزين المحلي - فقط التأجيلات والإجراءات
      const updatedCaseData = {
        ...caseItem,
        deferrals,
        actions: actionsData,
        updatedAt: new Date().toISOString(),
      };

      // استخدام خدمة التخزين المحلي لتحديث القضية
      await updateCase(userId, identifier, updatedCaseData);
    }

    setActions(actionsData);
    // لا نحدث الأرشيف الزمني عند التعديل
    setEditingActionId(null);
    setEditingActionData(null);
    alert('تم تعديل الإجراء بنجاح');
  } catch (e) {
    alert('خطأ في تعديل الإجراء: ' + e.message);
  }
};

export const handleDeleteReport = async (deferralId, updates, setUpdates, history, setHistory, caseItem, actions, setActions) => {
  const deferralIndex = updates.findIndex((u, index) => index === deferralId);
  const deferral = updates[deferralIndex];

  // التحقق من صحة البيانات
  validateCaseAndUser(caseItem, auth.currentUser);

  const identifier = caseItem.id;
  const userId = auth.currentUser.uid;

  try {
    // التحقق من الحساب النشط
    const activeAccount = getActiveAccount();

    // نسخة من البيانات الحالية - استخدام البيانات المحلية المحدثة
    let deferralsData = [...updates]; // استخدام البيانات المحلية المُمررة
    let actionsData = [...(actions || [])]; // استخدام البيانات المحلية للإجراءات
    let historyData = [...(history || [])]; // استخدام البيانات المحلية للتاريخ

    if (deferralIndex >= 0 && deferralIndex < deferralsData.length) {
      const deletedDeferral = deferralsData[deferralIndex];
      console.log('🗑️ حذف التأجيلة:', deletedDeferral);

      // إضافة حدث الحذف في الأرشيف قبل الحذف
      const deletionEvent = {
        timestamp: new Date().toLocaleString(),
        action: `تم حذف تأجيل: ${deletedDeferral.reasons?.join('، ') || deletedDeferral.content || 'غير محدد'}`,
        type: 'deferral_deleted',
        userId,
        description: deletedDeferral.reasons?.join('، ') || deletedDeferral.content || 'غير محدد',
        deletedAt: new Date().toISOString(),
        originalDate: deletedDeferral.date,
      };
      historyData.push(deletionEvent);

      // حذف التأجيلة نهائياً من القائمة
      deferralsData.splice(deferralIndex, 1);

      // البحث عن الإجراءات المرتبطة بهذه التأجيلة وحذفها
      const deferralId = deletedDeferral.id || `${identifier}-defer-${deferralIndex}`;
      actionsData = actionsData.filter(action => {
        if (action.linkedDeferralId === deferralId) {
          console.log('🔗 حذف إجراء مرتبط:', action.description);
          return false; // حذف الإجراء المرتبط
        }
        return true;
      });

      // تحديث الحالة المحلية أولاً
      if (setUpdates) setUpdates(deferralsData);
      if (setActions) setActions(actionsData);
      if (setHistory) setHistory(historyData);

      // حفظ البيانات حسب الحساب النشط
      if (activeAccount === 'online') {
        // حفظ البيانات في Firestore
        const caseRef = doc(db, 'cases', identifier);
        await updateDoc(caseRef, {
          deferrals: deferralsData,
          actions: actionsData,
          history: historyData,
          updatedAt: new Date().toISOString(),
        });
      } else {
        // حفظ البيانات في التخزين المحلي
        const updatedCaseData = {
          ...caseItem,
          deferrals: deferralsData,
          actions: actionsData,
          history: historyData,
          updatedAt: new Date().toISOString(),
        };

        // استخدام خدمة التخزين المحلي لتحديث القضية
        await updateCaseOptimized(userId, identifier, updatedCaseData);
      }

      console.log('✅ تم حذف التأجيلة وتحديث قاعدة البيانات');
      alert('تم حذف التأجيلة بنجاح');
    } else {
      throw new Error('فهرس التأجيلة غير صحيح');
    }
  } catch (e) {
    console.error('خطأ في حذف التأجيلة:', e);
    alert('خطأ في حذف التأجيلة: ' + e.message);
    throw e;
  }
};

