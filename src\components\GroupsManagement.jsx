import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON><PERSON><PERSON>, FiPlus, FiTrash2, FiUserPlus, FiUserX,
  FiSettings, FiAlertTriangle, FiInfo, FiUserCheck, FiShield,
  FiShare2
} from 'react-icons/fi';
import { FaMobileAlt } from 'react-icons/fa';
import TopBar from '../components/topbar/TopBar';
import styles from './GroupsManagement.module.css';
import { getActiveAccount } from '../services/StorageService';
import permissionsService from '../services/PermissionsService';
import {
  getGroups,
  getMembers,
  createGroup,
  deleteGroup,
  addMember,
  deleteMember,
  getGroupTaskAssignments
} from '../services/GroupsService';

const GroupsManagement = ({ currentUser }) => {
  const [activeTab, setActiveTab] = useState('assignments');
  const [showAddMemberModal, setShowAddMemberModal] = useState(false);
  const [showCreateGroupModal, setShowCreateGroupModal] = useState(false);
  const [showShareCaseModal, setShowShareCaseModal] = useState(false);
  const [activeAccount, setActiveAccount] = useState(getActiveAccount());
  const [loading, setLoading] = useState(true);

  // حالة المجموعات والأعضاء
  const [groups, setGroups] = useState([]);
  const [members, setMembers] = useState([]);
  const [taskAssignments, setTaskAssignments] = useState([]);
  const [newGroupName, setNewGroupName] = useState('');
  const [newGroupDescription, setNewGroupDescription] = useState('');
  const [newMemberEmail, setNewMemberEmail] = useState('');
  const [newMemberRole, setNewMemberRole] = useState('member');
  const [selectedGroup, setSelectedGroup] = useState(null);
  const [selectedCaseId, setSelectedCaseId] = useState('');
  const [sharedCases, setSharedCases] = useState([]);
  const [sharePermissions, setSharePermissions] = useState({
    canView: true,
    canEdit: false,
    canDelete: false
  });
  const [formError, setFormError] = useState('');
  
  // حالة الصلاحيات للأدوار المختلفة
  const [permissions, setPermissions] = useState(permissionsService.getPermissions());
  
  // صلاحية إدارة الأعضاء للمستخدم الحالي
  const [canManageMembers, setCanManageMembers] = useState(false);
  const [userGroups, setUserGroups] = useState([]);

  useEffect(() => {
    // تحديث حالة الحساب عند تغييرها
    const checkAccountStatus = () => setActiveAccount(getActiveAccount());
    window.addEventListener('storage', checkAccountStatus);
    return () => window.removeEventListener('storage', checkAccountStatus);
  }, []);

  // جلب بيانات المجموعات والأعضاء من Firebase
  useEffect(() => {
    const fetchData = async () => {
      if (currentUser && activeAccount === 'online') {
        setLoading(true);
        try {
          // ✅ جلب جميع المجموعات التي ينتمي إليها المستخدم (سواء كان منشئًا أو عضوًا)
          const groupsData = await getGroups(currentUser.uid);
          setGroups(groupsData);

          // جلب الأعضاء المرتبطين فقط بمجموعات المستخدم الحالي
          const membersData = await getMembers();
          const filteredMembers = membersData.filter(member =>
            groupsData.some(group => group.id === member.groupId)
          );
          setMembers(filteredMembers);

          // جلب التكليفات
          const assignments = await getGroupTaskAssignments(currentUser.uid);
          setTaskAssignments(assignments);
        } catch (error) {
          console.error('خطأ في جلب البيانات:', error);
        } finally {
          setLoading(false);
        }
      }
    };

    fetchData();
  }, [currentUser, activeAccount]);

  // التحقق من صلاحيات المستخدم الحالي
  useEffect(() => {
    const fetchUserPermissions = async () => {
      if (currentUser?.uid && activeAccount === 'online') {
        try {
          const groups = await getGroups(currentUser.uid);
          setUserGroups(groups);
          
          const userRole = permissionsService.getCurrentUserRole(currentUser.uid, groups);
          const hasManagePermission = permissionsService.hasPermission(userRole, 'manageMembers');
          
          setCanManageMembers(hasManagePermission);
          
          console.log('🔍 GroupsManagement - User Role:', userRole);
          console.log('🔍 GroupsManagement - Can Manage Members:', hasManagePermission);
        } catch (error) {
          console.error('خطأ في جلب صلاحيات المستخدم:', error);
          setCanManageMembers(false);
        }
      } else {
        // في الوضع المحلي، السماح بإدارة الأعضاء افتراضياً
        setCanManageMembers(true);
      }
    };

    fetchUserPermissions();
  }, [currentUser?.uid, activeAccount]);

  // فتح نافذة إضافة عضو
  const handleAddMember = () => {
    setFormError('');
    setNewMemberEmail('');
    setNewMemberRole('member');
    setShowAddMemberModal(true);
  };

  // فتح نافذة إنشاء مجموعة
  const handleCreateGroup = () => {
    setFormError('');
    setNewGroupName('');
    setNewGroupDescription('');
    setShowCreateGroupModal(true);
  };

  // إنشاء مجموعة جديدة
  const handleSubmitCreateGroup = async (e) => {
    e.preventDefault();
    if (!newGroupName.trim()) return setFormError('يرجى إدخال اسم المجموعة');
    if (activeAccount !== 'online') return setFormError('يمكن إنشاء المجموعات فقط في وضع الاتصال بالإنترنت');

    try {
      setLoading(true);
      const groupData = {
        name: newGroupName.trim(),
        description: newGroupDescription.trim(),
        creatorName: currentUser.displayName || 'المستخدم الحالي',
        creatorEmail: currentUser.email || 'غير متوفر'
      };
      const newGroup = await createGroup(currentUser.uid, groupData);
      setGroups([...groups, newGroup]);
      // تحديث قائمة الأعضاء بعد إنشاء المجموعة
      const updatedMembers = await getMembers();
      setMembers(updatedMembers.filter(member => [...groups, newGroup].some(group => group.id === member.groupId)));
      setShowCreateGroupModal(false);
    } catch (error) {
      console.error('خطأ في إنشاء المجموعة:', error);
      setFormError(error.message || 'حدث خطأ أثناء إنشاء المجموعة.');
    } finally {
      setLoading(false);
    }
  };

  // إضافة عضو جديد
  const handleSubmitAddMember = async (e) => {
    e.preventDefault();
    if (!newMemberEmail.trim()) return setFormError('يرجى إدخال اسم المستخدم للعضو');
    const groupId = selectedGroup || (groups.length > 0 ? groups[0].id : null);
    if (!groupId) return setFormError('يرجى إنشاء مجموعة أولاً');
    if (activeAccount !== 'online') return setFormError('يمكن إضافة الأعضاء فقط في وضع الاتصال بالإنترنت');

    try {
      setLoading(true);
      const memberData = {
        groupId,
        email: newMemberEmail.trim(),
        role: newMemberRole
      };
      await addMember(memberData);
      // تحديث البيانات بعد الإضافة
      const updatedGroups = await getGroups(currentUser.uid);
      setGroups(updatedGroups);
      const updatedMembers = await getMembers();
      setMembers(updatedMembers.filter(member => updatedGroups.some(group => group.id === member.groupId)));
      setShowAddMemberModal(false);
    } catch (error) {
      console.error('خطأ في إضافة العضو:', error);
      setFormError(error.message || 'حدث خطأ أثناء إضافة العضو.');
    } finally {
      setLoading(false);
    }
  };

  // مشاركة قضية مع مجموعة
  const handleShareCase = async (groupId) => {
    if (!selectedCaseId || !groupId) {
      setFormError('يرجى اختيار القضية والمجموعة');
      return;
    }

    try {
      setLoading(true);
      await shareCaseWithGroup(groupId, selectedCaseId, sharePermissions);
      const updatedSharedCases = await getSharedCasesInGroup(groupId);
      setSharedCases(updatedSharedCases);
      setShowShareCaseModal(false);
      setSelectedCaseId('');
    } catch (error) {
      console.error('خطأ في مشاركة القضية:', error);
      setFormError('حدث خطأ أثناء مشاركة القضية');
    } finally {
      setLoading(false);
    }
  };

  // حذف مجموعة
  const handleDeleteGroup = async (groupId) => {
    if (!window.confirm('هل أنت متأكد من حذف هذه المجموعة؟')) return;
    if (activeAccount !== 'online') return alert('يمكن حذف المجموعات فقط في وضع الاتصال بالإنترنت');

    try {
      setLoading(true);
      await deleteGroup(groupId);
      setGroups(groups.filter(group => group.id !== groupId));
      setMembers(members.filter(member => member.groupId !== groupId));
    } catch (error) {
      console.error('خطأ في حذف المجموعة:', error);
      alert('حدث خطأ أثناء حذف المجموعة.');
    } finally {
      setLoading(false);
    }
  };

  // تحديث الصلاحيات وحفظها في الخدمة
  const handlePermissionChange = (role, permission) => {
    const newPermissions = {
      ...permissions,
      [role]: {
        ...permissions[role],
        [permission]: !permissions[role][permission]
      }
    };
    setPermissions(newPermissions);
    permissionsService.savePermissions(newPermissions);
  };

  // حذف عضو
  const handleDeleteMember = async (memberId) => {
    if (!window.confirm('هل أنت متأكد من إزالة هذا العضو؟')) return;
    if (activeAccount !== 'online') return alert('يمكن حذف الأعضاء فقط في وضع الاتصال بالإنترنت');

    const memberToDelete = members.find(m => m.id === memberId);
    if (!memberToDelete) return;

    try {
      setLoading(true);
      await deleteMember(memberId, memberToDelete.groupId);
      // تحديث البيانات بعد الحذف
      const updatedGroups = await getGroups(currentUser.uid);
      setGroups(updatedGroups);
      const updatedMembers = await getMembers();
      setMembers(updatedMembers.filter(member => updatedGroups.some(group => group.id === member.groupId)));
    } catch (error) {
      console.error('خطأ في حذف العضو:', error);
      alert('حدث خطأ أثناء حذف العضو.');
    } finally {
      setLoading(false);
    }
  };

  if (!currentUser) {
    return <div>جاري التحقق من حالة المستخدم...</div>;
  }

  return (
    <div className={styles.container}>
      <TopBar currentUser={currentUser} />
      <div className={styles.content}>
        <h1 className={styles.title}>
          <FiUsers className={styles.icon} />
          إدارة المجموعات
        </h1>

        {activeAccount === 'local' && (
          <div className={styles.offlineAlert}>
            <FiAlertTriangle className={styles.alertIcon} />
            <div className={styles.alertContent}>
              <h3>ميزة غير متاحة في الوضع المحلي</h3>
              <p>إدارة المجموعات متاحة فقط عند استخدام حساب أونلاين. يرجى التبديل إلى الوضع الأونلاين من صفحة الملف الشخصي للوصول إلى هذه الميزة.</p>
              <div className={styles.accountIndicator}>
                <FaMobileAlt className={styles.localIcon} />
                <span>أنت حاليًا تستخدم حساب محلي</span>
              </div>
            </div>
          </div>
        )}

        {activeAccount === 'online' && (
          <div className={styles.dashboard}>
            <div className={styles.sidebar}>
              <div className={styles.tabs}>
                <button
                  className={`${styles.tabButton} ${activeTab === 'assignments' ? styles.activeTab : ''}`}
                  onClick={() => setActiveTab('assignments')}
                >
                  <FiUserCheck className={styles.tabIcon} />
                  <span>جدول التكليفات</span>
                </button>
                <button
                  className={`${styles.tabButton} ${activeTab === 'members' ? styles.activeTab : ''}`}
                  onClick={() => setActiveTab('members')}
                >
                  <FiUserPlus className={styles.tabIcon} />
                  <span>الأعضاء</span>
                </button>
                <button
                  className={`${styles.tabButton} ${activeTab === 'permissions' ? styles.activeTab : ''}`}
                  onClick={() => setActiveTab('permissions')}
                >
                  <FiSettings className={styles.tabIcon} />
                  <span>الصلاحيات</span>
                </button>
                <button
                  className={`${styles.tabButton} ${activeTab === 'groups' ? styles.activeTab : ''}`}
                  onClick={() => setActiveTab('groups')}
                >
                  <FiUsers className={styles.tabIcon} />
                  <span>المجموعات</span>
                </button>
              </div>

              <div className={styles.statsContainer}>
                <div className={styles.statCard}>
                  <div className={styles.statIcon}>
                    <FiUsers />
                  </div>
                  <div className={styles.statInfo}>
                    <span className={styles.statValue}>{groups.length}</span>
                    <span className={styles.statLabel}>المجموعات</span>
                  </div>
                </div>

                <div className={styles.statCard}>
                  <div className={styles.statIcon}>
                    <FiUserPlus />
                  </div>
                  <div className={styles.statInfo}>
                    <span className={styles.statValue}>{members.length}</span>
                    <span className={styles.statLabel}>الأعضاء</span>
                  </div>
                </div>
              </div>
            </div>

            <div className={styles.mainContent}>
              {loading && (
                <div className={styles.loadingContainer}>
                  <div className={styles.loadingSpinner}></div>
                  <p>جاري تحميل البيانات...</p>
                </div>
              )}

              {!loading && activeTab === 'assignments' && (
                <div className={styles.section}>
                  <div className={styles.sectionHeader}>
                    <h2>
                      <FiUserCheck className={styles.headerIcon} />
                      جدول التكليفات
                    </h2>
                  </div>
                  <table className={styles.membersTable}>
                    <thead>
                      <tr>
                        <th>اسم العضو</th>
                        <th>المهمة المكلف بها</th>
                        <th>نوع المهمة</th>
                        <th>تاريخ التكليف</th>
                      </tr>
                    </thead>
                    <tbody>
                      {(() => {
                        // تجميع المهام حسب العضو وعرض أول مهمة فقط
                        const memberTasks = {};
                        taskAssignments.forEach(assignment => {
                          const memberId = assignment.assignedTo?.id;
                          if (memberId && !memberTasks[memberId]) {
                            memberTasks[memberId] = assignment;
                          }
                        });
                        
                        const firstTasks = Object.values(memberTasks);
                        
                        return firstTasks.length > 0 ? (
                          firstTasks.map((assignment, idx) => (
                            <tr key={assignment.id || idx}>
                              <td>{assignment.assignedTo?.name || 'غير محدد'}</td>
                              <td>
                                {assignment.taskType === 'deferral' ? (
                                  assignment.taskData?.reasons?.join('، ') || 'تأجيل'
                                ) : (
                                  assignment.taskData?.description || 'إجراء'
                                )}
                              </td>
                              <td>
                                <span className={`${styles.taskTypeBadge} ${
                                  assignment.taskType === 'deferral' ? styles.deferralBadge : styles.actionBadge
                                }`}>
                                  {assignment.taskType === 'deferral' ? 'تأجيل' : 'إجراء'}
                                </span>
                              </td>
                              <td>
                                {assignment.assignedAt ? 
                                  new Date(assignment.assignedAt).toLocaleDateString('ar-EG') : 
                                  'غير محدد'
                                }
                              </td>
                            </tr>
                          ))
                        ) : (
                          <tr>
                            <td colSpan={4}>لا توجد مهام مكلفة حالياً</td>
                          </tr>
                        );
                      })()}
                    </tbody>
                  </table>
                </div>
              )}

              {!loading && activeTab === 'groups' && (
                <div className={styles.section}>
                  <div className={styles.sectionHeader}>
                    <h2>
                      <FiUsers className={styles.headerIcon} />
                      إدارة المجموعات
                    </h2>
                    {canManageMembers && (
                      <button
                        className={styles.addButton}
                        onClick={handleCreateGroup}
                      >
                        <FiPlus /> إنشاء مجموعة جديدة
                      </button>
                    )}
                  </div>

                  {groups.length > 0 ? (
                    <div className={styles.groupsList}>
                      {groups.map(group => (
                        <div key={group.id} className={styles.groupCard}>
                          <div className={styles.groupHeader}>
                            <h3>{group.name}</h3>
                            <div className={styles.groupBadge}>
                              {group.members || 0} {group.members === 1 ? 'عضو' : 'أعضاء'}
                            </div>
                          </div>
                          {group.description && <p className={styles.groupDescription}>{group.description}</p>}
                          <div className={styles.groupMeta}>
                            <span>تاريخ الإنشاء: {group.created}</span>
                          </div>
                          <div className={styles.groupActions}>
                            {canManageMembers ? (
                              <button
                                className={styles.actionButton}
                                onClick={() => {
                                  setSelectedGroup(group.id);
                                  handleAddMember();
                                }}
                              >
                                <FiUserPlus /> إضافة عضو
                              </button>
                            ) : (
                              <div className={styles.noPermissionButton}>
                                🔒 لا يمكن إضافة أعضاء
                              </div>
                            )}
                            {canManageMembers ? (
                              <button
                                className={styles.deleteButton}
                                onClick={() => handleDeleteGroup(group.id)}
                              >
                                <FiTrash2 /> حذف
                              </button>
                            ) : (
                              <div className={styles.noPermissionButton}>
                                🔒 لا يمكن الحذف
                              </div>
                            )}
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className={styles.emptyState}>
                      <div className={styles.emptyStateIcon}><FiUsers /></div>
                      <h3>لا توجد مجموعات حالياً</h3>
                      {canManageMembers ? (
                        <>
                          <p>قم بإنشاء مجموعة جديدة للبدء في إدارة فريقك</p>
                          <button className={styles.emptyStateButton} onClick={handleCreateGroup}>
                            <FiPlus /> إنشاء مجموعة جديدة
                          </button>
                        </>
                      ) : (
                        <p>🔒 ليس لديك صلاحية إنشاء مجموعات جديدة</p>
                      )}
                    </div>
                  )}
                </div>
              )}

              {!loading && activeTab === 'members' && (
                <div className={styles.section}>
                  <div className={styles.sectionHeader}>
                    <h2>
                      <FiUserPlus className={styles.headerIcon} />
                      إدارة الأعضاء
                    </h2>
                    {canManageMembers && (
                      <button
                        className={styles.addButton}
                        onClick={handleAddMember}
                        disabled={groups.length === 0}
                      >
                        <FiUserPlus /> إضافة عضو جديد
                      </button>
                    )}
                  </div>
                  
                  {!canManageMembers && (
                    <div className={styles.permissionAlert}>
                      <FiAlertTriangle className={styles.alertIcon} />
                      <div className={styles.alertContent}>
                        <h3>صلاحيات محدودة</h3>
                        <p>ليس لديك صلاحية إدارة الأعضاء. بعض الميزات ستكون مخفية أو غير متاحة.</p>
                      </div>
                    </div>
                  )}
                  
                  {members.length > 0 ? (
                    <div className={styles.membersContainer}>
                      <div className={styles.filterBar}>
                        <div className={styles.filterGroup}>
                          <label>تصفية حسب المجموعة:</label>
                          <select
                            className={styles.filterSelect}
                            onChange={(e) => setSelectedGroup(e.target.value)}
                            value={selectedGroup || ''}
                          >
                            <option value="">جميع المجموعات</option>
                            {groups.map(group => (
                              <option key={group.id} value={group.id}>{group.name}</option>
                            ))}
                          </select>
                        </div>
                      </div>
                      <table className={styles.membersTable}>
                        <thead>
                          <tr>
                            <th>الاسم</th>
                            <th>البريد الإلكتروني</th>
                            <th>المجموعة</th>
                            <th>الدور</th>
                            <th>الإجراءات</th>
                          </tr>
                        </thead>
                        <tbody>
                          {members
                            .filter(member => !selectedGroup || member.groupId === selectedGroup)
                            .map(member => {
                              const group = groups.find(g => g.id === member.groupId);
                              const roleBadgeClass =
                                member.role === 'admin'
                                  ? styles.adminBadge
                                  : member.role === 'editor'
                                    ? styles.editorBadge
                                    : styles.memberBadge;
                              return (
                                <tr key={member.id}>
                                  <td>
                                    {member.name && member.name !== member.email 
                                      ? member.name 
                                      : member.email ? member.email.split('@')[0] : 'غير محدد'
                                    }
                                  </td>
                                  <td>{member.email}</td>
                                  <td>{group ? group.name : 'غير معروف'}</td>
                                  <td>
                                    <span className={`${styles.roleBadge} ${roleBadgeClass}`}>
                                      {member.role === 'admin' ? 'مدير' : member.role === 'editor' ? 'محرر' : 'عضو'}
                                    </span>
                                  </td>
                                  <td>
                                    <div className={styles.memberActions}>
                                      {canManageMembers ? (
                                        <button
                                          className={styles.deleteButton}
                                          onClick={() => handleDeleteMember(member.id)}
                                        >
                                          <FiUserX /> إزالة
                                        </button>
                                      ) : (
                                        <div className={styles.noPermissionText}>
                                          🔒 غير مسموح
                                        </div>
                                      )}
                                    </div>
                                  </td>
                                </tr>
                              );
                            })}
                        </tbody>
                      </table>
                    </div>
                  ) : (
                    <div className={styles.emptyState}>
                      <div className={styles.emptyStateIcon}><FiUserPlus /></div>
                      <h3>لا يوجد أعضاء حالياً</h3>
                      {groups.length > 0 ? (
                        canManageMembers ? (
                          <>
                            <p>قم بإضافة أعضاء جدد للبدء في إدارة فريقك</p>
                            <button className={styles.emptyStateButton} onClick={handleAddMember}>
                              <FiUserPlus /> إضافة عضو جديد
                            </button>
                          </>
                        ) : (
                          <p>🔒 ليس لديك صلاحية إضافة أعضاء جدد</p>
                        )
                      ) : (
                        canManageMembers ? (
                          <>
                            <p>قم بإنشاء مجموعة أولاً قبل إضافة الأعضاء</p>
                            <button className={styles.emptyStateButton} onClick={handleCreateGroup}>
                              <FiPlus /> إنشاء مجموعة جديدة
                            </button>
                          </>
                        ) : (
                          <p>🔒 ليس لديك صلاحية إنشاء مجموعات أو إضافة أعضاء</p>
                        )
                      )}
                    </div>
                  )}
                </div>
              )}

              {activeTab === 'permissions' && (
                <div className={styles.section}>
                  <div className={styles.sectionHeader}>
                    <h2><FiSettings className={styles.headerIcon} />إعدادات الصلاحيات</h2>
                  </div>
                  
                  {/* معلومات المستخدم الحالي */}

                  <div className={styles.permissionsContainer}>
                    {/* مدير */}
                    <div className={styles.permissionCard}>
                      <div className={styles.permissionHeader}>
                        <FiShield className={styles.roleIcon} />
                        <h3>مدير (Admin)</h3>
                      </div>
                      <div className={styles.permissionList}>
                        <div className={styles.permissionItem}>
                          <label>
                            <input
                              type="checkbox"
                              checked={permissions.admin.deleteData}
                              onChange={() => handlePermissionChange('admin', 'deleteData')}
                            />
                            <span className={styles.checkmark}></span>
                            إمكانية حذف البيانات
                          </label>
                        </div>
                        <div className={styles.permissionItem}>
                          <label>
                            <input
                              type="checkbox"
                              checked={permissions.admin.addData}
                              onChange={() => handlePermissionChange('admin', 'addData')}
                            />
                            <span className={styles.checkmark}></span>
                            إمكانية إضافة البيانات
                          </label>
                        </div>
                        <div className={styles.permissionItem}>
                          <label>
                            <input
                              type="checkbox"
                              checked={permissions.admin.addCases}
                              onChange={() => handlePermissionChange('admin', 'addCases')}
                            />
                            <span className={styles.checkmark}></span>
                            إمكانية إضافة القضايا
                          </label>
                        </div>
                        <div className={styles.permissionItem}>
                          <label>
                            <input
                              type="checkbox"
                              checked={permissions.admin.viewNotifications}
                              onChange={() => handlePermissionChange('admin', 'viewNotifications')}
                            />
                            <span className={styles.checkmark}></span>
                            إمكانية رؤية الإشعارات
                          </label>
                        </div>
                        <div className={styles.permissionItem}>
                          <label>
                            <input
                              type="checkbox"
                              checked={permissions.admin.manageMembers}
                              onChange={() => handlePermissionChange('admin', 'manageMembers')}
                            />
                            <span className={styles.checkmark}></span>
                            إمكانية إضافة أعضاء وحذف أعضاء
                          </label>
                        </div>

                        <div className={styles.permissionItem}>
                          <label>
                            <input
                              type="checkbox"
                              checked={permissions.admin.assignTasks}
                              onChange={() => handlePermissionChange('admin', 'assignTasks')}
                            />
                            <span className={styles.checkmark}></span>
                            إمكانية تكليف الأعضاء
                          </label>
                        </div>
                      </div>
                    </div>

                    {/* محرر */}
                    <div className={styles.permissionCard}>
                      <div className={styles.permissionHeader}>
                        <FiUserCheck className={styles.roleIcon} />
                        <h3>محرر (Editor)</h3>
                      </div>
                      <div className={styles.permissionList}>
                        <div className={styles.permissionItem}>
                          <label>
                            <input
                              type="checkbox"
                              checked={permissions.editor.deleteData}
                              onChange={() => handlePermissionChange('editor', 'deleteData')}
                            />
                            <span className={styles.checkmark}></span>
                            إمكانية حذف البيانات
                          </label>
                        </div>
                        <div className={styles.permissionItem}>
                          <label>
                            <input
                              type="checkbox"
                              checked={permissions.editor.addData}
                              onChange={() => handlePermissionChange('editor', 'addData')}
                            />
                            <span className={styles.checkmark}></span>
                            إمكانية إضافة البيانات
                          </label>
                        </div>
                        <div className={styles.permissionItem}>
                          <label>
                            <input
                              type="checkbox"
                              checked={permissions.editor.addCases}
                              onChange={() => handlePermissionChange('editor', 'addCases')}
                            />
                            <span className={styles.checkmark}></span>
                            إمكانية إضافة القضايا
                          </label>
                        </div>
                        <div className={styles.permissionItem}>
                          <label>
                            <input
                              type="checkbox"
                              checked={permissions.editor.viewNotifications}
                              onChange={() => handlePermissionChange('editor', 'viewNotifications')}
                            />
                            <span className={styles.checkmark}></span>
                            إمكانية رؤية الإشعارات
                          </label>
                        </div>
                        <div className={styles.permissionItem}>
                          <label>
                            <input
                              type="checkbox"
                              checked={permissions.editor.manageMembers}
                              onChange={() => handlePermissionChange('editor', 'manageMembers')}
                            />
                            <span className={styles.checkmark}></span>
                            إمكانية إضافة أعضاء وحذف أعضاء
                          </label>
                        </div>

                        <div className={styles.permissionItem}>
                          <label>
                            <input
                              type="checkbox"
                              checked={permissions.editor.assignTasks}
                              onChange={() => handlePermissionChange('editor', 'assignTasks')}
                            />
                            <span className={styles.checkmark}></span>
                            إمكانية تكليف الأعضاء
                          </label>
                        </div>
                      </div>
                    </div>

                    {/* عضو */}
                    <div className={styles.permissionCard}>
                      <div className={styles.permissionHeader}>
                        <FiUsers className={styles.roleIcon} />
                        <h3>عضو (Member)</h3>
                      </div>
                      <div className={styles.permissionList}>
                        <div className={styles.permissionItem}>
                          <label>
                            <input
                              type="checkbox"
                              checked={permissions.member.deleteData}
                              onChange={() => handlePermissionChange('member', 'deleteData')}
                            />
                            <span className={styles.checkmark}></span>
                            إمكانية حذف البيانات
                          </label>
                        </div>
                        <div className={styles.permissionItem}>
                          <label>
                            <input
                              type="checkbox"
                              checked={permissions.member.addData}
                              onChange={() => handlePermissionChange('member', 'addData')}
                            />
                            <span className={styles.checkmark}></span>
                            إمكانية إضافة البيانات
                          </label>
                        </div>
                        <div className={styles.permissionItem}>
                          <label>
                            <input
                              type="checkbox"
                              checked={permissions.member.addCases}
                              onChange={() => handlePermissionChange('member', 'addCases')}
                            />
                            <span className={styles.checkmark}></span>
                            إمكانية إضافة القضايا
                          </label>
                        </div>
                        <div className={styles.permissionItem}>
                          <label>
                            <input
                              type="checkbox"
                              checked={permissions.member.viewNotifications}
                              onChange={() => handlePermissionChange('member', 'viewNotifications')}
                            />
                            <span className={styles.checkmark}></span>
                            إمكانية رؤية الإشعارات
                          </label>
                        </div>
                        <div className={styles.permissionItem}>
                          <label>
                            <input
                              type="checkbox"
                              checked={permissions.member.manageMembers}
                              onChange={() => handlePermissionChange('member', 'manageMembers')}
                            />
                            <span className={styles.checkmark}></span>
                            إمكانية إضافة أعضاء وحذف أعضاء
                          </label>
                        </div>

                        <div className={styles.permissionItem}>
                          <label>
                            <input
                              type="checkbox"
                              checked={permissions.member.assignTasks}
                              onChange={() => handlePermissionChange('member', 'assignTasks')}
                            />
                            <span className={styles.checkmark}></span>
                            إمكانية تكليف الأعضاء
                          </label>
                        </div>
                      </div>
                    </div>
                  </div>

                </div>
              )}
            </div>
          </div>
        )}
      </div>

      {showAddMemberModal && (
        <div className={styles.modalOverlay}>
          <div className={styles.modal}>
            <div className={styles.modalHeader}>
              <h3><FiUserPlus className={styles.modalIcon} /> إضافة عضو جديد</h3>
            </div>
            {formError && <div className={styles.formError}><FiAlertTriangle /> {formError}</div>}
            <form onSubmit={handleSubmitAddMember}>
              <div className={styles.formGroup}>
                <label>البريد الإلكتروني للعضو</label>
                <input
                  type="email"
                  placeholder="أدخل البريد الإلكتروني للعضو"
                  value={newMemberEmail}
                  onChange={(e) => setNewMemberEmail(e.target.value)}
                  required
                />
              </div>
              {groups.length > 0 && (
                <div className={styles.formGroup}>
                  <label>المجموعة</label>
                  <select
                    value={selectedGroup || ''}
                    onChange={(e) => setSelectedGroup(e.target.value)}
                    required
                  >
                    <option value="" disabled>اختر المجموعة</option>
                    {groups.map(group => (
                      <option key={group.id} value={group.id}>{group.name}</option>
                    ))}
                  </select>
                </div>
              )}
              <div className={styles.formGroup}>
                <label>الدور</label>
                <select value={newMemberRole} onChange={(e) => setNewMemberRole(e.target.value)}>
                  <option value="admin">مدير</option>
                  <option value="editor">محرر</option>
                  <option value="member">عضو</option>
                </select>
                <div className={styles.roleDescription}>
                  {newMemberRole === 'admin' && <p>المدير لديه صلاحيات كاملة لإدارة المجموعات والأعضاء</p>}
                  {newMemberRole === 'editor' && <p>المحرر يمكنه تعديل المحتوى وإضافة أعضاء جدد</p>}
                  {newMemberRole === 'member' && <p>العضو لديه صلاحيات محدودة للقراءة والمشاركة فقط</p>}
                </div>
              </div>
              <div className={styles.modalButtons}>
                <button type="button" className={styles.cancelButton} onClick={() => setShowAddMemberModal(false)}>إلغاء</button>
                <button type="submit" className={styles.submitButton}>إضافة العضو</button>
              </div>
            </form>
          </div>
        </div>
      )}

      {showCreateGroupModal && (
        <div className={styles.modalOverlay}>
          <div className={styles.modal}>
            <div className={styles.modalHeader}>
              <h3><FiUsers className={styles.modalIcon} /> إنشاء مجموعة جديدة</h3>
            </div>
            {formError && <div className={styles.formError}><FiAlertTriangle /> {formError}</div>}
            <form onSubmit={handleSubmitCreateGroup}>
              <div className={styles.formGroup}>
                <label>اسم المجموعة</label>
                <input
                  type="text"
                  placeholder="أدخل اسم المجموعة"
                  value={newGroupName}
                  onChange={(e) => setNewGroupName(e.target.value)}
                  required
                />
              </div>
              <div className={styles.formGroup}>
                <label>الوصف</label>
                <textarea
                  placeholder="أدخل وصفًا للمجموعة (اختياري)"
                  value={newGroupDescription}
                  onChange={(e) => setNewGroupDescription(e.target.value)}
                ></textarea>
              </div>
              <div className={styles.infoBox}><p>سيتم إضافتك تلقائيًا كمدير لهذه المجموعة.</p></div>
              <div className={styles.modalButtons}>
                <button type="button" className={styles.cancelButton} onClick={() => setShowCreateGroupModal(false)}>إلغاء</button>
                <button type="submit" className={styles.submitButton}>إنشاء المجموعة</button>
              </div>
            </form>
          </div>
        </div>
      )}

      {showShareCaseModal && (
        <div className={styles.modalOverlay}>
          <div className={styles.modal}>
            <div className={styles.modalHeader}>
              <h3><FiShare2 className={styles.modalIcon} /> مشاركة قضية</h3>
            </div>
            {formError && <div className={styles.formError}><FiAlertTriangle /> {formError}</div>}
            <form onSubmit={(e) => { e.preventDefault(); handleShareCase(selectedGroup); }}>
              <div className={styles.formGroup}>
                <label>القضية</label>
                <select value={selectedCaseId} onChange={(e) => setSelectedCaseId(e.target.value)} required>
                  <option value="">اختر القضية</option>
                  {/* هنا يجب إضافة قائمة القضايا */}
                </select>
              </div>
              <div className={styles.formGroup}>
                <label>الصلاحيات</label>
                <div className={styles.permissionsContainer}>
                  <label><input type="checkbox" checked={sharePermissions.canView} onChange={(e) => setSharePermissions(prev => ({ ...prev, canView: e.target.checked }))}/> عرض</label>
                  <label><input type="checkbox" checked={sharePermissions.canEdit} onChange={(e) => setSharePermissions(prev => ({ ...prev, canEdit: e.target.checked }))}/> تعديل</label>
                  <label><input type="checkbox" checked={sharePermissions.canDelete} onChange={(e) => setSharePermissions(prev => ({ ...prev, canDelete: e.target.checked }))}/> حذف</label>
                </div>
              </div>
              <div className={styles.modalButtons}>
                <button type="button" className={styles.cancelButton} onClick={() => setShowShareCaseModal(false)}>إلغاء</button>
                <button type="submit" className={styles.submitButton} disabled={!selectedCaseId || !selectedGroup}>مشاركة</button>
              </div>
            </form>
          </div>
        </div>
      )}
    </div>
  );
};

export default GroupsManagement;