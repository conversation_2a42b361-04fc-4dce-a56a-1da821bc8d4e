import{r as m,q as O,c as G,d as R,w as B,g as k,j as e}from"./index-Bd3HN_hN.js";import{y as F,z as T,A as P,x as W,u as q,t as V}from"./index-DQWGoQ3q.js";import{T as Y}from"./TopBar-DgdxUhb0.js";import"./iconBase-BmtohqY9.js";const J="_pageContainer_3ob0e_1",z="_contentWrapper_3ob0e_17",K="_header_3ob0e_29",Q="_titleContainer_3ob0e_43",U="_pageTitle_3ob0e_57",X="_titleIcon_3ob0e_71",Z="_notificationCount_3ob0e_81",ee="_filterControls_3ob0e_101",te="_filterButton_3ob0e_121",ae="_active_3ob0e_155",se="_notificationsList_3ob0e_165",oe="_notificationCard_3ob0e_177",ie="_high_3ob0e_227",ne="_medium_3ob0e_237",re="_cardHeader_3ob0e_259",le="_notificationType_3ob0e_277",ce="_typeIcon_3ob0e_293",de="_date_3ob0e_303",pe="_cardBody_3ob0e_313",he="_caseNumber_3ob0e_325",me="_detailsGrid_3ob0e_345",_e="_detailItem_3ob0e_357",ue="_detailLabel_3ob0e_367",Ne="_emptyState_3ob0e_377",fe="_emptyIcon_3ob0e_395",ye="_loadingContainer_3ob0e_427",ge="_spinner_3ob0e_445",be="_spin_3ob0e_445",Ce="_errorMessage_3ob0e_471",t={pageContainer:J,contentWrapper:z,header:K,titleContainer:Q,pageTitle:U,titleIcon:X,notificationCount:Z,filterControls:ee,filterButton:te,active:ae,notificationsList:se,notificationCard:oe,high:ie,medium:ne,cardHeader:re,notificationType:le,typeIcon:ce,date:de,cardBody:pe,caseNumber:he,detailsGrid:me,detailItem:_e,detailLabel:ue,emptyState:Ne,emptyIcon:fe,loadingContainer:ye,spinner:ge,spin:be,errorMessage:Ce},xe="notifications_",Ie=5*60*1e3,h={DEFERRAL:"تأجيل",ACTION:"إجراء",UPCOMING_ACTION:"إجراء قريب"},d={HIGH:"high",MEDIUM:"medium"},$=i=>{const n=new Date;return n.setDate(n.getDate()+1),i.getDate()===n.getDate()&&i.getMonth()===n.getMonth()&&i.getFullYear()===n.getFullYear()},je=i=>{const n=new Date,u=new Date;return u.setDate(n.getDate()+3),i>n&&i<=u},M=i=>{try{return new Date(i).toLocaleDateString("ar-EG",{day:"numeric",month:"long",year:"numeric"})}catch{return"تاريخ غير صالح"}},Me=({currentUser:i})=>{const[n,u]=m.useState([]),[N,g]=m.useState([]),[f,b]=m.useState(!0),[j,D]=m.useState(null),[r,A]=m.useState("all"),C=m.useMemo(()=>`${xe}${(i==null?void 0:i.uid)||"guest"}`,[i]),E=m.useCallback(async(a=!1)=>{if(!i){u([]),g([]),b(!1),D("الرجاء تسجيل الدخول لعرض الإشعارات.");return}if(!a){const l=localStorage.getItem(C);if(l)try{const{data:p,timestamp:I}=JSON.parse(l);if(Date.now()-I<Ie){u(p),v(r,p),b(!1);return}}catch(p){console.error("Error parsing cache:",p),localStorage.removeItem(C)}}b(!0),D(null);try{const l=i.uid,p=O(G(R,"cases"),B("userId","==",l)),w=(await k(p)).docs.map(s=>({id:s.id,...s.data()})),y=[],De=new Date;w.forEach(s=>{(s.deferrals||[]).forEach((o,c)=>{var _;if(!(o.isDeleted||!o.date))try{const L=new Date(o.date);$(L)&&y.push({id:`${s.id}-defer-${c}`,type:h.DEFERRAL,priority:d.HIGH,caseNumber:s.fullCaseNumber||"غير محدد",clientName:s.clientName||"غير محدد",courtLocation:s.courtLocation||"غير محددة",date:L,displayDate:M(o.date),reasons:((_=o.reasons)==null?void 0:_.join("، "))||"لا يوجد سبب محدد"})}catch{console.error(`Invalid date for deferral ${c} in case ${s.id}:`,o.date)}}),(s.actions||[]).forEach((o,c)=>{if(!(o.isDeleted||!o.deadline))try{const _=new Date(o.deadline);$(_)?y.push({id:`${s.id}-action-${c}-high`,type:h.ACTION,priority:d.HIGH,caseNumber:s.fullCaseNumber||"غير محدد",clientName:s.clientName||"غير محدد",courtLocation:s.courtLocation||"غير محددة",date:_,displayDate:M(o.deadline),description:o.description||"لا يوجد وصف"}):je(_)&&y.push({id:`${s.id}-action-${c}-medium`,type:h.UPCOMING_ACTION,priority:d.MEDIUM,caseNumber:s.fullCaseNumber||"غير محدد",clientName:s.clientName||"غير محدد",courtLocation:s.courtLocation||"غير محددة",date:_,displayDate:M(o.deadline),description:o.description||"لا يوجد وصف"})}catch{console.error(`Invalid date for action ${c} in case ${s.id}:`,o.deadline)}})}),y.sort((s,o)=>{const c={[d.HIGH]:1,[d.MEDIUM]:2};return c[s.priority]!==c[o.priority]?c[s.priority]-c[o.priority]:s.date-o.date}),u(y),v(r,y);try{localStorage.setItem(C,JSON.stringify({data:y,timestamp:Date.now()}))}catch(s){console.error("Error writing to cache:",s)}}catch(l){console.error("Error fetching notifications:",l),D("حدث خطأ أثناء جلب الإشعارات. يرجى المحاولة لاحقًا أو التحقق من اتصالك."),u([]),g([])}finally{b(!1)}},[i,C,r]),v=m.useCallback((a,l)=>{g(a==="all"?l:l.filter(p=>p.priority===a))},[]),x=a=>{A(a),v(a,n)};m.useEffect(()=>{E();const a=setInterval(()=>{console.log("Refreshing notifications..."),E(!0)},30*1e3);return()=>clearInterval(a)},[E]);const H={[h.DEFERRAL]:q,[h.ACTION]:W,[h.UPCOMING_ACTION]:T},S=()=>{if(f)return e.jsxs("div",{className:t.loadingContainer,children:[e.jsx("div",{className:t.spinner}),e.jsx("p",{children:"جاري تحميل الإشعارات..."})]});if(j)return e.jsx("div",{className:t.errorMessage,children:j});if(N.length===0){const a=r==="all"?"لا توجد لديك أي إشعارات حالياً.":`لا توجد إشعارات ذات أولوية "${r==="high"?"عالية":"متوسطة"}".`;return e.jsxs("div",{className:t.emptyState,children:[e.jsx(P,{className:t.emptyIcon}),e.jsx("h3",{children:"لا توجد إشعارات للعرض"}),e.jsx("p",{children:a}),r!=="all"&&e.jsx("button",{className:`${t.filterButton} ${t.showAllButton}`,onClick:()=>x("all"),style:{marginTop:"20px",background:"#eee",color:"#555"},children:"عرض كل الإشعارات"})]})}return e.jsx("div",{className:t.notificationsList,children:N.map(a=>{const l=H[a.type]||F,p=a.type===h.DEFERRAL?"سبب التأجيل:":a.type===h.ACTION||a.type===h.UPCOMING_ACTION?"تفاصيل الإجراء:":"التفاصيل:",I=a.type===h.DEFERRAL?a.reasons:a.description;return e.jsxs("div",{className:`${t.notificationCard} ${t[a.priority]}`,children:[e.jsxs("div",{className:t.cardHeader,children:[e.jsxs("div",{className:t.notificationType,children:[e.jsx(l,{className:t.typeIcon}),e.jsx("span",{children:a.type})]}),e.jsxs("span",{className:t.date,children:[a.displayDate," "]})]}),e.jsxs("div",{className:t.cardBody,children:[e.jsxs("h3",{className:t.caseNumber,children:[e.jsx(V,{className:t.icon})," ",a.caseNumber]}),e.jsxs("div",{className:t.detailsGrid,children:[e.jsxs("div",{className:t.detailItem,children:[e.jsx("span",{className:t.detailLabel,children:"الموكل:"}),e.jsx("span",{className:t.detailValue,children:a.clientName})]}),e.jsxs("div",{className:t.detailItem,children:[e.jsx("span",{className:t.detailLabel,children:"المحكمة:"}),e.jsx("span",{className:t.detailValue,children:a.courtLocation})]}),e.jsxs("div",{className:t.detailItem,children:[e.jsx("span",{className:t.detailLabel,children:p}),e.jsx("span",{className:t.detailValue,children:I})]})]})]})]},a.id)})})};return e.jsxs("div",{className:t.pageContainer,children:[e.jsx(Y,{currentUser:i}),e.jsxs("div",{className:t.contentWrapper,children:[e.jsxs("div",{className:t.header,children:[e.jsxs("div",{className:t.titleContainer,children:[e.jsx(F,{className:t.titleIcon}),e.jsx("h1",{className:t.pageTitle,children:"الإشعارات"}),!f&&!j&&e.jsxs("span",{className:t.notificationCount,children:[N.length," ",N.length===1?"إشعار":N.length===2?"إشعاران":N.length>=3&&N.length<=10?"إشعارات":"إشعار"," "]})]}),!f&&n.length>0||!f&&e.jsxs("div",{className:t.filterControls,children:[e.jsx("button",{className:`${t.filterButton} ${r==="all"?t.active:""}`,onClick:()=>x("all"),disabled:f,children:"الكل"}),e.jsxs("button",{className:`${t.filterButton} ${r===d.HIGH?t.active:""}`,onClick:()=>x(d.HIGH),disabled:f,children:[e.jsx(T,{style:{marginLeft:"5px",color:r===d.HIGH?"white":"#ff4d6d"}}),"عالي"]}),e.jsxs("button",{className:`${t.filterButton} ${r===d.MEDIUM?t.active:""}`,onClick:()=>x(d.MEDIUM),disabled:f,children:[e.jsx(T,{style:{marginLeft:"5px",color:r===d.MEDIUM?"white":"#ffa94d"}}),"متوسط"]})]})]}),S()]})]})};export{Me as default};
