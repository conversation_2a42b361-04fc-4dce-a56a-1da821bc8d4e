import React from 'react';
import { FaArrowRight, FaEnvelope, FaInfoCircle, FaLock, FaUser } from 'react-icons/fa';
import TopBar from '../topbar/TopBar';
import styles from './ProfilePage.module.css';

const ViewEmailPage = ({ currentUser }) => {



  return (
    <div className={styles.pageWrapper}>
      <TopBar currentUser={currentUser} />
      <div className={styles.mainContainer}>
        <div className={styles.accountManagementSection}>
          <div className={styles.sidebarNavItem} style={{ visibility: 'hidden' }}>
            <div className={styles.sidebarNavIcon}><FaUser /></div>
            <span>المعلومات الشخصية</span>
          </div>
        </div>
        <div className={styles.mainContentArea}>
          <div className={styles.pageHeader}>
            <h2 className={styles.pageTitle}>
              البريد الإلكتروني
            </h2>
          </div>
          
          <div className={styles.personalInfoSection}>
            <div className={styles.editFieldForm}>
              <h3 className={styles.sectionTitle}>معلومات البريد الإلكتروني</h3>
              
              <div style={{ 
                color: '#5f6368', 
                fontSize: '16px', 
                marginBottom: '25px',
                lineHeight: '1.6',
                display: 'flex',
                alignItems: 'flex-start',
                backgroundColor: '#f8f9fa',
                padding: '15px 18px',
                borderRadius: '8px'
              }}>
                <div className="field-description">
                  <FaInfoCircle style={{ color: '#1a73e8', marginLeft: '10px', flexShrink: 0, fontSize: '18px' }} />
                  <span>البريد الإلكتروني لحساب تطبيق الاجندة القضائية.<br/>العنوان المُستخدم لمساعدتك أنت والمستخدمين الآخرين في التعرّف على حسابك. لا يمكنك تغيير هذا العنوان.</span>
                </div>
              </div>
              
              <div style={{
                padding: '15px 20px',
                backgroundColor: '#e8f0fe',
                borderRadius: '8px',
                marginBottom: '30px',
                textAlign: 'center',
                fontSize: '18px',
                fontWeight: '500',
                color: '#1a73e8',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                gap: '10px'
              }}>
                <FaEnvelope />
                <span>{currentUser?.email || 'غير محدد'}</span>
              </div>
              
              <div style={{ 
                color: '#5f6368', 
                fontSize: '13px', 
                marginBottom: '20px',
                lineHeight: '1.5',
                display: 'flex',
                alignItems: 'flex-start',
                backgroundColor: '#f1f3f4',
                padding: '12px 15px',
                borderRadius: '8px'
              }}>
                <div className="field-footer">
                  <FaLock style={{ color: '#5f6368', marginLeft: '8px', flexShrink: 0 }} />
                  <span>يتم استخدام بريدك الإلكتروني لتسجيل الدخول وإرسال إشعارات مهمة متعلقة بحسابك. إذا كنت بحاجة إلى تغيير بريدك الإلكتروني، يرجى التواصل مع الدعم الفني.</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <style jsx>{`
        .field-description {
          display: flex;
          align-items: flex-start;
          gap: 8px;
          width: 100%;
        }
        .field-description span {
          flex: 1;
        }
        .field-footer {
          display: flex;
          align-items: flex-start;
          gap: 8px;
          width: 100%;
        }
        .field-footer span {
          flex: 1;
        }
      `}</style>
    </div>
  );
};

export default ViewEmailPage;