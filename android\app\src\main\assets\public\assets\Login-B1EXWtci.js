import{r as l,u as L,j as e,t as D,k as S,v as E}from"./index-Bd3HN_hN.js";function A(){const[u,b]=l.useState(""),[h,w]=l.useState(""),[x,o]=l.useState(""),[v,p]=l.useState(""),[m,n]=l.useState(!1),[k,f]=l.useState(0),N=L(),g=5,c=15*60*1e3;l.useEffect(()=>{const t=localStorage.getItem("loginLockout");if(t){const{timestamp:r,attempts:i}=JSON.parse(t),a=Date.now();if(a-r<c&&i>=g){const s=Math.ceil((c-(a-r))/1e3/60);o(`تم قفل تسجيل الدخول مؤقتًا بسبب محاولات متكررة. يرجى المحاولة مرة أخرى بعد ${s} دقيقة${s>1?"ات":""}.`),n(!0)}else localStorage.removeItem("loginLockout"),f(0)}},[]);const M=async t=>{if(t.preventDefault(),o(""),p(""),n(!0),!u.trim()||!h.trim()){o("يرجى إدخال البريد الإلكتروني وكلمة المرور."),n(!1);return}const r=localStorage.getItem("loginLockout");let i=k;if(r){const{timestamp:a,attempts:s}=JSON.parse(r),d=Date.now();if(d-a<c&&s>=g){const j=Math.ceil((c-(d-a))/1e3/60);o(`تم قفل تسجيل الدخول مؤقتًا بسبب محاولات متكررة. يرجى المحاولة مرة أخرى بعد ${j} دقيقة${j>1?"ات":""}.`),n(!0);return}else d-a>=c?(localStorage.removeItem("loginLockout"),i=0):i=s}try{const s=(await D(S,u.trim(),h)).user;console.log("تم تسجيل الدخول بنجاح:",s),localStorage.removeItem("loginLockout"),f(0),N("/dashboard",{replace:!0})}catch(a){if(console.error("خطأ في تسجيل الدخول:",a.code,a.message),i+=1,f(i),localStorage.setItem("loginLockout",JSON.stringify({timestamp:Date.now(),attempts:i})),i>=g){const d=Math.ceil(c/1e3/60);o(`تم قفل تسجيل الدخول مؤقتًا بسبب محاولات متكررة. يرجى المحاولة مرة أخرى بعد ${d} دقيقة${d>1?"ات":""}.`),n(!0);return}let s="حدث خطأ أثناء تسجيل الدخول. يرجى المحاولة مرة أخرى.";switch(a.code){case"auth/invalid-credential":s="البريد الإلكتروني أو كلمة المرور غير صحيحة. يرجى التحقق من البيانات أو إعادة تعيين كلمة المرور.";break;case"auth/invalid-email":s="صيغة البريد الإلكتروني غير صحيحة.";break;case"auth/user-disabled":s="تم تعطيل حساب المستخدم هذا.";break;case"auth/too-many-requests":s="تم حظر تسجيل الدخول مؤقتًا بسبب محاولات كثيرة جدًا. يرجى المحاولة لاحقًا.";break;default:s="حدث خطأ غير متوقع. يرجى التحقق من اتصال الإنترنت والمحاولة لاحقًا."}o(s)}finally{i<g&&n(!1)}},y=async()=>{if(!u.trim()){o("يرجى إدخال البريد الإلكتروني أولاً.");return}o(""),p(""),n(!0);try{await E(S,u.trim()),p("تم إرسال رابط إعادة تعيين كلمة المرور إلى بريدك الإلكتروني. يرجى التحقق من صندوق الوارد أو البريد العشوائي.")}catch(t){let r="حدث خطأ أثناء إرسال رابط إعادة التعيين.";switch(t.code){case"auth/invalid-email":r="صيغة البريد الإلكتروني غير صحيحة.";break;case"auth/user-not-found":r="لا يوجد حساب مرتبط بهذا البريد الإلكتروني.";break;default:r="حدث خطأ غير متوقع. يرجى المحاولة لاحقًا."}o(r)}finally{n(!1)}};return e.jsx("div",{className:"login-container",children:e.jsxs("div",{className:"login-box",children:[e.jsx("h2",{children:"تسجيل الدخول"}),m&&e.jsxs("div",{className:"loading-container",children:[e.jsx("div",{className:"loader"}),e.jsx("p",{className:"loading-text",children:"جاري المعالجة..."})]}),x&&e.jsx("div",{className:"error-message",children:x}),v&&e.jsx("div",{className:"success-message",children:v}),e.jsxs("form",{onSubmit:M,children:[e.jsxs("div",{className:"form-group",children:[e.jsx("label",{htmlFor:"email",children:"البريد الإلكتروني"}),e.jsx("input",{type:"email",id:"email",name:"email",value:u,onChange:t=>b(t.target.value),required:!0,disabled:m,placeholder:"أدخل بريدك الإلكتروني"})]}),e.jsxs("div",{className:"form-group",children:[e.jsx("label",{htmlFor:"password",children:"كلمة المرور"}),e.jsx("input",{type:"password",id:"password",name:"password",value:h,onChange:t=>w(t.target.value),required:!0,disabled:m,placeholder:"أدخل كلمة المرور"})]}),e.jsx("button",{type:"submit",disabled:m,children:m?"جاري تسجيل الدخول...":"تسجيل الدخول"})]}),e.jsxs("div",{className:"login-links",children:[e.jsx("p",{children:e.jsx("a",{href:"#",onClick:t=>{t.preventDefault(),y()},children:"نسيت كلمة المرور؟"})}),e.jsxs("p",{children:["ليس لديك حساب؟ ",e.jsx("a",{href:"/signup",children:"إنشاء حساب جديد"})]})]})]})})}export{A as default};
