/* متغيرات الألوان الموحدة مع باقي الكروت */
@import '../../styles/variables.css';

:root {
  /* استخدام المتغيرات الموحدة للكروت */
  --modal-gradient-bg: var(--card-header-bg);
  --modal-gradient-light: var(--card-content-bg);
  --modal-primary-color: #014871;
  --modal-accent-color: #4a8fa3;
}

.modalContent {
  direction: rtl;
  text-align: right;
  background: var(--page-background);
  border-radius: 20px;
  padding: 1.5rem;
  box-shadow: 
    0 20px 60px var(--shadow-medium),
    0 10px 30px var(--shadow-light);
  transition: all var(--transition-normal);
  border: none;
  width: 100%;
  margin: 0 auto;
  box-sizing: border-box;
  position: relative;
  overflow: hidden;
  min-height: 200px;
  display: flex;
  flex-direction: column;
}

.modalContent:hover {
  box-shadow: 
    0 25px 70px var(--shadow-medium),
    0 15px 40px var(--shadow-light);
  transform: translateY(-4px);
}

/* Header موحد مع كروت المعلومات - نفس تصميم groupTitle */
.notesHeader {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 1.1rem;
  font-weight: 500;
  color: var(--neutral-700);
  margin-bottom: 1rem;
  padding-bottom: 0.5rem;
  border-bottom: 1px solid var(--neutral-200);
  background: none;
  border-radius: 0;
  position: relative;
}

.notesHeaderLeft {
  display: flex;
  align-items: center;
  gap: 8px;
}

.notesHeaderTitle {
  font-size: 1.1rem;
  color: var(--neutral-700);
  margin: 0;
  font-weight: 500;
  flex: 1;
}

.buttonRow {
  display: flex;
  justify-content: center;
  gap: 8px;
  margin-bottom: 15px;
}

.styledButton {
  padding: 8px 16px;
  border-radius: 20px;
  cursor: pointer;
  font-size: 16px;
  text-align: center;
  background: var(--page-background);
  color: var(--neutral-700);
  border: none;
  box-shadow: 
    0 4px 15px var(--shadow-light),
    0 2px 8px var(--shadow-medium);
  white-space: nowrap;
  transition: all var(--transition-normal);
  font-weight: 600;
}

.styledButton:hover {
  background: var(--neutral-200);
  transform: translateY(-2px);
  box-shadow: 
    0 8px 25px var(--shadow-light),
    0 4px 15px var(--shadow-medium);
  color: var(--neutral-800);
}

/* المحتوى الداخلي موحد مع كروت المعلومات - نفس تصميم infoItems */
.notesContent {
  display: grid;
  gap: 1rem;
  flex: 1;
  background: transparent;
  overflow: visible;
}

.notesInnerContent {
  padding: 0;
  flex: 1;
}

/* مساحة فاضية عند عدم وجود ملاحظات */
.emptyNotesSpace {
  min-height: 120px;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

.emptyNotesMessage {
  text-align: center;
  width: 100%;
}

.emptyText {
  color: var(--neutral-600);
  font-size: 14px;
  margin: 10px 0;
  opacity: 0.7;
}

.textareaSection {
  margin-bottom: 12px;
}

.typeSection {
  margin-bottom: 12px;
}

.textareaTitle {
  font-size: 16px;
  color: var(--neutral-700);
  text-align: right;
  margin-bottom: 8px;
  font-weight: 500;
}

.textareaContainer {
  display: flex;
  justify-content: center;
}

.styledInput {
  background-color: var(--page-background);
  padding: 12px;
  border-radius: 20px;
  color: var(--neutral-700);
  text-align: right;
  font-size: 16px;
  border: none;
  box-shadow: 
    inset 0 2px 8px var(--shadow-light),
    0 2px 4px var(--shadow-medium);
  resize: none;
  direction: rtl;
  width: 100%;
  box-sizing: border-box;
  min-height: 60px;
  transition: all var(--transition-normal);
  font-weight: 500;
}

.styledInput:focus {
  outline: none;
  box-shadow: 
    inset 0 2px 8px var(--shadow-light),
    0 0 0 3px rgba(1, 72, 113, 0.2),
    0 4px 12px var(--shadow-medium);
}

.styledInput:disabled {
  background: var(--neutral-100);
  color: var(--neutral-500);
  cursor: not-allowed;
}

.buttonGroup {
  display: flex;
  justify-content: space-between;
  margin-top: 15px;
}

.buttonGroup button {
  padding: 10px 20px;
  border-radius: 20px;
  cursor: pointer;
  font-weight: 600;
  background: var(--page-background);
  color: var(--neutral-700);
  border: none;
  box-shadow: 
    0 4px 15px var(--shadow-light),
    0 2px 8px var(--shadow-medium);
  flex: 1;
  margin: 0 5px;
  transition: all var(--transition-normal);
  font-size: 16px;
}

.buttonGroup button:hover {
  background: var(--neutral-200);
  transform: translateY(-2px);
  box-shadow: 
    0 8px 25px var(--shadow-light),
    0 4px 15px var(--shadow-medium);
  color: var(--neutral-800);
}

.buttonGroup button:disabled {
  background: var(--neutral-200);
  color: var(--neutral-500);
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.buttonGroup button:disabled:hover {
  background: var(--neutral-200);
  transform: none;
  box-shadow: none;
}

.saveButton {
  background: var(--primary-color);
  color: white;
}

.saveButton:hover:not(:disabled) {
  background: var(--primary-dark);
  color: white;
}

.savedNotesSection {
  margin-bottom: 1rem;
}

.activeButton {
  background: var(--primary-color);
  color: white;
  border-color: var(--primary-color);
  box-shadow: var(--box-shadow-sm);
}

/* ==== الملاحظات المحفوظة المعدلة === */
.savedNotesList {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  justify-content: flex-start;
  margin-top: 12px;
  padding: 0;
  list-style: none;
}

.savedNoteItem {
  background: var(--page-background);
  border: none;
  border-radius: 20px;
  box-shadow: 
    0 4px 15px var(--shadow-light),
    0 2px 8px var(--shadow-medium);
  padding: 12px;
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 16px;
  color: var(--neutral-700);
  position: relative;
  cursor: pointer;
  transition: all var(--transition-normal);
  user-select: none;
  margin-bottom: 8px;
  font-weight: 500;
}

.savedNoteItem:hover {
  background: var(--neutral-100);
  transform: translateY(-2px);
  box-shadow: 
    0 8px 25px var(--shadow-light),
    0 4px 15px var(--shadow-medium);
}

.savedNoteItem:active {
  transform: translateY(0);
  box-shadow: 
    0 4px 15px var(--shadow-light),
    0 2px 8px var(--shadow-medium);
  background: var(--neutral-150);
}

.savedNoteItem:last-child {
  border-bottom: none;
}

.noteHeader {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 4px;
}

.noteType {
  font-weight: 700;
  color: var(--primary-color);
  font-size: 16px;
}

.noteContent {
  color: var(--neutral-700);
  font-size: 16px;
  flex: 1;
  text-align: right;
  font-weight: 500;
}

/* زر عرض المزيد/أقل مثل الكروت - نفس تصميم CaseInfoGroups */
.expandToggle {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 12px 16px;
  margin-top: 16px;
  background: var(--neutral-100);
  border: 1px solid var(--neutral-200);
  border-radius: 12px;
  cursor: pointer;
  transition: all var(--transition-normal);
  font-size: 0.95rem;
  font-weight: 500;
  color: var(--neutral-700);
  user-select: none;
}

.expandToggle:hover {
  background: var(--neutral-200);
  border-color: var(--primary-color);
  color: var(--primary-color);
  transform: translateY(-1px);
}

/* نافذة تأكيد الحذف */
.deleteModalOverlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  backdrop-filter: blur(4px);
}

.deleteModal {
  background: var(--page-background);
  border-radius: 20px;
  padding: 24px;
  box-shadow: 
    0 25px 80px var(--shadow-medium),
    0 15px 50px var(--shadow-light);
  max-width: 400px;
  width: 90%;
  max-height: 80vh;
  overflow-y: auto;
  position: relative;
  direction: rtl;
  text-align: right;
}

.deleteModalHeader {
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 1px solid var(--neutral-200);
}

.deleteModalHeader h3 {
  margin: 0;
  font-size: 1.3rem;
  color: var(--danger-color);
  font-weight: 600;
}

.deleteModalContent {
  margin-bottom: 24px;
}

.deleteModalContent p {
  margin: 0 0 16px 0;
  font-size: 1.1rem;
  color: var(--neutral-700);
  line-height: 1.5;
}

.notePreview {
  background: var(--neutral-100);
  border: 1px solid var(--neutral-200);
  border-radius: 12px;
  padding: 12px;
  font-size: 0.95rem;
  color: var(--neutral-700);
  line-height: 1.4;
  max-height: 100px;
  overflow-y: auto;
}

.notePreview strong {
  color: var(--primary-color);
  font-weight: 600;
}

.deleteModalActions {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
}

.cancelButton,
.confirmDeleteButton {
  padding: 12px 24px;
  border-radius: 12px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  border: none;
  transition: all var(--transition-normal);
  min-width: 80px;
}

.cancelButton {
  background: var(--neutral-200);
  color: var(--neutral-700);
}

.cancelButton:hover:not(:disabled) {
  background: var(--neutral-300);
  transform: translateY(-1px);
}

.confirmDeleteButton {
  background: var(--danger-color);
  color: white;
}

.confirmDeleteButton:hover:not(:disabled) {
  background: var(--danger-dark);
  transform: translateY(-1px);
}

.cancelButton:disabled,
.confirmDeleteButton:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

/* تحسينات للشاشات الكبيرة */
@media (min-width: 769px) {
  .modalContent {
    width: 100%;
    padding: 1.5rem;
  }

  .notesHeader {
    font-size: 1.2rem;
    margin-bottom: 1rem;
  }

  .savedNoteItem {
    font-size: 16px;
    padding: 12px;
  }

  .styledInput {
    font-size: 16px;
    padding: 12px;
  }

  .styledButton {
    font-size: 16px;
    padding: 8px 16px;
  }

  .buttonGroup button {
    font-size: 16px;
    padding: 10px 20px;
  }
}

@media (max-width: 768px) {
  .modalContent {
    padding: 1rem;
    margin: 0 auto;
    width: 100%;
  }

  .notesHeader {
    font-size: 1.1rem;
    margin-bottom: 1rem;
    gap: 0.5rem;
  }

  .notesHeaderTitle {
    font-size: 1.1rem;
  }

  .savedNoteItem {
    padding: 10px;
    margin-bottom: 8px;
    font-size: 14px;
  }

  .noteType {
    font-size: 14px;
  }

  .noteContent {
    font-size: 14px;
  }

  .styledInput {
    font-size: 14px;
    padding: 10px;
  }

  .styledButton {
    font-size: 14px;
    padding: 6px 12px;
  }

  .buttonGroup button {
    font-size: 14px;
    padding: 8px 16px;
  }
}

/* أنماط الصلاحيات - حذف الملاحظات */
.deletable {
  cursor: pointer !important;
  user-select: none;
  transition: all var(--transition-normal);
}

.deletable:hover {
  background: var(--neutral-50);
  transform: translateX(-2px);
  border-left: 3px solid var(--danger-color);
}

.deletable:active {
  transform: scale(0.98);
  background: var(--neutral-100);
}

.nonDeletable {
  cursor: default !important;
  opacity: 0.8;
  position: relative;
}

.nonDeletable::after {
  content: "🔒";
  position: absolute;
  top: 8px;
  left: 8px;
  font-size: 12px;
  opacity: 0.6;
}