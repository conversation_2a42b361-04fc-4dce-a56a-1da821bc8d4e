import React from 'react';
import { FiSun, FiMoon, FiDroplet } from 'react-icons/fi';
import styles from './ThemeSelector.module.css';
import { useTheme } from './ThemeProvider';

const ThemeSelector = () => {
  const { theme: currentTheme, changeTheme } = useTheme();

  // تغيير الثيم
  const handleThemeChange = (theme) => {
    changeTheme(theme);
  };

  const themes = [
    {
      id: 'light',
      name: 'الوضع المضيء',
      icon: FiSun,
      description: 'الثيم الكلاسيكي المضيء',
      preview: 'linear-gradient(135deg, #ffffff 0%, #f8fafc 100%)'
    },
    {
      id: 'dark',
      name: 'الوضع المظلم',
      icon: FiMoon,
      description: 'ثيم مظلم مريح للعينين',
      preview: 'linear-gradient(135deg, #0f172a 0%, #1e293b 100%)'
    },
    {
      id: 'liquid',
      name: 'الوضع الشفاف',
      icon: FiDroplet,
      description: 'ثيم شفاف مع تأثيرات زجاجية',
      preview: 'linear-gradient(135deg, rgba(255,255,255,0.8) 0%, rgba(248,250,252,0.6) 100%)'
    }
  ];

  return (
    <div className={styles.themeSelector}>
      <div className={styles.header}>
        <h3 className={styles.title}>اختيار الثيم</h3>
        <p className={styles.subtitle}>اختر المظهر المفضل لديك</p>
      </div>
      
      <div className={styles.themesGrid}>
        {themes.map((theme) => {
          const IconComponent = theme.icon;
          const isActive = currentTheme === theme.id;
          
          return (
            <div
              key={theme.id}
              className={`${styles.themeCard} ${isActive ? styles.active : ''}`}
              onClick={() => handleThemeChange(theme.id)}
            >
              <div 
                className={styles.themePreview}
                style={{ background: theme.preview }}
              >
                <div className={styles.previewContent}>
                  <IconComponent className={styles.themeIcon} />
                </div>
              </div>
              
              <div className={styles.themeInfo}>
                <h4 className={styles.themeName}>{theme.name}</h4>
                <p className={styles.themeDescription}>{theme.description}</p>
              </div>
              
              {isActive && (
                <div className={styles.activeIndicator}>
                  <div className={styles.checkmark}>✓</div>
                </div>
              )}
            </div>
          );
        })}
      </div>
      
      <div className={styles.currentThemeInfo}>
        <div className={styles.currentThemeLabel}>الثيم الحالي:</div>
        <div className={styles.currentThemeName}>
          {themes.find(t => t.id === currentTheme)?.name}
        </div>
      </div>
    </div>
  );
};

export default ThemeSelector;
