import React, { useState, useEffect, useRef } from 'react';
import { FiLogOut, FiPlusCircle, FiSettings, FiUsers, FiArrowRight, FiMenu, FiSun, FiMoon, FiMonitor } from 'react-icons/fi';
import { useNavigate } from 'react-router-dom';
import { auth, db } from "../../config/firebaseConfig";
import { signOut } from "firebase/auth";
import { collection, query, where, getDocs, doc, getDoc } from "firebase/firestore";
import styles from './TopBar.module.css';
import { getActiveAccount } from '../../services/StorageService';
import permissionsService from '../../services/PermissionsService';
import { getGroups } from '../../services/GroupsService';
import { useTheme } from '../../hooks/useTheme';

const debounce = (func, wait) => {
  let timeout;
  return (...args) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  };
};

const fetchNotifications = async (userId) => {
  if (!userId) return [];
  const casesQuery = query(
    collection(db, 'cases'),
    where('userId', '==', userId)
  );
  const querySnapshot = await getDocs(casesQuery);
  const userCases = querySnapshot.docs.map(doc => ({
    id: doc.id,
    ...doc.data()
  }));

  const today = new Date();
  const tomorrow = new Date(today);
  tomorrow.setDate(today.getDate() + 1);

  const generatedNotifications = [];

  userCases.forEach((caseItem) => {
    const deferrals = caseItem.deferrals || [];
    deferrals.forEach((deferral, index) => {
      if (deferral.isDeleted || !deferral.date) return;
      const date = new Date(deferral.date);
      if (
        date.getDate() === tomorrow.getDate() &&
        date.getMonth() === tomorrow.getMonth() &&
        date.getFullYear() === tomorrow.getFullYear()
      ) {
        generatedNotifications.push({
          id: `${caseItem.id}-defer-${index}`,
          type: 'تأجيل',
          caseNumber: caseItem.fullCaseNumber || 'غير محدد',
          clientName: caseItem.clientName || 'غير محدد',
          courtLocation: caseItem.courtLocation || 'غير محددة',
          date: deferral.date,
          displayDate: new Date(deferral.date).toLocaleDateString('ar-EG', {
            day: 'numeric',
            month: 'long',
            year: 'numeric',
          }),
          reasons: deferral.reasons?.join('، ') || 'لا يوجد سبب محدد',
        });
      }
    });

    const actions = caseItem.actions || [];
    actions.forEach((action, index) => {
      if (action.isDeleted || !action.deadline) return;
      const deadline = new Date(action.deadline);
      if (
        deadline.getDate() === tomorrow.getDate() &&
        deadline.getMonth() === tomorrow.getMonth() &&
        deadline.getFullYear() === tomorrow.getFullYear()
      ) {
        generatedNotifications.push({
          id: `${caseItem.id}-action-${index}`,
          type: 'إجراء',
          caseNumber: caseItem.fullCaseNumber || 'غير محدد',
          clientName: caseItem.clientName || 'غير محدد',
          courtLocation: caseItem.courtLocation || 'غير محددة',
          date: action.deadline,
          displayDate: new Date(action.deadline).toLocaleDateString('ar-EG', {
            day: 'numeric',
            month: 'long',
            year: 'numeric',
          }),
          description: action.description || 'لا يوجد وصف',
          linkedDeferralId: action.linkedDeferralId || '',
        });
      }
    });
  });

  return generatedNotifications;
};

const TopBar = ({ currentUser }) => {
  const [isProfileMenuOpen, setProfileMenuOpen] = useState(false);
  const [activeAccount, setActiveAccountState] = useState(getActiveAccount());
  const navigate = useNavigate();
  const [userPhotoURL, setUserPhotoURL] = useState(null);
  const [userName, setUserName] = useState(null);
  const [canAddCases, setCanAddCases] = useState(false);
  const [userGroups, setUserGroups] = useState([]);
  const { theme, changeTheme } = useTheme();
  
  // تحديث الحساب النشط عند تغييره
  useEffect(() => {
    const handleStorageChange = () => {
      const newActiveAccount = getActiveAccount();
      if (newActiveAccount !== activeAccount) {
        setActiveAccountState(newActiveAccount);
      }
    };
    
    window.addEventListener('storage', handleStorageChange);
    
    // تحديث الحساب النشط كل ثانية للتأكد من مزامنته
    const intervalId = setInterval(() => {
      const currentActiveAccount = getActiveAccount();
      if (currentActiveAccount !== activeAccount) {
        setActiveAccountState(currentActiveAccount);
      }
    }, 1000);
    
    return () => {
      window.removeEventListener('storage', handleStorageChange);
      clearInterval(intervalId);
    };
  }, [activeAccount]);

  // جلب المجموعات الخاصة بالمستخدم
  useEffect(() => {
    const fetchUserGroups = async () => {
      if (currentUser?.uid && getActiveAccount() === 'online') {
        try {
          const groups = await getGroups(currentUser.uid);
          setUserGroups(groups);
          console.log('🔍 TopBar - User Groups:', groups);
        } catch (error) {
          console.error('خطأ في جلب مجموعات المستخدم:', error);
          setUserGroups([]);
        }
      } else {
        setUserGroups([]);
      }
    };

    fetchUserGroups();
  }, [currentUser, activeAccount]);

  // التحقق من صلاحيات المستخدم
  useEffect(() => {
    const checkPermissions = () => {
      if (currentUser?.uid) {
        const userRole = permissionsService.getCurrentUserRole(currentUser.uid, userGroups);
        const hasAddCasesPermission = permissionsService.hasPermission(userRole, 'addCases');

        
        // Uncomment for debugging:
        // console.log('🔍 TopBar - User Groups:', userGroups);
        // console.log('🔍 TopBar - User Role:', userRole);
        // console.log('🔍 TopBar - Can Add Cases:', hasAddCasesPermission);
        // console.log('🔍 TopBar - Can View Notifications:', hasViewNotificationsPermission);
        
        setCanAddCases(hasAddCasesPermission);
      }
    };

    checkPermissions();

    // مستمع للتغييرات في localStorage للصلاحيات
    const handlePermissionsChange = (e) => {
      if (e.key === 'user_permissions') {
        checkPermissions();
      }
    };

    window.addEventListener('storage', handlePermissionsChange);

    // فحص دوري للتأكد من مزامنة الصلاحيات
    const intervalId = setInterval(checkPermissions, 2000);

    return () => {
      window.removeEventListener('storage', handlePermissionsChange);
      clearInterval(intervalId);
    };
  }, [currentUser, userGroups]);

  // جلب صورة واسم المستخدم
  useEffect(() => {
    const fetchUserData = async () => {
      if (!currentUser?.uid) return;
      
      const activeAccount = getActiveAccount();
      console.log('جاري جلب صورة المستخدم، الحساب النشط:', activeAccount);
      
      if (activeAccount === 'local') {
        // جلب الصورة من الحساب المحلي
        const localData = localStorage.getItem('localUserData_' + currentUser.uid);
        if (localData) {
          try {
            const parsedData = JSON.parse(localData);
            if (parsedData.photoURL) {
              console.log('تم العثور على صورة في الحساب المحلي');
              setUserPhotoURL(parsedData.photoURL);
            }
            if (parsedData.name || parsedData.username) {
              setUserName(parsedData.name || parsedData.username);
            }
            if (parsedData.photoURL || parsedData.name || parsedData.username) {
              return;
            }
          } catch (e) {
            console.error('خطأ في قراءة بيانات المستخدم المحلية:', e);
          }
        }
      } else {
        // جلب الصورة من الحساب الأونلاين
        try {
          const userRef = doc(db, 'users', currentUser.uid);
          const userSnap = await getDoc(userRef);
          
          if (userSnap.exists()) {
            const userData = userSnap.data();
            if (userData.photoURL) {
              console.log('تم العثور على صورة في Firestore');
              setUserPhotoURL(userData.photoURL);
            }
            if (userData.name || userData.username) {
              setUserName(userData.name || userData.username);
            }
            if (userData.photoURL || userData.name || userData.username) {
              return;
            }
          }
        } catch (e) {
          console.error('خطأ في جلب صورة المستخدم من Firestore:', e);
        }
      }
      
      // إذا لم تكن هناك صورة في الحساب المحلي أو الأونلاين، استخدم صورة المستخدم من Firebase Auth
      if (currentUser.photoURL) {
        console.log('استخدام صورة المستخدم من Firebase Auth');
        setUserPhotoURL(currentUser.photoURL);
      } else {
        console.log('لا توجد صورة للمستخدم');
        setUserPhotoURL(null);
      }
    };
    
    fetchUserData();
  }, [currentUser, activeAccount]);



  const profileDropdownRef = useRef(null);
  const profileButtonRef = useRef(null);
  const topBarRef = useRef(null);

  useEffect(() => {
    // إضافة تأثير منع التمرير عند فتح القائمة
    if (isProfileMenuOpen) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = '';
    }
    
    return () => {
      document.body.style.overflow = '';
    };
  }, [isProfileMenuOpen]);

  const handleLogout = async () => {
    try {
      await signOut(auth);
      setProfileMenuOpen(false);
      navigate('/login');
    } catch (error) {
      console.error("خطأ أثناء تسجيل الخروج:", error);
    }
  };

  const handleAddCase = () => {
    navigate('/cases');
  };



  const toggleProfileMenu = () => {
    setProfileMenuOpen(!isProfileMenuOpen);
    
    // إضافة تأثير صوتي خفيف عند فتح القائمة
    if (!isProfileMenuOpen) {
      try {
        const audio = new Audio('/sounds/pop.mp3');
        audio.volume = 0.2;
        audio.play();
      } catch (e) {
        console.log('تعذر تشغيل الصوت');
      }
    }
  };

  // تحقق مما إذا كان المستخدم في صفحة Dashboard
  const isDashboard = window.location.pathname === '/' || window.location.pathname === '/dashboard';

  return (
    <div className={styles.headerContainer}>
      <header ref={topBarRef} className={styles.topBar}>
        <div className={styles.leftSection}>
          {/* قسم فارغ - تم نقل المحتوى */}
        </div>
        <div className={styles.rightSection}>
          {/* زر الملف الشخصي - في أقصى اليمين */}
          <div className={styles.profileWrapper}>
            <button
              ref={profileButtonRef}
              className={styles.profileButton}
              onClick={() => navigate('/profile')}
              aria-label="ملف المستخدم"
            >
              {userPhotoURL ? (
                <img
                  src={userPhotoURL}
                  alt="صورة المستخدم"
                  className={styles.userPhoto}
                />
              ) : (
                <div className={styles.userInitials}>
                  {userName ? userName.charAt(0).toUpperCase() :
                   currentUser?.displayName ? currentUser.displayName.charAt(0).toUpperCase() : 'م'}
                </div>
              )}
            </button>
          </div>

          {/* زر إضافة قضية */}
          {canAddCases && (
            <div className={styles.iconWrapper}>
              <button
                className={styles.iconButton}
                onClick={handleAddCase}
                aria-label="إضافة قضية جديدة"
              >
                <FiPlusCircle size={20} />
                <span className={styles.tooltip}>إضافة قضية</span>
              </button>
            </div>
          )}

          {/* زر العودة في التوب بار */}
          {!isDashboard && (
            <div className={styles.iconWrapper}>
              <button
                className={styles.iconButton}
                onClick={() => navigate(-1)}
                aria-label="العودة"
              >
                <FiArrowRight size={20} />
                <span className={styles.tooltip}>العودة</span>
              </button>
            </div>
          )}

          {/* زر القائمة الجديد */}
          <div className={styles.iconWrapper}>
            <button
              className={styles.iconButton}
              onClick={toggleProfileMenu}
              aria-label="القائمة"
              aria-expanded={isProfileMenuOpen}
            >
              <FiMenu size={20} />
              <span className={styles.tooltip}>القائمة</span>
            </button>
          </div>
        </div>
      </header>
      {isProfileMenuOpen && (
        <div 
          ref={profileDropdownRef} 
          className={styles.profileDropdown}
          onClick={() => setProfileMenuOpen(false)}
        >
          <div className={styles.menuContainer} onClick={(e) => e.stopPropagation()}>
            <div
              className={styles.dropdownItem}
              style={{"--item-index": 0}}
              onClick={() => {
                navigate('/deferral-templates');
                setProfileMenuOpen(false);
              }}
            >
              <span>قوالب التأجيلات</span>
              <FiSettings size={20} />
            </div>
            <div
              className={styles.dropdownItem}
              style={{"--item-index": 1}}
              onClick={() => {
                navigate('/groups');
                setProfileMenuOpen(false);
              }}
            >
              <span>إدارة المجموعات</span>
              <FiUsers size={20} />
            </div>

            <div
              className={styles.dropdownItem}
              style={{ "--item-index": 2 }}
            >
              <span>الوضع</span>
              <div className={styles.themeButtonGroup}>
                {/* الوضع الفاتح */}
                <button
                  type="button"
                  role="radio"
                  data-theme-switcher="true"
                  aria-checked={theme === 'light'}
                  className={`${styles.themeIcon} ${theme === 'light' ? styles.active : ''}`}
                  onClick={() => changeTheme('light')}
                  aria-label="الوضع الفاتح"
                >
                  <svg
                    width="24"
                    height="24"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="1.5"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  >
                    <circle cx="12" cy="12" r="5" />
                    <path d="M12 1v2M12 21v2M4.22 4.22l1.42 1.42M18.36 18.36l1.42 1.42M1 12h2M21 12h2M4.22 19.78l1.42-1.42M18.36 5.64l1.42-1.42" />
                  </svg>
                </button>

                {/* الوضع التلقائي */}
                <button
                  type="button"
                  role="radio"
                  data-theme-switcher="true"
                  aria-checked={theme === 'liquid'}
                  className={`${styles.themeIcon} ${theme === 'liquid' ? styles.active : ''}`}
                  onClick={() => changeTheme('liquid')}
                  aria-label="الوضع التلقائي"
                >
                  <svg
                    width="24"
                    height="24"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="1.5"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  >
                    <rect x="2" y="3" width="20" height="14" rx="2" ry="2" />
                    <path d="M8 21h8" />
                    <path d="M12 17v4" />
                  </svg>
                </button>

                {/* الوضع الليلي */}
                <button
                  type="button"
                  role="radio"
                  data-theme-switcher="true"
                  aria-checked={theme === 'dark'}
                  className={`${styles.themeIcon} ${theme === 'dark' ? styles.active : ''}`}
                  onClick={() => changeTheme('dark')}
                  aria-label="الوضع الليلي"
                >
                  <svg
                    width="24"
                    height="24"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="1.5"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  >
                    <path d="M21 12.79A9 9 0 1111.21 3 7 7 0 0021 12.79z" />
                  </svg>
                </button>
              </div>
            </div>

            <div
              className={styles.dropdownItem}
              style={{"--item-index": 3}}
              onClick={handleLogout}
            >
              <span>تسجيل الخروج</span>
              <FiLogOut size={20} />
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default TopBar;