// Form Validation Utilities for Case Registration

/**
 * Validates case registration form data
 * @param {Object} caseData - The case data object
 * @param {number} currentStep - Current step in the form
 * @returns {Object} validation result with isValid and errors
 */
export const validateCaseForm = (caseData, currentStep) => {
  const errors = {};
  let isValid = true;

  switch (currentStep) {
    case 1:
      // Step 1: Case Type and Number validation
      if (!caseData.caseStatus) {
        errors.caseStatus = 'يجب اختيار نوع القضية';
        isValid = false;
      }

      if (caseData.caseStatus === 'دعوى قضائية') {
        if (!caseData.caseNumber || !caseData.caseNumber.trim()) {
          errors.caseNumber = 'يجب إدخال رقم القضية';
          isValid = false;
        } else if (!/^\d+$/.test(caseData.caseNumber.trim())) {
          errors.caseNumber = 'رقم القضية يجب أن يحتوي على أرقام فقط';
          isValid = false;
        }

        if (!caseData.caseYear || !/^\d{4}$/.test(caseData.caseYear)) {
          errors.caseYear = 'السنة يجب أن تكون مكونة من 4 أرقام';
          isValid = false;
        }
      }

      if (caseData.caseStatus === 'محضر') {
        if (!caseData.reportNumber || !caseData.reportNumber.trim()) {
          errors.reportNumber = 'يجب إدخال رقم المحضر';
          isValid = false;
        } else if (!/^\d+$/.test(caseData.reportNumber.trim())) {
          errors.reportNumber = 'رقم المحضر يجب أن يحتوي على أرقام فقط';
          isValid = false;
        }

        if (!caseData.caseYear || !/^\d{4}$/.test(caseData.caseYear)) {
          errors.caseYear = 'السنة يجب أن تكون مكونة من 4 أرقام';
          isValid = false;
        }
      }
      break;

    case 2:
      // Step 2: Parties validation
      if (!caseData.clientName || !caseData.clientName.trim()) {
        errors.clientName = 'يجب إدخال اسم الموكل';
        isValid = false;
      } else if (caseData.clientName.trim().length < 2) {
        errors.clientName = 'اسم الموكل يجب أن يكون على الأقل حرفين';
        isValid = false;
      } else if (!/^[\u0600-\u06FF\s\u0020-\u007E]+$/.test(caseData.clientName.trim())) {
        errors.clientName = 'اسم الموكل يجب أن يحتوي على أحرف عربية أو انجليزية فقط';
        isValid = false;
      }

      // Optional opponent name validation
      if (caseData.opponentName && caseData.opponentName.trim()) {
        if (caseData.opponentName.trim().length < 2) {
          errors.opponentName = 'اسم الخصم يجب أن يكون على الأقل حرفين';
          isValid = false;
        } else if (!/^[\u0600-\u06FF\s\u0020-\u007E]+$/.test(caseData.opponentName.trim())) {
          errors.opponentName = 'اسم الخصم يجب أن يحتوي على أحرف عربية أو انجليزية فقط';
          isValid = false;
        }
      }
      break;

    case 3:
      // Step 3: Legal Details validation
      if (caseData.caseStatus === 'دعوى قضائية') {
        if (!caseData.circleNumber || !caseData.circleNumber.trim()) {
          errors.circleNumber = 'يجب إدخال رقم الدائرة';
          isValid = false;
        } else if (!/^\d+$/.test(caseData.circleNumber.trim())) {
          errors.circleNumber = 'رقم الدائرة يجب أن يحتوي على أرقام فقط';
          isValid = false;
        }

        if (!caseData.courtLocation) {
          errors.courtLocation = 'يجب اختيار المحكمة';
          isValid = false;
        }

        if (!caseData.caseDegree) {
          errors.caseDegree = 'يجب اختيار درجة الدعوى';
          isValid = false;
        }

        if (!caseData.caseCategory) {
          errors.caseCategory = 'يجب اختيار نوع الدعوى';
          isValid = false;
        }

        if (!caseData.caseDate) {
          errors.caseDate = 'يجب إدخال تاريخ رفع الدعوى';
          isValid = false;
        } else {
          const caseDate = new Date(caseData.caseDate);
          const today = new Date();
          const oneYearAgo = new Date();
          oneYearAgo.setFullYear(today.getFullYear() - 10);
          
          if (caseDate > today) {
            errors.caseDate = 'تاريخ رفع الدعوى لا يمكن أن يكون في المستقبل';
            isValid = false;
          } else if (caseDate < oneYearAgo) {
            errors.caseDate = 'تاريخ رفع الدعوى قديم جداً (أكثر من 10 سنوات)';
            isValid = false;
          }
        }
      }

      if (caseData.caseStatus === 'محضر') {
        if (!caseData.reportLocation || !caseData.reportLocation.trim()) {
          errors.reportLocation = 'يجب إدخال مكان الجهة المختصة';
          isValid = false;
        } else if (caseData.reportLocation.trim().length < 3) {
          errors.reportLocation = 'مكان الجهة المختصة يجب أن يكون على الأقل 3 أحرف';
          isValid = false;
        }

        if (!caseData.caseDate) {
          errors.caseDate = 'يجب إدخال تاريخ المحضر';
          isValid = false;
        } else {
          const caseDate = new Date(caseData.caseDate);
          const today = new Date();
          const oneYearAgo = new Date();
          oneYearAgo.setFullYear(today.getFullYear() - 5);
          
          if (caseDate > today) {
            errors.caseDate = 'تاريخ المحضر لا يمكن أن يكون في المستقبل';
            isValid = false;
          } else if (caseDate < oneYearAgo) {
            errors.caseDate = 'تاريخ المحضر قديم جداً (أكثر من 5 سنوات)';
            isValid = false;
          }
        }
      }

      if (caseData.caseStatus === 'قيد النظر') {
        if (!caseData.reportLocation || !caseData.reportLocation.trim()) {
          errors.reportLocation = 'يجب إدخال مكان الجهة المختصة';
          isValid = false;
        } else if (caseData.reportLocation.trim().length < 3) {
          errors.reportLocation = 'مكان الجهة المختصة يجب أن يكون على الأقل 3 أحرف';
          isValid = false;
        }
      }
      break;

    case 4:
      // Step 4: Description validation
      if (!caseData.caseDescription || !caseData.caseDescription.trim()) {
        errors.caseDescription = 'يجب إدخال وصف القضية';
        isValid = false;
      } else if (caseData.caseDescription.trim().length < 10) {
        errors.caseDescription = 'وصف القضية يجب أن يكون على الأقل 10 أحرف';
        isValid = false;
      } else if (caseData.caseDescription.trim().length > 1000) {
        errors.caseDescription = 'وصف القضية لا يمكن أن يتجاوز 1000 حرف';
        isValid = false;
      }
      break;

    default:
      isValid = false;
      break;
  }

  return { isValid, errors };
};

/**
 * Validates a specific field
 * @param {string} fieldName - Name of the field to validate
 * @param {any} value - Value to validate
 * @param {Object} caseData - Complete case data for context
 * @returns {Object} validation result with isValid and error message
 */
export const validateField = (fieldName, value, caseData = {}) => {
  let isValid = true;
  let error = '';

  switch (fieldName) {
    case 'clientName':
      if (!value || !value.trim()) {
        error = 'يجب إدخال اسم الموكل';
        isValid = false;
      } else if (value.trim().length < 2) {
        error = 'اسم الموكل يجب أن يكون على الأقل حرفين';
        isValid = false;
      } else if (!/^[\u0600-\u06FF\s\u0020-\u007E]+$/.test(value.trim())) {
        error = 'اسم الموكل يجب أن يحتوي على أحرف عربية أو انجليزية فقط';
        isValid = false;
      }
      break;

    case 'opponentName':
      if (value && value.trim()) {
        if (value.trim().length < 2) {
          error = 'اسم الخصم يجب أن يكون على الأقل حرفين';
          isValid = false;
        } else if (!/^[\u0600-\u06FF\s\u0020-\u007E]+$/.test(value.trim())) {
          error = 'اسم الخصم يجب أن يحتوي على أحرف عربية أو انجليزية فقط';
          isValid = false;
        }
      }
      break;

    case 'caseNumber':
      if (!value || !value.trim()) {
        error = 'يجب إدخال رقم القضية';
        isValid = false;
      } else if (!/^\d+$/.test(value.trim())) {
        error = 'رقم القضية يجب أن يحتوي على أرقام فقط';
        isValid = false;
      }
      break;

    case 'reportNumber':
      if (!value || !value.trim()) {
        error = 'يجب إدخال رقم المحضر';
        isValid = false;
      } else if (!/^\d+$/.test(value.trim())) {
        error = 'رقم المحضر يجب أن يحتوي على أرقام فقط';
        isValid = false;
      }
      break;

    case 'caseYear':
      if (!value || !/^\d{4}$/.test(value)) {
        error = 'السنة يجب أن تكون مكونة من 4 أرقام';
        isValid = false;
      } else {
        const year = parseInt(value);
        const currentYear = new Date().getFullYear();
        if (year < 1900 || year > currentYear + 1) {
          error = `السنة يجب أن تكون بين 1900 و ${currentYear + 1}`;
          isValid = false;
        }
      }
      break;

    case 'circleNumber':
      if (!value || !value.trim()) {
        error = 'يجب إدخال رقم الدائرة';
        isValid = false;
      } else if (!/^\d+$/.test(value.trim())) {
        error = 'رقم الدائرة يجب أن يحتوي على أرقام فقط';
        isValid = false;
      }
      break;

    case 'reportLocation':
      if (!value || !value.trim()) {
        error = 'يجب إدخال مكان الجهة المختصة';
        isValid = false;
      } else if (value.trim().length < 3) {
        error = 'مكان الجهة المختصة يجب أن يكون على الأقل 3 أحرف';
        isValid = false;
      }
      break;

    case 'caseDescription':
      if (!value || !value.trim()) {
        error = 'يجب إدخال وصف القضية';
        isValid = false;
      } else if (value.trim().length < 10) {
        error = 'وصف القضية يجب أن يكون على الأقل 10 أحرف';
        isValid = false;
      } else if (value.trim().length > 1000) {
        error = 'وصف القضية لا يمكن أن يتجاوز 1000 حرف';
        isValid = false;
      }
      break;

    case 'caseDate':
      if (!value) {
        error = 'يجب إدخال التاريخ';
        isValid = false;
      } else {
        const selectedDate = new Date(value);
        const today = new Date();
        const maxPastYears = caseData.caseStatus === 'محضر' ? 5 : 10;
        const pastLimit = new Date();
        pastLimit.setFullYear(today.getFullYear() - maxPastYears);
        
        if (selectedDate > today) {
          error = 'التاريخ لا يمكن أن يكون في المستقبل';
          isValid = false;
        } else if (selectedDate < pastLimit) {
          error = `التاريخ قديم جداً (أكثر من ${maxPastYears} سنوات)`;
          isValid = false;
        }
      }
      break;

    case 'courtLocation':
      if (!value) {
        error = 'يجب اختيار المحكمة';
        isValid = false;
      }
      break;

    case 'caseDegree':
      if (!value) {
        error = 'يجب اختيار درجة الدعوى';
        isValid = false;
      }
      break;

    case 'caseCategory':
      if (!value) {
        error = 'يجب اختيار نوع الدعوى';
        isValid = false;
      }
      break;

    default:
      break;
  }

  return { isValid, error };
};

/**
 * Gets progress percentage for the form
 * @param {number} currentStep - Current step (1-4)
 * @param {Object} caseData - Case data object
 * @returns {number} Progress percentage (0-100)
 */
export const getFormProgress = (currentStep, caseData) => {
  const baseProgress = ((currentStep - 1) / 4) * 100;
  
  // Add partial progress based on filled fields in current step
  let stepProgress = 0;
  const fieldsPerStep = {
    1: ['caseStatus', 'caseNumber', 'reportNumber', 'caseYear'],
    2: ['clientName', 'opponentName'],
    3: ['circleNumber', 'courtLocation', 'caseDegree', 'caseCategory', 'caseDate', 'reportLocation'],
    4: ['caseDescription']
  };

  const currentStepFields = fieldsPerStep[currentStep] || [];
  const filledFields = currentStepFields.filter(field => {
    const value = caseData[field];
    return value && value.toString().trim() !== '';
  });

  stepProgress = (filledFields.length / currentStepFields.length) * 25;
  
  return Math.min(100, baseProgress + stepProgress);
};

/**
 * Formats error messages for display
 * @param {Object} errors - Error object from validation
 * @returns {string} Formatted error message
 */
export const formatErrorMessage = (errors) => {
  const errorMessages = Object.values(errors).filter(msg => msg);
  if (errorMessages.length === 0) return '';
  
  if (errorMessages.length === 1) {
    return errorMessages[0];
  }
  
  return `يوجد ${errorMessages.length} أخطاء في البيانات المدخلة`;
};

/**
 * Checks if the form is ready for submission
 * @param {Object} caseData - Case data object
 * @returns {Object} readiness status and missing fields
 */
export const checkFormReadiness = (caseData) => {
  const validation1 = validateCaseForm(caseData, 1);
  const validation2 = validateCaseForm(caseData, 2);
  const validation3 = validateCaseForm(caseData, 3);
  const validation4 = validateCaseForm(caseData, 4);

  const allErrors = {
    ...validation1.errors,
    ...validation2.errors,
    ...validation3.errors,
    ...validation4.errors
  };

  const isReady = validation1.isValid && validation2.isValid && validation3.isValid && validation4.isValid;
  
  return {
    isReady,
    errors: allErrors,
    missingFields: Object.keys(allErrors)
  };
};