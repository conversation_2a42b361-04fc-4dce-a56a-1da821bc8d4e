import { useState, useEffect, useMemo } from 'react';
import { FaClock, FaTimes } from 'react-icons/fa';
import styles from "./AddDeferralAction.module.css";

// دالة مساعدة للتحقق من صحة موعد الإجراء
function validateDeadline(deadline, linkedDeferralId, linkedActionId, currentLinkType, deferrals, actions, setError) {
  const actionDate = new Date(deadline);
  if (currentLinkType === 'custom') {
    setError(null);
    return true;
  }
  if (linkedDeferralId) {
    const deferral = deferrals.find(d => d.id === linkedDeferralId);
    if (deferral && deferral.date) {
      const expectedDate = new Date(deferral.date);
      expectedDate.setDate(expectedDate.getDate() - 1);
      if (actionDate.toDateString() !== expectedDate.toDateString()) {
        setError('موعد الإجراء يجب أن يكون قبل تاريخ التأجيل بيوم واحد بالضبط.');
        return false;
      }
    }
  }
  if (linkedActionId) {
    const linkedAction = actions.find(a => a.id === linkedActionId);
    if (linkedAction && linkedAction.deadline) {
      const linkedActionDate = new Date(linkedAction.deadline);
      if (actionDate <= linkedActionDate) {
        setError('موعد الإجراء يجب أن يكون بعد تاريخ الإجراء المرتبط.');
        return false;
      }
      if (linkedAction.linkedDeferralId) {
        const deferral = deferrals.find(d => d.id === linkedAction.linkedDeferralId);
        if (deferral && deferral.date) {
          const deferralDate = new Date(deferral.date);
          if (actionDate >= deferralDate) {
            setError('موعد الإجراء يجب أن يكون قبل تاريخ التأجيل المرتبط بالإجراء المتسلسل بيوم واحد.');
            return false;
          }
        }
      }
    }
  }
  setError(null);
  return true;
}

const AddAction = ({  currentUser, caseItem, deferrals, actions, setActions, history, setHistory,
  onSave, onClose, templateActions = [],
}) => {
  const [newAction, setNewAction] = useState('');
  const [actionDeadline, setActionDeadline] = useState('');
  const [reminderType, setReminderType] = useState('');
  const [linkedDeferralId, setLinkedDeferralId] = useState('');
  const [linkedActionId, setLinkedActionId] = useState('');
  const [linkType, setLinkType] = useState('custom');
  const [error, setError] = useState(null);

  // تحسين: استخدام useMemo مع dependency محسنة ومعالجة حالات null/undefined
  const availableDeferrals = useMemo(() => {
    if (!deferrals || !caseItem?.id) return [];
    
    return deferrals
      .map((deferral, index) => ({
        id: deferral.id || `${caseItem.id}-defer-${index}`,
        content: deferral.content || (deferral.reasons ? deferral.reasons.join('، ') : `تأجيل ${index + 1}`),
        isDeleted: deferral.isDeleted || false,
        date: deferral.date,
      }))
      .filter((deferral) => !deferral.isDeleted);
  }, [deferrals, caseItem?.id]);

  const availableActions = useMemo(() => {
    if (!actions) return [];
    
    return actions
      .map((action) => ({
        id: action.id,
        description: action.description || `إجراء بدون وصف`,
        isDeleted: action.isDeleted || false,
        deadline: action.deadline,
        linkedDeferralId: action.linkedDeferralId,
      }))
      .filter((action) => !action.isDeleted);
  }, [actions]);

  useEffect(() => {
    if (linkType === 'deferral' && linkedDeferralId) {
      const deferral = deferrals.find(d => d.id === linkedDeferralId);
      if (deferral && deferral.date) {
        const deferralDate = new Date(deferral.date);
        deferralDate.setDate(deferralDate.getDate() - 1);
        setActionDeadline(deferralDate.toISOString().split('T')[0]);
      }
    } else if (linkType === 'action' && linkedActionId) {
      const linkedAction = actions.find(a => a.id === linkedActionId);
      if (linkedAction && linkedAction.deadline) {
        const linkedActionDate = new Date(linkedAction.deadline);
        linkedActionDate.setDate(linkedActionDate.getDate() + 1);
        setActionDeadline(linkedActionDate.toISOString().split('T')[0]);
        if (linkedAction.linkedDeferralId) {
          const deferral = deferrals.find(d => d.id === linkedAction.linkedDeferralId);
          if (deferral && deferral.date) {
            const deferralDate = new Date(deferral.date);
            deferralDate.setDate(deferralDate.getDate() - 1);
            if (linkedActionDate >= deferralDate) {
              setError('موعد الإجراء يجب أن يكون قبل تاريخ التأجيل المرتبط بالإجراء المتسلسل بيوم واحد.');
              setActionDeadline('');
            }
          }
        }
      }
    } else if (linkType === 'custom') {
      setActionDeadline(new Date().toISOString().split('T')[0]);
    } else if (!linkType) {
      setActionDeadline('');
    }
  }, [linkType, linkedDeferralId, linkedActionId, deferrals, actions]);

  return (
    <div className={styles.addActionForm}>
      {error && <div className={styles.errorAlert}>{error}</div>}
      <div className={styles.actionField}>
        <label>نوع ربط الإجراء:</label>
        <select
          value={linkType}
          onChange={(e) => {
            setLinkType(e.target.value);
            setLinkedDeferralId('');
            setLinkedActionId('');
            setError(null);
          }}
          className={styles.actionInput}
        >
          <option value="custom">تاريخ مخصص</option>
          <option value="deferral">ربط بتأجيل</option>
          <option value="action">ربط بإجراء</option>
        </select>
        {linkType === 'deferral' && (
          <>
            <div className={styles.actionField}>
              <label>اختر التأجيل:</label>
              <select
                value={linkedDeferralId}
                onChange={(e) => setLinkedDeferralId(e.target.value)}
                className={styles.actionInput}
              >
                <option value="">ا��تر تأجيل</option>
                {availableDeferrals.length > 0 ? (
                  availableDeferrals.map((deferral) => (
                    <option key={deferral.id} value={deferral.id}>
                      {deferral.content}
                    </option>
                  ))
                ) : (
                  <option disabled>لا توجد تأجيلات متاحة</option>
                )}
              </select>
            </div>
            {linkedDeferralId && (
              <div className={styles.actionField}>
                <label>موعد الإجراء (محدد تلقائيًا):</label>
                <input
                  type="date"
                  value={actionDeadline}
                  className={styles.actionInput}
                  disabled
                />
              </div>
            )}
          </>
        )}
        {linkType === 'action' && (
          <>
            <div className={styles.actionField}>
              <label>اختر الإجراء:</label>
              <select
                value={linkedActionId}
                onChange={(e) => setLinkedActionId(e.target.value)}
                className={styles.actionInput}
              >
                <option value="">اختر إجراء</option>
                {availableActions.length > 0 ? (
                  availableActions.map((action) => (
                    <option key={action.id} value={action.id}>
                      {action.description}
                    </option>
                  ))
                ) : (
                  <option disabled>لا توجد إجراءات متاحة</option>
                )}
              </select>
            </div>
            {linkedActionId && (
              <div className={styles.actionField}>
                <label>موعد الإجراء (محدد تلقائيًا):</label>
                <input
                  type="date"
                  value={actionDeadline}
                  className={styles.actionInput}
                  disabled
                />
              </div>
            )}
          </>
        )}
        {linkType === 'custom' && (
          <div className={styles.actionField}>
            <label>تاريخ الإجراء المخصص:</label>
            <input
              type="date"
              value={actionDeadline}
              onChange={(e) => {
                const newDeadline = e.target.value;
                const currentLinkedDeferralId = linkType === 'custom' ? '' : linkedDeferralId;
                const currentLinkedActionId = linkType === 'custom' ? '' : linkedActionId;
                if (validateDeadline(newDeadline, currentLinkedDeferralId, currentLinkedActionId, linkType, deferrals, actions, setError)) {
                  setActionDeadline(newDeadline);
                }
              }}
              className={styles.actionInput}
              min={new Date().toISOString().split('T')[0]}
            />
          </div>
        )}
      </div>
      <div className={styles.actionField}>
        <label>وصف الإجراء:</label>
        <input
          type="text"
          value={newAction}
          onChange={(e) => setNewAction(e.target.value)}
          placeholder="أدخل وصف الإجراء"
          className={styles.actionInput}
        />
      </div>
      <div className={styles.actionField}>
        <label>توقيت الإشعار:</label>
        <div className={styles.reminderTags}>
          <div 
            className={`${styles.reminderTag} ${reminderType === 'daily' ? styles.selectedTag : ''}`}
            onClick={() => setReminderType(reminderType === 'daily' ? '' : 'daily')}
          >
            <span className={styles.tagIcon}>🔔</span>
            <span className={styles.tagText}>إشعار يومي</span>
          </div>
          <div 
            className={`${styles.reminderTag} ${reminderType === 'dayBefore' ? styles.selectedTag : ''}`}
            onClick={() => setReminderType(reminderType === 'dayBefore' ? '' : 'dayBefore')}
          >
            <span className={styles.tagIcon}>⏰</span>
            <span className={styles.tagText}>إشعار قبلها بيوم</span>
          </div>
        </div>
      </div>
      <div className={styles.actionFormButtons}>
        <button
          onClick={() => {
            if (!newAction || !actionDeadline || !reminderType) {
              setError('يرجى ملء جميع الحقول المطلوبة.');
              return;
            }
            const currentLinkedDeferralId = linkType === 'custom' ? '' : linkedDeferralId;
            const currentLinkedActionId = linkType === 'custom' ? '' : linkedActionId;
            if (validateDeadline(actionDeadline, currentLinkedDeferralId, currentLinkedActionId, linkType, deferrals, actions, setError)) {
              onSave(newAction, actionDeadline, linkType, linkedDeferralId, linkedActionId, reminderType, setError);
            }
          }}
          className={styles.addActionButton}
          disabled={!!error}
        >
          <FaClock className={styles.buttonIcon} />
          <span>إضافة تنبيه بإجراء</span>
        </button>
        <button onClick={() => { onClose(); setError(null); }} className={styles.cancelButton}>
          <FaTimes className={styles.buttonIcon} />
          <span>إلغاء</span>
        </button>
      </div>
    </div>
  );
};

export default AddAction;
