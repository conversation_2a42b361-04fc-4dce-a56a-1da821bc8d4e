import{u as Z,r as l,j as s,c as z,d as I,q as S,w as $,A as y,B as P,C as Q,g as W}from"./index-Bd3HN_hN.js";import{p as X,q as U,r as ee,s as se,t as ae,u as te,v as ne,w as re,x as oe,j as ce}from"./index-DQWGoQ3q.js";import{T as le}from"./TopBar-DgdxUhb0.js";import ie from"./TimelinePage-uDsF-3Vt.js";import"./iconBase-BmtohqY9.js";const de="_pageContainer_7uqff_45",ue="_contentContainer_7uqff_87",fe="_header_7uqff_105",_e="_pageTitle_7uqff_121",he="_controls_7uqff_197",pe="_searchBox_7uqff_231",xe="_searchIcon_7uqff_245",je="_searchInput_7uqff_263",ve="_viewToggle_7uqff_297",Ce="_viewButton_7uqff_307",Ne="_active_7uqff_349",ge="_casesGrid_7uqff_361",we="_caseCard_7uqff_377",me="_cardHeader_7uqff_427",Te="_caseNumber_7uqff_445",qe="_caseIcon_7uqff_463",Be="_reportDate_7uqff_473",be="_cardBody_7uqff_489",De="_infoGroup_7uqff_501",Re="_infoIcon_7uqff_515",ke="_infoLabel_7uqff_525",Le="_infoValue_7uqff_535",Ae="_previewContainer_7uqff_543",Se="_reportPreview_7uqff_621",$e="_actionReportPreview_7uqff_621",ye="_sectionTitle_7uqff_635",Pe="_reportText_7uqff_651",Fe="_actionText_7uqff_651",Ge="_actionIcon_7uqff_677",Ee="_linkedInfo_7uqff_687",He="_linkIcon_7uqff_705",Ve="_noCases_7uqff_715",Me="_cardBorderPending_7uqff_943",Oe="_cardBorderReport_7uqff_951",Je="_cardBorderLawsuit_7uqff_959",Ke="_cardBorderDefault_7uqff_967",Ye="_casesTableContainer_7uqff_989",Ze="_casesTable_7uqff_989",ze="_dateText_7uqff_1141",a={pageContainer:de,contentContainer:ue,header:fe,pageTitle:_e,controls:he,searchBox:pe,searchIcon:xe,searchInput:je,viewToggle:ve,viewButton:Ce,active:Ne,casesGrid:ge,caseCard:we,cardHeader:me,caseNumber:Te,caseIcon:qe,reportDate:Be,cardBody:be,infoGroup:De,infoIcon:Re,infoLabel:ke,infoValue:Le,previewContainer:Ae,reportPreview:Se,actionReportPreview:$e,sectionTitle:ye,reportText:Pe,actionText:Fe,actionIcon:Ge,linkedInfo:Ee,linkIcon:He,noCases:Ve,cardBorderPending:Me,cardBorderReport:Oe,cardBorderLawsuit:Je,cardBorderDefault:Ke,casesTableContainer:Ye,casesTable:Ze,dateText:ze},es=({currentUser:p})=>{const q=Z(),[C,F]=l.useState(""),[u,N]=l.useState("cards"),[G,g]=l.useState([]),[B,E]=l.useState(null),[f,w]=l.useState(!1),[b,H]=l.useState(!0),[D,m]=l.useState(null),T=10,R="cases_list",k=5*60*1e3,x=async(e=!1,c=!1)=>{if(!p){m("المستخدم غير متوفر. يرجى تسجيل الدخول.");return}w(!0),m(null);try{if(!e&&!c){const o=localStorage.getItem(R);if(o){const{data:d,timestamp:v}=JSON.parse(o);if(Date.now()-v<k){g(d),w(!1);return}}}const t=z(I,"cases");let n=S(t,$("userId","==",p.uid),y("updatedAt","desc"),P(T));e&&B&&(n=S(t,$("userId","==",p.uid),y("updatedAt","desc"),Q(B),P(T)));const r=await W(n),i=r.docs.map(o=>({id:o.id,...o.data()}));E(r.docs[r.docs.length-1]),H(r.docs.length===T),e?g(o=>[...o,...i]):(g(i),localStorage.setItem(R,JSON.stringify({data:i,timestamp:Date.now()})))}catch(t){console.error("خطأ في جلب القضايا:",t),m("خطأ في جلب القضايا: "+t.message)}finally{w(!1)}};l.useEffect(()=>{x(!1,!0);const e=setInterval(()=>x(),k);return()=>clearInterval(e)},[p]);const V=e=>{var n;if(!e||e.length===0)return{date:null,report:null};const c=e.filter(r=>!r.isDeleted),t=c[c.length-1];return t?{date:t.date?new Date(t.date):null,report:t.content||`${t.date} - ${(n=t.reasons)==null?void 0:n.join("، ")}`||"لا توجد تفاصيل"}:{date:null,report:null}},M=(e,c)=>{var i;if(!e||e.length===0)return{date:null,action:null,linkedReport:null};const t=e.filter(o=>!o.isDeleted),n=t[t.length-1];if(!n)return{date:null,action:null,linkedReport:null};let r=null;if(n.linkedDeferralId&&c){const o=c.filter(v=>!v.isDeleted),d=o.findIndex((v,Y)=>`${n.id}-defer-${Y}`===n.linkedDeferralId);d!==-1&&(r=o[d].content||`${o[d].date} - ${(i=o[d].reasons)==null?void 0:i.join("، ")}`)}return{date:n.deadline?new Date(n.deadline):null,action:n.description||"لا توجد تفاصيل",linkedReport:r||null}},_=C.toLowerCase(),j=G.reduce((e,c)=>{try{const t=V(c.deferrals),n=M(c.actions,c.deferrals),r={...c,lastReport:t.report,lastReportDate:t.date,lastAction:n.action,lastActionDate:n.date,linkedReport:n.linkedReport};return(r.fullCaseNumber||"").toLowerCase().includes(_)||(r.clientName||"").toLowerCase().includes(_)||(r.courtLocation||"").toLowerCase().includes(_)||(r.lastReport||"").toLowerCase().includes(_)||(r.lastAction||"").toLowerCase().includes(_)?[...e,r]:e}catch(t){return console.error(`خطأ في معالجة القضية ${c.id}:`,t),e}},[]),O=()=>q("/dashboard"),L=e=>q(`/case-details/${e.id}`),h=e=>e?new Date(e).toLocaleDateString("ar-EG",{month:"numeric",day:"numeric"}):"—",A=e=>{switch(e.caseStatus||e.case_status){case"دعوى قضائية":return`قضية رقم: ${e.fullCaseNumber||""}`;case"محضر":return`محضر رقم: ${e.fullCaseNumber||""}`;case"قيد النظر":return"ملف قيد النظر";default:return`قضية رقم: ${e.fullCaseNumber||""}`}},J=e=>{switch(e){case"دعوى قضائية":return a.cardBorderLawsuit;case"محضر":return a.cardBorderReport;case"قيد النظر":return a.cardBorderPending;default:return a.cardBorderDefault}},K=()=>D?s.jsx("div",{className:a.noCases,style:{color:"red"},children:D}):f?s.jsx("div",{className:a.noCases,children:"جاري التحميل..."}):j.length===0?s.jsx("div",{className:a.noCases,children:C?"لا توجد نتائج مطابقة للبحث":"لا توجد قضايا مسجلة"}):u==="table"?s.jsxs("div",{className:a.casesTableContainer,children:[s.jsxs("table",{className:a.casesTable,children:[s.jsx("thead",{children:s.jsxs("tr",{children:[s.jsx("th",{children:"اسم الموكل"}),s.jsx("th",{children:"مكان المحكمة"}),s.jsx("th",{children:"آخر ما تم"})]})}),s.jsx("tbody",{children:j.map(e=>s.jsxs("tr",{onClick:()=>L(e),className:(e.caseStatus||e.case_status)==="قيد النظر"?a.rowPending:(e.caseStatus||e.case_status)==="دعوى قضائية"?a.rowLawsuit:a.rowDefault,children:[s.jsx("td",{children:e.clientName||"غير محدد"}),s.jsx("td",{children:e.courtLocation||"غير محدد"}),s.jsx("td",{children:e.lastAction?s.jsxs(s.Fragment,{children:[e.lastAction.substring(0,20)+(e.lastAction.length>20?"...":""),s.jsx("br",{}),s.jsx("span",{className:a.dateText,children:h(e.lastActionDate)})]}):e.lastReport?s.jsxs(s.Fragment,{children:[e.lastReport.substring(0,20)+(e.lastReport.length>20?"...":""),s.jsx("br",{}),s.jsx("span",{className:a.dateText,children:h(e.lastReportDate)})]}):"—"})]},e.id))})]}),b&&s.jsx("button",{onClick:()=>x(!0),className:a.loadMoreButton,disabled:f,children:f?"جاري التحميل...":"تحميل المزيد"})]}):u==="cards"?s.jsxs("div",{className:a.casesGrid,children:[j.map(e=>s.jsxs("div",{className:`${a.caseCard} ${J(e.caseStatus||e.case_status)}`,onClick:()=>L(e),children:[s.jsxs("div",{className:a.cardHeader,children:[s.jsxs("h3",{className:a.caseNumber,children:[s.jsx(ae,{className:a.caseIcon}),A(e)]}),s.jsxs("span",{className:a.reportDate,children:[s.jsx(te,{})," ",h(e.lastActionDate||e.lastReportDate)]})]}),s.jsxs("div",{className:a.cardBody,children:[s.jsxs("div",{className:a.infoGroup,children:[s.jsx(ne,{className:a.infoIcon}),s.jsxs("div",{children:[s.jsx("span",{className:a.infoLabel,children:"الموكل:"}),s.jsx("span",{className:a.infoValue,children:e.clientName||"غير محدد"})]})]}),s.jsxs("div",{className:a.infoGroup,children:[s.jsx(re,{className:a.infoIcon}),s.jsxs("div",{children:[s.jsx("span",{className:a.infoLabel,children:(e.caseStatus||e.case_status)==="دعوى قضائية"?"المحكمة:":"مكان الجهة المختصة:"}),s.jsx("span",{className:a.infoValue,children:e.courtLocation||"غير محددة"})]})]}),(e.lastAction||e.lastReport)&&s.jsxs("div",{className:a.previewContainer,children:[e.lastAction&&s.jsxs("div",{className:a.actionReportPreview,children:[s.jsx("h4",{className:a.sectionTitle,children:"آخر إجراء:"}),s.jsxs("p",{className:a.actionText,children:[s.jsx(oe,{className:a.actionIcon}),e.lastAction," - ",h(e.lastActionDate),e.linkedReport&&s.jsxs("span",{className:a.linkedInfo,children:[s.jsx(ce,{className:a.linkIcon}),"مرتبط بتقرير: ",e.linkedReport]})]})]}),!e.lastAction&&e.lastReport&&s.jsxs("div",{className:a.reportPreview,children:[s.jsx("h4",{className:a.sectionTitle,children:"آخر تأجيل:"}),s.jsxs("p",{className:a.reportText,children:[e.lastReport," - ",h(e.lastReportDate)]})]})]})]})]},e.id)),b&&s.jsx("button",{onClick:()=>x(!0),className:a.loadMoreButton,disabled:f,children:f?"جاري التحميل...":"تحميل المزيد"})]}):s.jsx("div",{className:a.casesContainer,children:s.jsx(ie,{casesList:j,getCaseTitle:A})});return s.jsxs("div",{className:a.pageContainer,children:[s.jsx(le,{title:"نظام إدارة التقارير القانونية",showBackButton:!0,onBack:O}),s.jsxs("div",{className:a.contentContainer,children:[s.jsxs("div",{className:a.header,children:[s.jsx("h1",{className:a.pageTitle,children:"نظرة عامة على القضايا"}),s.jsxs("div",{className:a.controls,children:[s.jsxs("div",{className:a.searchBox,children:[s.jsx(X,{className:a.searchIcon}),s.jsx("input",{type:"text",placeholder:"ابحث عن قضية أو موكل...",value:C,onChange:e=>F(e.target.value),className:a.searchInput})]}),s.jsxs("div",{className:a.viewToggle,children:[s.jsxs("button",{className:`${a.viewButton} ${u==="cards"?a.active:""}`,onClick:()=>N("cards"),children:[s.jsx(U,{})," عرض البطاقات"]}),s.jsxs("button",{className:`${a.viewButton} ${u==="radar"?a.active:""}`,onClick:()=>N("radar"),children:[s.jsx(ee,{})," عرض الرادار"]}),s.jsxs("button",{className:`${a.viewButton} ${u==="table"?a.active:""}`,onClick:()=>N("table"),children:[s.jsx(se,{})," عرض الجدول"]})]})]})]}),s.jsx("div",{className:a.casesContainer,children:K()})]})]})};export{es as default};
