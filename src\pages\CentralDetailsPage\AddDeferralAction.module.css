/* أنماط واجهة إضافة التأجيلات والإجراءات فقط */
@import '../../styles/variables.css';

.addReportForm {
  background: var(--page-background);
  border-radius: 20px;
  padding: 28px;
  box-shadow: 
    0 20px 60px var(--shadow-medium),
    0 10px 30px var(--shadow-light);
  border: none;
  margin: 20px auto;
  width: 100%;
  max-width: 900px;
  transition: all var(--transition-normal);
  position: relative;
  overflow: hidden;
}

.addReportForm:hover {
  box-shadow: 
    0 25px 70px var(--shadow-medium),
    0 15px 40px var(--shadow-light);
  transform: translateY(-4px);
}


.dateReasonSection {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-lg);
  margin: 16px 0;
  align-items: flex-start;
  width: 100%;
  justify-content: space-between;
}

.verticalSection {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-lg);
  flex: 1;
  min-width: 200px;
  max-width: 48%;
}

.verticalSection .dateField {
  flex: none;
  min-width: 100%;
  max-width: 100%;
}

.dateField {
  flex: 1;
  min-width: 200px;
  max-width: 48%;
}

.dateField .descriptionTextarea {
  width: 100%;
  padding: var(--spacing-md) 16px;
  border: 2px solid var(--neutral-400);
  border-radius: var(--radius-md);
  font-size: 1.1rem;
  background: var(--page-background);
  color: var(--neutral-800);
  transition: all var(--transition-normal);
  font-weight: 600;
  box-shadow: var(--shadow-sm);
  resize: vertical;
  min-height: 80px;
  font-family: inherit;
  line-height: 1.5;
}

.dateField .descriptionTextarea:focus {
  outline: none;
  border-color: var(--primary-color);
  background: var(--page-background);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
  transform: translateY(-1px);
}

.dateField .descriptionTextarea::placeholder {
  color: #9ca3af;
  font-style: italic;
}
.dateField label {
  font-weight: 600;
  color: var(--primary-color);
  margin-bottom: var(--spacing-sm);
  display: block;
  font-size: 1.1rem;
  position: relative;
}
.dateInput {
  width: 100%;
  padding: var(--spacing-md) 16px;
  border: 2px solid var(--neutral-400);
  border-radius: var(--radius-md);
  font-size: 1.1rem;
  background: var(--page-background);
  color: var(--neutral-800);
  transition: all var(--transition-normal);
  font-weight: 600;
  box-shadow: var(--shadow-sm);
}
.dateInput:focus {
  outline: none;
  border-color: var(--primary-color);
  background: var(--page-background);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
  transform: translateY(-1px);
}
.reasonSection {
  flex: 2;
  min-width: 250px;
  max-width: 48%;
}
.reasonSection label {
  font-weight: 600;
  color: #1e40af;
  margin-bottom: 8px;
  display: block;
  font-size: 1.1rem;
  position: relative;
}
.optionalLabel {
  font-weight: 600;
  color: #014871;
  margin-bottom: 12px;
  display: block;
  font-size: 18px;
  letter-spacing: 0.5px;
  position: relative;
  padding-right: 10px;
}
.optionalLabel::after {
  content: '';
  position: absolute;
  bottom: -4px;
  right: 0;
  width: 30px;
  height: 2px;
  background: #4a8fa3;
}
.optional {
  color: #4a8fa3;
  font-weight: 500;
  font-size: 0.9rem;
  margin-right: 8px;
  letter-spacing: normal;
  font-style: italic;
}
.descriptionSection {
  margin: 24px 0;
  width: 100%;
}
.descriptionTextarea {
  width: 100%;
  padding: 12px 16px;
  border: 1px solid #cbd5e1;
  border-radius: 8px;
  font-size: 1rem;
  background: #ffffff;
  color: #1f2937;
  transition: all 0.2s ease;
  font-weight: 500;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  resize: vertical;
  min-height: 80px;
  font-family: inherit;
  line-height: 1.5;
}
.descriptionTextarea:focus {
  outline: none;
  border-color: #3b82f6;
  background: #ffffff;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
  transform: translateY(-1px);
}
.descriptionTextarea::placeholder {
  color: #9ca3af;
  font-style: italic;
}
.selectedReasons {
  padding: 16px;
  margin-bottom: 20px;
  border: 2px solid var(--neutral-400);
  border-radius: 12px;
  background: var(--page-background);
  min-height: 60px;
  display: flex;
  align-items: center;
  font-size: 18px;
  color: var(--neutral-800);
  box-shadow: var(--shadow-sm);
  width: 100%;
  font-weight: 600;
}
.selectedReasons:focus-within {
  border-color: #4a8fa3;
  box-shadow: 0 0 0 3px rgba(74, 143, 163, 0.15);
}
.noSelection {
  color: #4a8fa3;
  font-style: italic;
  font-weight: 400;
}
.reasonButtons {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  justify-content: center;
  margin-top: 8px;
}
.reasonButton {
  padding: 10px 16px;
  border-radius: 8px;
  cursor: pointer;
  transition: var(--transition-all);
  font-size: 14px;
  font-weight: 600;
  text-align: center;
  width: fit-content;
  white-space: nowrap;
  background: #ffffff;
  color: #1e40af;
  border: 1px solid #1e40af;
  box-shadow: 0 2px 6px rgba(30, 64, 175, 0.1);
  position: relative;
  overflow: hidden;
}
.reasonButton:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 10px rgba(30, 64, 175, 0.15);
  background: rgba(30, 64, 175, 0.05);
}
.reasonButton.selected {
  background: #ffffff;
  color: #3b82f6;
  border: 1px solid #3b82f6;
  box-shadow: 0 2px 6px rgba(59, 130, 246, 0.15);
}
.reasonButton.selected:hover {
  background: rgba(59, 130, 246, 0.05);
  box-shadow: 0 4px 10px rgba(59, 130, 246, 0.2);
}
.reportFormButtons {
  display: flex;
  gap: 20px;
  justify-content: center;
  margin-top: 30px;
  padding-top: 20px;
  flex-wrap: nowrap;
  align-items: center;
}
.saveButton {
  background: #1e40af;
  color: #ffffff;
  border: none;
  padding: 16px 28px;
  font-weight: 600;
  font-size: 16px;
  display: flex;
  align-items: center;
  gap: 12px;
  box-shadow: 0 4px 12px rgba(30, 64, 175, 0.2);
  flex: 1;
  justify-content: center;
  min-width: 200px;
  min-height: 56px;
  border-radius: 8px;
  position: relative;
  overflow: hidden;
  transition: var(--transition-all);
}
.saveButton:hover {
  background: #3b82f6;
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(30, 64, 175, 0.3);
}
.addActionForm {
  background: var(--page-background);
  border-radius: 20px;
  padding: 24px;
  box-shadow: 
    0 20px 60px var(--shadow-medium),
    0 10px 30px var(--shadow-light);
  border: none;
  margin: 0 auto;
  width: 100%;
  max-width: 900px;
  transition: all var(--transition-normal);
  position: relative;
  overflow: hidden;
}



.formHeader {
  display: flex;
  align-items: center;
  gap: 12px;
  margin: 0 0 20px 0;
  padding: 0 0 16px 0;
  border-bottom: 1px solid #e2e8f0;
  flex-direction: column;
}

.formTitle {
  font-size: 1.5rem;
  font-weight: 700;
  color: #1e40af;
  margin: 0;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.formSubtitle {
  font-size: 0.95rem;
  color: #64748b;
  margin: 0;
  font-weight: 500;
  line-height: 1.5;
}

.errorAlert {
  background: rgba(220, 38, 38, 0.08);
  border-right: 4px solid #dc2626;
  border-radius: 8px;
  padding: 14px 18px;
  margin-bottom: 20px;
  color: #dc2626;
  font-weight: 600;
  font-size: 0.95rem;
  display: flex;
  align-items: center;
  gap: 10px;
  box-shadow: var(--box-shadow-danger);
}

.errorAlert::before {
  content: '⚠️';
  font-size: 18px;
}

.actionField {
  margin-bottom: 16px;
}

.actionField label {
  font-weight: 600;
  color: #1e40af;
  margin-bottom: 8px;
  display: block;
  font-size: 0.95rem;
  position: relative;
}

.actionInput {
  width: 100%;
  padding: 10px 12px;
  border: 1px solid #cbd5e1;
  border-radius: 6px;
  font-size: 0.95rem;
  background: #ffffff;
  color: #1f2937;
  transition: all 0.2s ease;
  font-weight: 500;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.actionInput:focus {
  outline: none;
  border-color: #3b82f6;
  background: #ffffff;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
}

.actionInput:disabled {
  background: rgba(1, 72, 113, 0.05);
  color: #4a8fa3;
  cursor: not-allowed;
  border-color: rgba(1, 72, 113, 0.2);
}

.actionFormButtons {
  display: flex;
  gap: 12px;
  justify-content: center;
  margin-top: 20px;
  padding-top: 16px;
  flex-wrap: nowrap;
  align-items: center;
}

.addActionButton {
  padding: var(--padding-button);
  background: #1e40af;
  color: white;
  border: none;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: var(--transition-all);
  font-size: 0.95rem;
  display: flex;
  align-items: center;
  gap: 10px;
  box-shadow: 0 4px 12px rgba(30, 64, 175, 0.2);
  flex: 1;
  justify-content: center;
  min-width: 150px;
  position: relative;
  overflow: hidden;
}

.addActionButton:hover {
  background: #3b82f6;
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(30, 64, 175, 0.3);
}

.addActionButton:disabled {
  background: rgba(1, 72, 113, 0.3);
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.cancelButton {
  padding: var(--padding-button);
  background: #ffffff;
  color: #ef4444;
  border: 1px solid #ef4444;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: var(--transition-all);
  font-size: 0.95rem;
  display: flex;
  align-items: center;
  gap: 10px;
  flex: 1;
  justify-content: center;
  min-width: 120px;
  box-shadow: 0 1px 3px rgba(239, 68, 68, 0.1);
}

.cancelButton:hover {
  background: rgba(239, 68, 68, 0.05);
  transform: translateY(-2px);
  box-shadow: 0 4px 10px rgba(239, 68, 68, 0.15);
}
.buttonIcon {
  margin-left: 8px;
  font-size: 18px;
  vertical-align: middle;
}

@media (max-width: 768px) {
  .addReportForm,
  .addActionForm {
    padding: 24px;
    max-width: 100%;
    width: 100%;
    margin: 20px 0;
    box-sizing: border-box;
  }
  .dateReasonSection {
    gap: 16px;
    flex-direction: column;
  }
  .dateField,
  .reasonSection,
  .verticalSection {
    min-width: 100%;
    max-width: 100%;
    width: 100%;
  }
  .actionField {
    margin-bottom: 20px;
  }
  .reportFormButtons,
  .actionFormButtons {
    gap: 16px;
    flex-wrap: nowrap;
  }
  .addActionButton,
  .cancelButton,
  .saveButton {
    flex: 1;
    min-width: 0;
    max-width: none;
    padding: 14px 16px;
    font-size: 15px;
    height: 52px;
    min-height: 52px;
    font-weight: 600;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
  }
}

@media (max-width: 480px) {
  .addReportForm,
  .addActionForm {
    padding: 16px;
    border-radius: 12px;
    width: 100%;
    max-width: 100%;
    margin: 16px 0;
    box-sizing: border-box;
  }
  
  .formHeader {
    margin-bottom: 12px;
    padding-bottom: 12px;
  }
  
  .formTitle {
    font-size: 1.3rem;
  }
  
  .formSubtitle {
    font-size: 0.85rem;
    text-align: center;
  }
  
  .dateInput,
  .descriptionTextarea,
  .selectedReasons {
    padding: 12px;
    font-size: 15px;
    border-width: 2px;
  }
  
  .actionInput {
    padding: 8px 10px;
    font-size: 0.9rem;
  }
  
  .reasonButtons {
    gap: 8px;
  }
  
  .reasonButton {
    padding: 8px 12px;
    font-size: 14px;
    border-width: 2px;
  }
  
  .reportFormButtons,
  .actionFormButtons {
    flex-direction: column;
    gap: 12px;
  }
  
  .addActionButton,
  .cancelButton,
  .saveButton {
    width: 100%;
    min-height: 48px;
    font-size: 14px;
  }


  .dateInput,
  .actionInput {
    padding: 12px;
    font-size: 14px;
  }
  .selectedReasons {
    padding: 12px;
    font-size: 14px;
    min-height: 48px;
  }
  .reasonButton {
    padding: 8px 12px;
    font-size: 13px;
    border-width: 2px;
  }
  .reportFormButtons,
  .actionFormButtons {
    gap: 12px;
    flex-wrap: nowrap;
  }
  .addActionButton,
  .cancelButton,
  .saveButton {
    flex: 1;
    min-width: 0;
    max-width: none;
    padding: 12px 8px;
    font-size: 14px;
    height: 48px;
    min-height: 48px;
    font-weight: 600;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
  }
}

/* أسلوب العلامات لتوقيت الإشعار */
.reminderTags {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
  margin-top: 8px;
}

.reminderTag {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 16px;
  border: 2px solid #e2e8f0;
  border-radius: 12px;
  background: #ffffff;
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: 140px;
  justify-content: center;
  font-weight: 500;
  font-size: 0.95rem;
  color: #64748b;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  user-select: none;
  position: relative;
  overflow: hidden;
}

.reminderTag::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(59, 130, 246, 0.1), transparent);
  transition: left 0.6s ease;
}

.reminderTag:hover::before {
  left: 100%;
}

.reminderTag:hover {
  border-color: #3b82f6;
  color: #3b82f6;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
}

.reminderTag.selectedTag {
  border-color: #3b82f6;
  background: rgba(59, 130, 246, 0.08);
  color: #1e40af;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.2);
}

.reminderTag.selectedTag:hover {
  background: rgba(59, 130, 246, 0.12);
  transform: translateY(-1px);
}

.tagIcon {
  font-size: 1.1rem;
  flex-shrink: 0;
}

.tagText {
  font-weight: 600;
  white-space: nowrap;
}

.reminderTag.selectedTag .tagText {
  color: #1e40af;
  font-weight: 700;
}

.reminderTag.selectedTag .tagIcon {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.1); }
  100% { transform: scale(1); }
}

/* تحسينات للشاشات الصغيرة */
@media (max-width: 768px) {
  .reminderTags {
    flex-direction: column;
    gap: 10px;
  }
  
  .reminderTag {
    min-width: 100%;
    padding: 14px 16px;
    font-size: 1rem;
  }
  
  .tagIcon {
    font-size: 1.2rem;
  }
  
  .tagText {
    font-size: 1rem;
  }
}

@media (max-width: 480px) {
  .reminderTag {
    padding: 12px 14px;
    min-height: 50px;
    font-size: 0.95rem;
  }
  
  .tagIcon {
    font-size: 1.1rem;
  }
  
  .tagText {
    font-size: 0.95rem;
  }
}
