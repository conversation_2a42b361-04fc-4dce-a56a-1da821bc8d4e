الكلاسات المستخدمة في CaseDetails.module.css
========================================

من CaseInfoGroups.jsx:
- partiesCard
- identificationCard  
- locationCard
- longTextContainer
- longTextFull
- longTextPreview
- moreTextIndicator
- valueTextHorizontal
- dataFieldHorizontal
- fieldLabelHorizontal
- fieldValueContainerHorizontal
- editFieldContainerHorizontal
- fullCaseNumberEditContainer
- caseNumberFieldsContainer
- caseNumberField
- inlineLabel
- editInput
- inputError
- errorText
- editActionsHorizontal
- saveEditButton
- cancelEditButton
- textareaInput
- valueWithActionHorizontal
- booleanValue
- trueValue
- falseValue
- referralButtonHorizontal
- enabledReferralButton
- disabledReferralButton
- editIconButtonHorizontal
- coloredCardsContainerHorizontal
- simpleCard
- simpleCardHeader
- cardPrimaryContent
- cardExpandedContent
- expanded
- expandedContentDivider
- transferDegreeSection
- transferDegreeBox
- partiesTheme
- identificationTheme
- transferHeader
- transferTitleSection
- transferIcon
- transferTitle
- transferContent
- transferInfoRow
- transferLabel
- transferValue
- transferButton
- expandToggle
- transferConfirmationOverlay
- transferConfirmationDialog
- transferConfirmationIcon
- transferConfirmationTitle
- transferConfirmationMessage
- transferConfirmationActions
- transferConfirmButton
- transferCancelButton
- statusOptionsContainer
- questionText
- statusOptions
- statusOptionButton
- selected
- dateInputContainer
- dateLabel
- dateInput
- errorMessage
- verdictButtonsContainer
- verdictButton
- selectedButton
- verdictButtonIcon

من CaseArchiveView.jsx:
- cardHeaderInSimple
- cardHeaderContent
- cardTitle
- fullReportButton
- cardPrimaryContent
- timelineScrollContainer
- timelineEntry
- timelineDate
- timelineDescription
- noTimeline
- coloredCard
- timelineCard
- cardHeader
- fullReportModalOverlay
- fullReportModal
- fullReportModalHeader
- closeModalButton
- fullReportModalContent

المجموع الإجمالي للكلاسات المستخدمة:
=============================================
partiesCard, identificationCard, locationCard, longTextContainer, longTextFull, longTextPreview, moreTextIndicator, valueTextHorizontal, dataFieldHorizontal, fieldLabelHorizontal, fieldValueContainerHorizontal, editFieldContainerHorizontal, fullCaseNumberEditContainer, caseNumberFieldsContainer, caseNumberField, inlineLabel, editInput, inputError, errorText, editActionsHorizontal, saveEditButton, cancelEditButton, textareaInput, valueWithActionHorizontal, booleanValue, trueValue, falseValue, referralButtonHorizontal, enabledReferralButton, disabledReferralButton, editIconButtonHorizontal, coloredCardsContainerHorizontal, simpleCard, simpleCardHeader, cardPrimaryContent, cardExpandedContent, expanded, expandedContentDivider, transferDegreeSection, transferDegreeBox, partiesTheme, identificationTheme, transferHeader, transferTitleSection, transferIcon, transferTitle, transferContent, transferInfoRow, transferLabel, transferValue, transferButton, expandToggle, transferConfirmationOverlay, transferConfirmationDialog, transferConfirmationIcon, transferConfirmationTitle, transferConfirmationMessage, transferConfirmationActions, transferConfirmButton, transferCancelButton, statusOptionsContainer, questionText, statusOptions, statusOptionButton, selected, dateInputContainer, dateLabel, dateInput, errorMessage, verdictButtonsContainer, verdictButton, selectedButton, verdictButtonIcon, cardHeaderInSimple, cardHeaderContent, cardTitle, fullReportButton, timelineScrollContainer, timelineEntry, timelineDate, timelineDescription, noTimeline, coloredCard, timelineCard, cardHeader, fullReportModalOverlay, fullReportModal, fullReportModalHeader, closeModalButton, fullReportModalContent