// مدير منع المحاولات المتكررة (Lockout Manager)
const LOCKOUT_DURATION = 15 * 60 * 1000; // 15 دقيقة
const MAX_ATTEMPTS = 3;

export class LockoutManager {
  constructor(type) {
    this.type = type; // 'login' أو 'signup'
    this.storageKey = `${type}Lockout`;
  }

  // التحقق من حالة القفل الحالية
  checkLockoutStatus() {
    const lockoutData = localStorage.getItem(this.storageKey);
    if (!lockoutData) {
      return { isLocked: false, attempts: 0 };
    }

    const { timestamp, attempts } = JSON.parse(lockoutData);
    const now = Date.now();

    // التحقق من انتهاء فترة القفل
    if (now - timestamp >= LOCKOUT_DURATION) {
      localStorage.removeItem(this.storageKey);
      return { isLocked: false, attempts: 0 };
    }

    // التحقق من تجاوز الحد الأقصى للمحاولات
    if (attempts >= MAX_ATTEMPTS) {
      const remainingMinutes = Math.ceil((LOCKOUT_DURATION - (now - timestamp)) / 1000 / 60);
      return {
        isLocked: true,
        attempts,
        remainingMinutes,
        message: this.getLockoutMessage(remainingMinutes)
      };
    }

    return { isLocked: false, attempts };
  }

  // تسجيل محاولة فاشلة
  recordFailedAttempt(currentAttempts = 0) {
    const attempts = currentAttempts + 1;
    const lockoutData = {
      timestamp: Date.now(),
      attempts
    };

    localStorage.setItem(this.storageKey, JSON.stringify(lockoutData));

    // التحقق من تجاوز الحد الأقصى
    if (attempts >= MAX_ATTEMPTS) {
      const remainingMinutes = Math.ceil(LOCKOUT_DURATION / 1000 / 60);
      return {
        isLocked: true,
        attempts,
        remainingMinutes,
        message: this.getLockoutMessage(remainingMinutes)
      };
    }

    return { isLocked: false, attempts };
  }

  // مسح بيانات القفل (عند النجاح)
  clearLockout() {
    localStorage.removeItem(this.storageKey);
  }

  // الحصول على رسالة القفل
  getLockoutMessage(remainingMinutes) {
    const action = this.type === 'login' ? 'تسجيل الدخول' : 'إنشاء الحساب';
    const minuteText = remainingMinutes > 1 ? 'دقائق' : 'دقيقة';
    return `تم قفل ${action} مؤقتًا بسبب محاولات متكررة. يرجى المحاولة مرة أخرى بعد ${remainingMinutes} ${minuteText}.`;
  }

  // الحصول على عدد المحاولات المتبقية
  getRemainingAttempts(currentAttempts = 0) {
    return Math.max(0, MAX_ATTEMPTS - currentAttempts);
  }
}

// إنشاء مثيلات للاستخدام
export const loginLockout = new LockoutManager('login');
export const signupLockout = new LockoutManager('signup');

// دوال مساعدة للاستخدام السهل
export const checkLoginLockout = () => loginLockout.checkLockoutStatus();
export const checkSignupLockout = () => signupLockout.checkLockoutStatus();
export const recordLoginFailure = (attempts) => loginLockout.recordFailedAttempt(attempts);
export const recordSignupFailure = (attempts) => signupLockout.recordFailedAttempt(attempts);
export const clearLoginLockout = () => loginLockout.clearLockout();
export const clearSignupLockout = () => signupLockout.clearLockout();
