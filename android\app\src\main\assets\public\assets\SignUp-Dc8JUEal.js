import{r as o,u as I,j as e,x as L,k as E,y as O,z as F,f as T,d as $}from"./index-Bd3HN_hN.js";function U(){const[m,N]=o.useState(""),[g,k]=o.useState(""),[h,y]=o.useState(""),[f,M]=o.useState(""),[j,i]=o.useState(""),[v,b]=o.useState(""),[n,l]=o.useState(!1),[D,x]=o.useState(0),S=I(),p=3,c=15*60*1e3;o.useEffect(()=>{const t=localStorage.getItem("signupLockout");if(t){const{timestamp:d,attempts:r}=JSON.parse(t),a=Date.now();if(a-d<c&&r>=p){const s=Math.ceil((c-(a-d))/1e3/60);i(`تم قفل إنشاء الحساب مؤقتًا بسبب محاولات متكررة. يرجى المحاولة مرة أخرى بعد ${s} دقيقة${s>1?"ات":""}.`),l(!0)}else localStorage.removeItem("signupLockout"),x(0)}},[]);const A=async t=>{t.preventDefault(),i(""),b(""),l(!0);const d=localStorage.getItem("signupLockout");let r=D;if(d){const{timestamp:a,attempts:s}=JSON.parse(d),u=Date.now();if(u-a<c&&s>=p){const w=Math.ceil((c-(u-a))/1e3/60);i(`تم قفل إنشاء الحساب مؤقتًا بسبب محاولات متكررة. يرجى المحاولة مرة أخرى بعد ${w} دقيقة${w>1?"ات":""}.`),l(!0);return}else u-a>=c?(localStorage.removeItem("signupLockout"),r=0):r=s}if(!g.trim()){i("يرجى إدخال البريد الإلكتروني."),l(!1);return}if(!h.trim()){i("يرجى إدخال كلمة المرور."),l(!1);return}try{const s=(await L(E,g.trim(),h)).user;console.log("تم إنشاء حساب بنجاح:",s),m.trim()&&(await O(s,{displayName:m.trim()}),console.log("تم تحديث اسم المستخدم:",m.trim())),await F(T($,"users",s.uid),{username:m.trim()||null,email:s.email,phone:f.trim()||null,role:"محامي",createdAt:new Date().toISOString()}),console.log("تم حفظ بيانات المستخدم في Firestore"),b("تم إنشاء الحساب بنجاح! سيتم توجيهك إلى صفحة تسجيل الدخول."),localStorage.removeItem("signupLockout"),x(0),setTimeout(()=>{S("/login")},1500)}catch(a){if(console.error("خطأ في إنشاء الحساب:",a.code,a.message),r+=1,x(r),localStorage.setItem("signupLockout",JSON.stringify({timestamp:Date.now(),attempts:r})),r>=p){const u=Math.ceil(c/1e3/60);i(`تم قفل إنشاء الحساب مؤقتًا بسبب محاولات متكررة. يرجى المحاولة مرة أخرى بعد ${u} دقيقة${u>1?"ات":""}.`),l(!0);return}let s="حدث خطأ أثناء إنشاء الحساب. يرجى المحاولة مرة أخرى.";switch(a.code){case"auth/email-already-in-use":s="البريد الإلكتروني هذا مستخدم بالفعل. يرجى استخدام بريد إلكتروني آخر أو تسجيل الدخول.";break;case"auth/invalid-email":s="صيغة البريد الإلكتروني غير صحيحة. يرجى إدخال بريد إلكتروني صالح.";break;case"auth/weak-password":s="كلمة المرور ضعيفة جدًا. يجب أن تحتوي على 6 أحرف على الأقل.";break;case"auth/too-many-requests":s="تم حظر إنشاء الحساب مؤقتًا بسبب كثرة المحاولات. يرجى المحاولة لاحقًا.";break;default:s="حدث خطأ غير متوقع. يرجى التحقق من اتصال الإنترنت والمحاولة لاحقًا."}i(s)}finally{r<p&&l(!1)}},C=()=>{S("/login")};return e.jsx("div",{className:"signup-container",children:e.jsxs("div",{className:"signup-box",children:[e.jsx("h2",{children:"إنشاء حساب جديد"}),n&&e.jsxs("div",{className:"loading-container",children:[e.jsx("div",{className:"loader"}),e.jsx("p",{className:"loading-text",children:"جاري إنشاء الحساب..."})]}),j&&e.jsx("div",{className:"error-message",children:j}),v&&e.jsx("div",{className:"success-message",children:v}),e.jsxs("form",{onSubmit:A,children:[e.jsxs("div",{className:"form-row",children:[e.jsxs("div",{className:"form-group",children:[e.jsx("label",{htmlFor:"username",children:"اسم المستخدم (اختياري)"}),e.jsx("input",{type:"text",id:"username",name:"username",value:m,onChange:t=>N(t.target.value),disabled:n,placeholder:"أدخل اسم المستخدم"})]}),e.jsxs("div",{className:"form-group",children:[e.jsx("label",{htmlFor:"email",children:"البريد الإلكتروني"}),e.jsx("input",{type:"email",id:"email",name:"email",value:g,onChange:t=>k(t.target.value),required:!0,disabled:n,placeholder:"أدخل بريدك الإلكتروني"})]})]}),e.jsxs("div",{className:"form-row",children:[e.jsxs("div",{className:"form-group",children:[e.jsx("label",{htmlFor:"password",children:"كلمة المرور"}),e.jsx("input",{type:"password",id:"password",name:"password",value:h,onChange:t=>y(t.target.value),required:!0,disabled:n,placeholder:"أدخل كلمة المرور"})]}),e.jsxs("div",{className:"form-group",children:[e.jsx("label",{htmlFor:"phone",children:"رقم الهاتف (اختياري)"}),e.jsx("input",{type:"tel",id:"phone",name:"phone",value:f,onChange:t=>M(t.target.value),disabled:n,placeholder:"أدخل رقم الهاتف"})]})]}),e.jsxs("div",{className:"button-row",children:[e.jsx("button",{type:"submit",disabled:n,children:n?"جاري إنشاء الحساب...":"إنشاء حساب"}),e.jsx("button",{type:"button",className:"back-button",onClick:C,disabled:n,children:"رجوع"})]})]}),e.jsxs("p",{className:"login-link",children:["لديك حساب بالفعل؟ ",e.jsx("a",{href:"/login",children:"تسجيل الدخول"})]})]})})}export{U as default};
