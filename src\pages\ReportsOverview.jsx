import React, { useState, useEffect, useMemo } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  FaFileAlt, FaChevronLeft, FaCalendarAlt, FaSearch,
  FaUserTie, FaBalanceScale, FaChartPie, FaThLarge, FaFileSignature, FaLink,
  FaTable, FaGlobe, FaMobileAlt, FaSync
} from 'react-icons/fa';
import TopBar from '../components/topbar/TopBar';
import LoadingSpinner from '../components/ui/LoadingSpinner';
import TimelinePage from './TimelinePage.jsx';
import styles from './ReportsOverview.module.css';
import { db } from '../config/firebaseConfig';
import { collection, query, where, getDocs, limit, startAfter, orderBy } from 'firebase/firestore';
// ✅ استيراد دالة جلب القضايا المشتركة
import { getActiveAccount, getCases, setActiveAccount } from '../services/StorageService';
import { getSharedCasesForUser } from '../services/GroupsService'; // افترض أن الملف موجود في هذا المسار
import { cacheManager } from '../utils/CacheManager';
import { useDebounce } from '../utils/useDebounce';

const ReportsOverview = ({ currentUser }) => {
  const navigate = useNavigate();
  const [searchTerm, setSearchTerm] = useState('');
  const [viewMode, setViewMode] = useState('cards');
  
  // التحقق من وجود مصطلح البحث في الـ URL
  useEffect(() => {
    const urlParams = new URLSearchParams(window.location.search);
    const searchParam = urlParams.get('search');
    if (searchParam) {
      setSearchTerm(searchParam);
    }
  }, []);
  const [casesList, setCasesList] = useState([]);
  const [lastDoc, setLastDoc] = useState(null);
  const [loading, setLoading] = useState(false);
  const [hasMore, setHasMore] = useState(true);
  const [error, setError] = useState(null);
  const [activeAccount, setActiveAccountState] = useState(getActiveAccount());
  
  // استخدام debouncing للبحث لتحسين الأداء
  const debouncedSearchTerm = useDebounce(searchTerm, 300);
  
  const PAGE_SIZE = 10;
  const CACHE_KEY = 'cases_list';
  const CACHE_TTL = 5 * 60 * 1000;

  // ✅ تعديل دالة جلب القضايا لتشمل القضايا المشتركة
  const fetchCases = async (isLoadMore = false, bypassCache = false) => {
    if (!currentUser) {
      setError('المستخدم غير متوفر. يرجى تسجيل الدخول.');
      return;
    }

    const activeAccount = getActiveAccount();
    setLoading(true);
    setError(null);

    try {
      if (activeAccount === 'online') {
        if (!navigator.onLine) {
          setError('غير متصل بالإنترنت. يرجى الاتصال بالإنترنت أو التبديل إلى الحساب المحلي.');
          setLoading(false);
          return;
        }

        // تحميل المجموعات والقضايا بشكل متوازي لتحسين الأداء
        const [groupsResult] = await Promise.allSettled([
          import('../services/GroupsService').then(mod => mod.getGroups(currentUser.uid))
        ]);

        let groupIds = [];
        if (groupsResult.status === 'fulfilled' && Array.isArray(groupsResult.value)) {
          groupIds = groupsResult.value.map(g => g.id);
        }

        // جلب القضايا بناءً على groupIds
        const newCases = await getCases(currentUser.uid, groupIds);
        newCases.sort((a, b) => new Date(b.updatedAt || b.createdAt || 0) - new Date(a.updatedAt || a.createdAt || 0));
        setCasesList(newCases);
        setHasMore(false);
      } else {
        // الحصول على القضايا من التخزين المحلي - يجب جلب المجموعات أيضاً
        console.log('🔍 جلب القضايا من التخزين المحلي');
        
        // جلب معرفات المجموعات التي ينتمي إليها المستخدم (حتى في الوضع المحلي)
        let groupIds = [];
        try {
          const groups = await import('../services/GroupsService').then(mod => mod.getGroups(currentUser.uid));
          const resolvedGroups = await groups;
          groupIds = Array.isArray(resolvedGroups) ? resolvedGroups.map(g => g.id) : [];
          console.log('🏷️ معرفات المجموعات المحلية:', groupIds);
        } catch (e) {
          console.log('⚠️ خطأ في جلب المجموعات المحلية:', e);
          groupIds = [];
        }
        
        // جلب القضايا بناءً على groupIds (مثل الوضع الأونلاين)
        const newCases = await getCases(currentUser.uid, groupIds);
        console.log('📊 عدد القضايا المجلبة نهائياً:', newCases.length);
        newCases.sort((a, b) => new Date(b.updatedAt || 0) - new Date(a.updatedAt || 0));
        setCasesList(newCases);
        setHasMore(false);
      }
    } catch (e) {
      console.error('خطأ في جلب القضايا:', e);
      setError('خطأ في جلب القضايا: ' + e.message);
    } finally {
      setLoading(false);
    }
  };


  useEffect(() => {
    if (currentUser) {
        fetchCases(false, true);
    }
  }, [currentUser]);

  // الاستماع لإضافة قضايا جديدة
  useEffect(() => {
    const handleCasesRefresh = () => {
      console.log('🔄 تحديث قائمة القضايا بسبب إضافة قضية جديدة');
      if (currentUser) {
        fetchCases(false, true);
      }
    };

    cacheManager.addListener('cases_refresh', handleCasesRefresh);

    return () => {
      cacheManager.removeListener('cases_refresh', handleCasesRefresh);
    };
  }, [currentUser]);

  const getLastReportInfo = (deferrals) => {
    if (!deferrals || deferrals.length === 0) return { date: null, report: null };
    const activeDeferrals = deferrals.filter(deferral => !deferral.isDeleted);
    const lastDeferral = activeDeferrals[activeDeferrals.length - 1];
    if (!lastDeferral) return { date: null, report: null };
    return {
      date: lastDeferral.date ? new Date(lastDeferral.date) : null,
      report: lastDeferral.content || `${lastDeferral.date} - ${lastDeferral.reasons?.join('، ')}` || 'لا توجد تفاصيل'
    };
  };

  const getAllActionsInfo = (actions, deferrals) => {
    if (!actions || actions.length === 0) return [];
    const activeActions = actions.filter(action => !action.isDeleted && !action.isCompleted);
    return activeActions.map(action => ({
        date: action.deadline ? new Date(action.deadline) : null,
        description: action.description || 'لا توجد تفاصيل',
        type: 'action'
      })).sort((a, b) => (b.date || 0) - (a.date || 0));
  };

  const getAllDeferralsInfo = (deferrals) => {
    if (!deferrals || deferrals.length === 0) return [];
    const activeDeferrals = deferrals.filter(deferral => !deferral.isDeleted);
    return activeDeferrals.map(deferral => ({
        date: deferral.date ? new Date(deferral.date) : null,
        description: deferral.content || deferral.reasons?.join('، ') || 'لا توجد تفاصيل',
        type: 'deferral'
      })).sort((a, b) => (b.date || 0) - (a.date || 0));
  };

  // معالجة محسنة للبيانات باستخدام useMemo
  const processedCases = useMemo(() => {
    return casesList.map(caseItem => {
      try {
        const lastReportInfo = getLastReportInfo(caseItem.deferrals);
        const allActions = getAllActionsInfo(caseItem.actions, caseItem.deferrals);
        const allDeferrals = getAllDeferralsInfo(caseItem.deferrals);

        return {
          ...caseItem,
          lastReport: lastReportInfo.report,
          lastReportDate: lastReportInfo.date,
          allActions: allActions,
          allDeferrals: allDeferrals,
          lastAction: allActions.length > 0 ? allActions[0].description : null,
          lastActionDate: allActions.length > 0 ? allActions[0].date : null,
        };
      } catch (e) {
        console.error(`خطأ في معالجة القضية ${caseItem.id}:`, e);
        return caseItem; // إرجاع القضية الأصلية في حالة الخطأ
      }
    });
  }, [casesList]);

  // البحث المحسن باستخدام debouncedSearchTerm
  const casesToDisplay = useMemo(() => {
    if (!debouncedSearchTerm) return processedCases;
    
    const searchTermLower = debouncedSearchTerm.toLowerCase();
    return processedCases.filter(enhancedCase =>
      (enhancedCase.fullCaseNumber || '').toLowerCase().includes(searchTermLower) ||
      (enhancedCase.clientName || '').toLowerCase().includes(searchTermLower) ||
      (enhancedCase.courtLocation || '').toLowerCase().includes(searchTermLower) ||
      (enhancedCase.reportLocation || '').toLowerCase().includes(searchTermLower)
    );
  }, [processedCases, debouncedSearchTerm]);

  const handleBack = () => navigate('/dashboard');
  const handleCaseClick = (caseItem) => navigate(`/case-details/${caseItem.id}`);

  const formatDate = (date) => {
    return date ? new Date(date).toLocaleDateString('ar-EG', { month: 'numeric', day: 'numeric' }) : '—';
  };
  
  const getCaseTitle = (caseItem) => {
    switch (caseItem.caseStatus || caseItem.case_status) {
      case 'دعوى قضائية': return `قضية رقم: ${caseItem.fullCaseNumber || ''}`;
      case 'محضر': return `محضر رقم: ${caseItem.fullCaseNumber || ''}`;
      case 'قيد النظر': return 'ملف قيد النظر';
      default: return `قضية رقم: ${caseItem.fullCaseNumber || ''}`;
    }
  };

  const getCardBorderClass = (caseStatus) => {
    switch (caseStatus) {
      case 'دعوى قضائية': return styles.cardBorderLawsuit;
      case 'محضر': return styles.cardBorderReport;
      case 'قيد النظر': return styles.cardBorderPending;
      default: return styles.cardBorderDefault;
    }
  };

  const renderCases = () => {
    if (error) return <div className={styles.noCases} style={{ color: 'red' }}>{error}</div>;
    if (loading) return <LoadingSpinner message="جاري تحميل القضايا..." />;
    if (casesToDisplay.length === 0) {
      return (
        <div className={styles.noCases}>
          {searchTerm ? 'لا توجد نتائج مطابقة للبحث' : 'لا توجد قضايا لعرضها'}
        </div>
      );
    }
    
    if (viewMode === 'table') {
      return (
        <div className={styles.casesTableContainer}>
          <table className={styles.casesTable}>
            <thead>
              <tr>
                <th>اسم الموكل</th>
                <th>مكان المحكمة</th>
                <th>آخر ما تم</th>
              </tr>
            </thead>
            <tbody>
              {casesToDisplay.map((caseItem) => (
                <tr
                  key={caseItem.id}
                  onClick={() => handleCaseClick(caseItem)}
                  className={
                    (caseItem.caseStatus || caseItem.case_status) === 'قيد النظر'
                      ? styles.rowPending
                      : (caseItem.caseStatus || caseItem.case_status) === 'دعوى قضائية'
                      ? styles.rowLawsuit
                      : styles.rowDefault
                  }
                >
                  <td>{caseItem.clientName || 'غير محدد'}</td>
                  <td>
                    {(caseItem.caseStatus || caseItem.case_status) === 'دعوى قضائية' 
                      ? (caseItem.courtLocation || 'غير محدد')
                      : (caseItem.reportLocation || caseItem.courtLocation || 'غير محدد')
                    }
                  </td>
                  <td>
                    {caseItem.lastAction ? (
                      <>
                        {caseItem.lastAction.substring(0, 20) + (caseItem.lastAction.length > 20 ? '...' : '')}
                        <br />
                        <span className={styles.dateText}>
                          {formatDate(caseItem.lastActionDate)}
                        </span>
                      </>
                    ) : caseItem.lastReport ? (
                      <>
                        {caseItem.lastReport.substring(0, 20) + (caseItem.lastReport.length > 20 ? '...' : '')}
                        <br />
                        <span className={styles.dateText}>
                          {formatDate(caseItem.lastReportDate)}
                        </span>
                      </>
                    ) : (
                      '—'
                    )}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
          {hasMore && (
            <button onClick={() => fetchCases(true)} className={styles.loadMoreButton} disabled={loading}>
              {loading ? 'جاري التحميل...' : 'تحميل المزيد'}
            </button>
          )}
        </div>
      );
    }

    return viewMode === 'cards' ? (
      <div className={styles.casesGrid}>
        {casesToDisplay.map((caseItem) => (
          <div
            key={caseItem.id}
            className={`${styles.caseCard} ${getCardBorderClass(caseItem.caseStatus || caseItem.case_status)}`}
            onClick={() => handleCaseClick(caseItem)}
          >
            {caseItem.sharedInfo && (
              <div className={styles.sharedBadge} title={`هذه القضية مشتركة معك عبر مجموعة "${caseItem.sharedInfo.groupName}"`}>
                <FaGlobe /> {`مشتركة من: ${caseItem.sharedInfo.groupName}`}
              </div>
            )}
            <div className={styles.cardHeader}>
              <h3 className={styles.caseNumber}>
                <FaFileAlt className={styles.caseIcon} />
                {getCaseTitle(caseItem)}
              </h3>
              <span className={styles.reportDate}>
                <FaCalendarAlt /> {formatDate(caseItem.lastActionDate || caseItem.lastReportDate)}
              </span>
            </div>

            <div className={styles.cardBody}>
              <div className={styles.infoGroup}>
                <FaUserTie className={styles.infoIcon} />
                <div>
                  <span className={styles.infoLabel}>الموكل:</span>
                  <span className={styles.infoValue}>{caseItem.clientName || 'غير محدد'}</span>
                </div>
              </div>

              <div className={styles.infoGroup}>
                <FaBalanceScale className={styles.infoIcon} />
                <div>
                  <span className={styles.infoLabel}>
                    {(caseItem.caseStatus || caseItem.case_status) === 'دعوى قضائية' ? 'المحكمة:' : 'مكان الجهة المختصة:'}
                  </span>
                  <span className={styles.infoValue}>
                    {(caseItem.caseStatus || caseItem.case_status) === 'دعوى قضائية' 
                      ? (caseItem.courtLocation || 'غير محددة')
                      : (caseItem.reportLocation || caseItem.courtLocation || 'غير محددة')
                    }
                  </span>
                </div>
              </div>

              {(caseItem.allActions.length > 0 || caseItem.allDeferrals.length > 0) && (
                <div className={styles.previewContainer}>
                  {/* عرض التنبيهات النشطة */}
                  {caseItem.allActions.length > 0 && (
                    <div className={styles.actionsSection}>
                      <h4 className={styles.sectionTitle}>التنبيهات النشطة ({caseItem.allActions.length}):</h4>
                      <div className={styles.itemsList}>
                        {caseItem.allActions.slice(0, 3).map((action, index) => (
                          <div key={index} className={styles.actionItem}>
                            <FaFileSignature className={styles.actionIcon} />
                            <div className={styles.itemContent}>
                              <span className={styles.itemText}>
                                <span>
                                  {action.description.length > 50 ?
                                    action.description.substring(0, 50) + '...' :
                                    action.description}
                                </span>
                                <span className={styles.itemDate}>{formatDate(action.date)}</span>
                              </span>
                              {action.linkedReport && (
                                <span className={styles.linkedInfo}>
                                  <FaLink className={styles.linkIcon} />
                                  مرتبط بتقرير
                                </span>
                              )}
                            </div>
                          </div>
                        ))}
                        {caseItem.allActions.length > 3 && (
                          <div className={styles.moreItems}>
                            +{caseItem.allActions.length - 3} تنبيهات أخرى
                          </div>
                        )}
                      </div>
                    </div>
                  )}

                  {/* عرض تنبيهات الجلسات */}
                  {caseItem.allDeferrals.length > 0 && (
                    <div className={styles.deferralsSection}>
                      <h4 className={styles.sectionTitle}>تنبيهات الجلسات ({caseItem.allDeferrals.length}):</h4>
                      <div className={styles.itemsList}>
                        {caseItem.allDeferrals.slice(0, 2).map((deferral, index) => (
                          <div key={index} className={styles.deferralItem}>
                            <FaCalendarAlt className={styles.deferralIcon} />
                            <div className={styles.itemContent}>
                              <span className={styles.itemText}>
                                <span>
                                  {deferral.description.length > 50 ?
                                    deferral.description.substring(0, 50) + '...' :
                                    deferral.description}
                                </span>
                                <span className={styles.itemDate}>{formatDate(deferral.date)}</span>
                              </span>
                            </div>
                          </div>
                        ))}
                        {caseItem.allDeferrals.length > 2 && (
                          <div className={styles.moreItems}>
                            +{caseItem.allDeferrals.length - 2} تنبيهات أخرى
                          </div>
                        )}
                      </div>
                    </div>
                  )}
                </div>
              )}
            </div>
          </div>
        ))}
        {hasMore && (
          <button onClick={() => fetchCases(true)} className={styles.loadMoreButton} disabled={loading}>
            {loading ? 'جاري التحميل...' : 'تحميل المزيد'}
          </button>
        )}
      </div>
    ) : (
      <div className={styles.casesContainer}>
        <TimelinePage casesList={casesToDisplay} getCaseTitle={getCaseTitle} />
      </div>
    );
  };
  
  return (
    <div className={styles.pageContainer}>
      <TopBar currentUser={currentUser} title="نظام إدارة التقارير القانونية" showBackButton onBack={handleBack} />
      <div className={styles.contentContainer}>
        <div className={styles.header}>
          <h1 className={styles.pageTitle}>نظرة عامة على القضايا</h1>
        </div>

        <div className={styles.controlsContainer}>
          <div className={styles.searchBox}>
            <FaSearch className={styles.searchIcon} />
            <input
              type="text"
              placeholder="ابحث عن قضية أو موكل..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className={styles.searchInput}
            />
            <div className={styles.viewIcons}>
              <FaThLarge 
                className={`${styles.viewIcon} ${viewMode === 'cards' ? styles.activeIcon : ''}`} 
                onClick={() => setViewMode('cards')}
                title="عرض بطاقات"
              />
              <FaChartPie 
                className={`${styles.viewIcon} ${viewMode === 'radar' ? styles.activeIcon : ''}`} 
                onClick={() => setViewMode('radar')}
                title="عرض خط زمني"
              />
              <FaTable 
                className={`${styles.viewIcon} ${viewMode === 'table' ? styles.activeIcon : ''}`} 
                onClick={() => setViewMode('table')}
                title="عرض جدول"
              />
                          </div>
          </div>
        </div>

        <div className={styles.casesContainer}>{renderCases()}</div>
      </div>
    </div>
  );
};

export default ReportsOverview;