/* متغيرات CSS مخصصة - ثيم أزرق مهني */
@import '../../styles/variables.css';

:root {
  /* ألوان حالة القضية - ألوان متنوعة */
  --case-pending: #163473; /* أزرق متوسط بديل */
  --case-report: #d2ab17; /* ذهبي للتقارير */
  --case-lawsuit: #622872; /* بنفسجي داكن للدعاوى */
}

/* التخطيط العام */
.pageWrapper {
  min-height: 100vh;
  background: var(--page-background);
  display: flex;
  flex-direction: column;
  font-family: var(--font-family-primary);
  color: var(--neutral-700);
}

.mainContainer {
  flex: 1;
  max-width: 1400px;
  margin: 0 auto;
  padding: 1.5rem;
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

/* قسم العنوان - بخلفية بيضاء أنيقة */
.headerSection {
  background: var(--page-background);
  border-radius: 20px;
  padding: 1.5rem 2rem;
  box-shadow: 
    0 20px 60px var(--shadow-medium),
    0 10px 30px var(--shadow-light);
  border: none;
  position: relative;
  overflow: visible;
  transition: all var(--transition-normal);
}

/* خانة الخبراء في أقصى اليسار والأعلى داخل الهدر */
.topLeftExpertBox {
  position: absolute;
  top: 20px;
  left: 20px;
  max-width: 350px;
  z-index: 10;
}

.headerSection:hover {
  box-shadow: 
    0 25px 70px var(--shadow-medium),
    0 15px 40px var(--shadow-light);
  transform: translateY(-4px);
}

.headerContent {
  width: 100%;
}

.headerLayout {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 1.5rem;
  width: 100%;
}

.titleSection {
  text-align: center;
  flex: 0 0 auto;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  gap: 1rem;
}

.headerExpertSection {
  flex: 0 0 auto;
  max-width: 400px;
}

.headerSpacer {
  flex: 1;
}

.caseTitle {
  font-size: 2.4rem;
  font-weight: 700;
  color: #444;
  margin-bottom: 0.5em;
  line-height: 1.15;
  padding-bottom: 0.2em;
  letter-spacing: 0.01em;
  background: none;
  border-bottom: 1.5px solid #e0e0e0;
  font-family: var(--font-family-primary);
}

.caseStatusIcon {
  font-size: 1.5rem;
  margin: 0;
  color: #374151;
  filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.1));
  transition: var(--transition);
}

.caseSubtitle {
  color: #222;
  font-size: 2rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  margin-bottom: 0.7em;
  text-align: center;
  font-family: var(--font-family-primary);
}

.subtitleIcon {
  color: #444;
  font-size: 2.1rem;
  margin-left: 0.4em;
  filter: none;
  vertical-align: middle;
  display: inline-block;
}

/* تخطيط المحتوى */
.contentGrid {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.caseInfoGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
  width: 100%;
}

/* بطاقات المعلومات - بأسلوب هادئ */
.infoGroup {
  background: var(--page-background);
  border-radius: 20px;
  padding: 2rem;
  box-shadow: 
    0 20px 60px var(--shadow-medium),
    0 10px 30px var(--shadow-light);
  transition: all var(--transition-normal);
  border: none;
  position: relative;
  overflow: hidden;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.infoGroup:hover {
  box-shadow: 
    0 25px 70px var(--shadow-medium),
    0 15px 40px var(--shadow-light);
  transform: translateY(-4px);
}

.groupTitle {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  font-size: var(--text-xl);
  font-weight: 500;
  color: var(--neutral-700);
  margin-bottom: 1.25rem;
  padding-bottom: 0.75rem;
  border-bottom: 1px solid var(--neutral-200);
}

.groupTitle svg {
  color: white;
  font-size: var(--text-lg);
  padding: 0.5rem;
  background: var(--primary-color);
  border-radius: 50%;
  box-shadow: var(--shadow-sm);
}

.titleText {
  flex: 1;
}

.infoItems {
  display: grid;
  gap: 1rem;
  flex: 1;
}

/* عناصر المعلومات - بأسلوب هادئ */
.infoItem {
  padding: 1rem;
  background: var(--page-background);
  border-radius: var(--radius-sm);
  border: none;
  transition: all var(--transition-normal);
  position: relative;
}

.infoItem:hover {
  background: var(--page-background);
  box-shadow: var(--shadow-sm);
}

.infoLabel {
  font-size: var(--text-base);
  font-weight: 600;
  color: var(--neutral-600);
  margin-bottom: 0.5rem;
  display: block;
}

.valueContainer {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 0.75rem;
}

.infoValue {
  font-size: var(--text-lg);
  font-weight: 600;
  color: var(--neutral-800);
  flex: 1;
  word-break: break-word;
  line-height: 1.5;
}

/* زر التعديل - بأسلوب هادئ */
.editIconButton {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border: none;
  border-radius: 50%;
  background: var(--primary-color);
  color: white;
  cursor: pointer;
  transition: var(--transition);
  opacity: 0;
  transform: scale(0.8);
}

.infoItem:hover .editIconButton {
  opacity: 1;
  transform: scale(1);
}

.editIconButton:hover {
  background: var(--primary-dark);
}

/* تنسيقات التعديل */
.editFieldContainer {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  background: var(--page-background);
  padding: 1rem;
  border-radius: var(--radius-sm);
  box-shadow: var(--shadow-sm);
  border: 2px solid var(--primary-color);
}

.editInput, .textareaInput {
  padding: 0.75rem;
  border: 1px solid var(--neutral-300);
  border-radius: var(--radius-sm);
  font-size: var(--text-base);
  transition: all var(--transition-normal);
  background: var(--page-background);
}

.editInput:focus, .textareaInput:focus {
  outline: none;
  border-color: var(--primary-color);
  background: var(--page-background);
  box-shadow: 0 0 0 2px rgba(120, 166, 200, 0.1);
}

.textareaInput {
  min-height: 100px;
  resize: vertical;
}

.inputError {
  border-color: var(--danger-color);
  background: #fff5f5;
}

.errorText {
  color: var(--danger-color);
  font-size: var(--text-sm);
  font-weight: 400;
  margin-top: 0.5rem;
}

.editActions {
  display: flex;
  gap: 0.75rem;
  justify-content: flex-end;
  margin-top: 1rem;
}




.addOptions {
  display: flex;
  gap: 1rem;
  justify-content: center;
  flex-wrap: wrap;
}

.followUpButton {
  padding: 0.75rem 1.25rem;
  border: none;
  border-radius: var(--border-radius);
  font-weight: 400;
  font-size: var(--text-base);
  cursor: pointer;
  transition: var(--transition);
  display: flex;
  align-items: center;
  gap: 0.5rem;
  flex: 1;
  justify-content: center;
  min-width: 180px;
  max-width: 250px;
}


 .followUpButton {
  background: var(--secondary-color);
  color: white;
}

followUpButton:hover {
  background: var(--secondary-dark);
}

.buttonIcon {
  font-size: var(--text-lg);
}

.buttonsSection {
  display: flex;
  gap: 1rem;
  justify-content: center;
  margin-top: 1.5rem;
}

.backButton, .editButton, .saveButton {
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: var(--border-radius);
  font-weight: 400;
  font-size: var(--text-base);
  cursor: pointer;
  transition: var(--transition);
  display: flex;
  align-items: center;
  gap: 0.5rem;
  min-width: 120px;
  justify-content: center;
}

.backButton {
  background: var(--neutral-500);
  color: white;
}

.backButton:hover {
  background: var(--neutral-600);
}

.editButton {
  background: var(--warning-color);
  color: white;
}

.editButton:hover {
  background: #d4b179;
}

.saveButton {
  background: var(--success-color);
  color: white;
}

.saveButton:hover {
  background: #6ca372;
}

/* رسائل الخطأ والتحميل - بأسلوب هادئ */
.errorMessage {
  text-align: center;
  color: var(--danger-color);
  font-size: var(--text-lg);
  font-weight: 400;
  background: #fff5f5;
  padding: 1.5rem;
  border-radius: var(--border-radius);
  box-shadow: var(--shadow);
  border: 1px solid #fdd;
  margin: 1.5rem 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
}

.errorIcon {
  font-size: 2rem;
  color: var(--danger-color);
}

.loadingMessage {
  text-align: center;
  color: var(--primary-color);
  font-size: var(--text-lg);
  font-weight: 400;
  background: var(--primary-light);
  padding: 1.5rem;
  border-radius: var(--border-radius);
  box-shadow: var(--shadow);
  border: 1px solid #d4e6f9;
  margin: 1.5rem 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
}

/* أيقونة التحميل الدوارة - بأسلوب هادئ */
.loadingIcon {
  display: flex;
  justify-content: center;
  margin-bottom: 1rem;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 3px solid rgba(120, 166, 200, 0.2);
  border-radius: 50%;
  border-top-color: var(--primary-color);
  animation: modernSpin 1s ease infinite;
}

/* تنسيقات إضافية للنماذج */
.addFormContainer {
  background: var(--page-background);
  border-radius: var(--radius-md);
  padding: 1.5rem;
  box-shadow: var(--shadow-sm);
  border: 2px solid var(--neutral-400);
  margin: 1.5rem 0;
  position: relative;
  overflow: hidden;
}

.addFormContainer::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: var(--primary-light);
}

/* تنسيقات الحوار */
.promptDialog {
  background: var(--page-background);
  border-radius: var(--radius-md);
  padding: 1.5rem;
  box-shadow: var(--shadow-sm);
  border: 2px solid var(--primary-color);
  margin: 1.5rem 0;
  text-align: center;
}

.promptDialog h3 {
  color: var(--neutral-700);
  font-size: var(--text-xl);
  font-weight: 500;
  margin-bottom: 1rem;
}

.previewDialog {
  background: var(--page-background);
  border-radius: var(--radius-md);
  padding: 1rem;
  margin: 1rem 0;
  text-align: right;
  border: 2px solid var(--neutral-400);
}

.previewDialog h4 {
  color: var(--neutral-700);
  font-size: var(--text-lg);
  font-weight: 500;
  margin-bottom: 0.75rem;
}

.previewDialog ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.previewDialog li {
  margin-bottom: 0.5rem;
  padding-right: 1rem;
  position: relative;
}

.previewDialog li::before {
  content: '•';
  position: absolute;
  right: 0;
  color: var(--primary-color);
}

/* تأثيرات الحركة - بأسلوب هادئ */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.headerSection, .infoGroup, .actionsSection {
  animation: fadeIn 0.5s ease;
}

/* تحسينات للأجهزة اللوحية */
@media (max-width: 1024px) {
  .topLeftExpertBox {
    max-width: 300px;
    left: 15px;
    top: 15px;
  }

  .twoColumnLayout {
    grid-template-columns: 1fr 300px;
    gap: 1.5rem;
  }
}

/* تحسينات للأجهزة المحمولة */
@media (max-width: 768px) {
  .topLeftExpertBox {
    position: static;
    max-width: none;
    margin-bottom: 0;
    margin-top: 1rem;
    left: auto;
    top: auto;
    z-index: auto;
    order: 2;
  }

  .mainContainer {
    padding: 1rem;
    gap: 1rem;
  }

  .headerSection {
    padding: 1.5rem;
    display: flex;
    flex-direction: column;
  }

  .headerContent {
    order: 1;
  }

  .headerLayout {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }

  .titleSection {
    width: 100%;
    text-align: center;
    align-items: center;
  }

  .headerExpertSection {
    width: 100%;
    max-width: none;
  }

  .caseTitle {
    font-size: var(--text-2xl);
    text-align: center;
  }

  .caseSubtitle {
    font-size: var(--text-base);
    justify-content: center;
    text-align: center;
  }

  .caseInfoGrid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .infoGroup {
    padding: 1rem;
  }

  .groupTitle {
    font-size: var(--text-lg);
    margin-bottom: 1rem;
  }

  .infoItem {
    padding: 0.75rem;
  }

  .addOptions {
    flex-direction: column;
    align-items: stretch;
  }

  .addDeferralButton, .addActionOptionButton, .followUpButton {
    max-width: none;
    width: 100%;
  }

  .buttonsSection {
    flex-direction: column;
    align-items: stretch;
  }

  .backButton, .editButton, .saveButton {
    width: 100%;
  }
}

@media (max-width: 480px) {
  .topLeftExpertBox {
    position: static;
    max-width: none;
    margin-bottom: 0;
    margin-top: 0.75rem;
    left: auto;
    top: auto;
    order: 2;
  }

  .mainContainer {
    padding: 0.75rem;
  }

  .headerSection {
    padding: 1rem;
    display: flex;
    flex-direction: column;
  }

  .headerContent {
    order: 1;
  }

  .caseTitle {
    font-size: var(--text-xl);
  }

  .caseSubtitle {
    font-size: 2rem;
    line-height: 1.2;
  }
  .subtitleIcon {
    font-size: 1.5rem;
    vertical-align: middle;
    margin-left: 0.3em;
  }

  .infoGroup {
    padding: 0.75rem;
  }

  .groupTitle {
    font-size: var(--text-base);
    gap: 0.5rem;
    margin-bottom: 0.75rem;
  }

  .groupTitle svg {
    font-size: var(--text-base);
    padding: 0.4rem;
  }

  .infoItem {
    padding: 0.5rem;
  }

  .infoLabel {
    font-size: var(--text-xs);
  }

  .infoValue {
    font-size: var(--text-sm);
  }

  .editIconButton {
    width: 28px;
    height: 28px;
  }

  .addDeferralButton, .addActionOptionButton, .followUpButton {
    padding: 0.5rem 0.75rem;
    font-size: var(--text-sm);
  }

  .buttonIcon {
    font-size: var(--text-base);
  }
}

/* أنماط النوافذ المنبثقة للإحالة */
.transferConfirmationOverlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.transferConfirmationDialog {
  background: white;
  border-radius: 12px;
  padding: 2rem;
  max-width: 500px;
  width: 90%;
  max-height: 80vh;
  overflow-y: auto;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.transferConfirmationIcon {
  text-align: center;
  margin-bottom: 1rem;
  font-size: 3rem;
  color: #2196F3;
}

.transferConfirmationTitle {
  text-align: center;
  margin-bottom: 1rem;
  color: #333;
  font-size: 1.5rem;
  font-weight: bold;
}

.transferConfirmationMessage {
  text-align: center;
  margin-bottom: 2rem;
  color: #666;
  line-height: 1.6;
}

.transferConfirmationActions {
  display: flex;
  gap: 1rem;
  justify-content: center;
  margin-top: 2rem;
}

.transferConfirmButton {
  background: #4CAF50;
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  cursor: pointer;
  font-size: 1rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  transition: background-color 0.3s;
}

.transferConfirmButton:hover {
  background: #45a049;
}

.transferConfirmButton:disabled {
  background: #ccc;
  cursor: not-allowed;
}

.transferCancelButton {
  background: #f44336;
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  cursor: pointer;
  font-size: 1rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  transition: background-color 0.3s;
}

.transferCancelButton:hover {
  background: #da190b;
}

/* أنماط خيارات منطوق الأحكام */
.verdictOptionsContainer {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.verdictOption {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  border: 2px solid #e0e0e0;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  background: white;
}

.verdictOption:hover {
  border-color: #2196F3;
  background: #f5f5f5;
}

.verdictOption.selectedOption {
  border-color: #2196F3;
  background: #e3f2fd;
  color: #1976d2;
}

/* أنماط أزرار منطوق الأحكام الجديدة */
.verdictButtonsContainer {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.verdictButton {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.75rem;
  padding: 1rem 1.5rem;
  border: 2px solid #e0e0e0;
  border-radius: 8px;
  background: white;
  color: #333;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.verdictButton:hover {
  border-color: #2196F3;
  background: #f8f9fa;
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.verdictButton:active {
  transform: translateY(0);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.verdictButton.selectedButton {
  border-color: #2196F3;
  background: #2196F3;
  color: white;
  box-shadow: 0 4px 12px rgba(33, 150, 243, 0.3);
}

.verdictButton.selectedButton:hover {
  background: #1976d2;
  border-color: #1976d2;
}

.verdictButtonIcon {
  font-size: 1.1rem;
}

.verdictIcon {
  font-size: 1.5rem;
  color: #2196F3;
}

.selectedOption .verdictIcon {
  color: #1976d2;
}

.textareaInput {
  min-height: 80px;
  resize: vertical;
  font-family: inherit;
}

/* أنماط إضافية للنوافذ المنبثقة */
.statusOptionsContainer {
  margin-bottom: 1.5rem;
}

.dateInputContainer {
  margin-bottom: 1rem;
}

.dateLabel {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
  color: #333;
}

.dateInput {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid #ddd;
  border-radius: 6px;
  font-size: 1rem;
  transition: border-color 0.3s;
}

.dateInput:focus {
  outline: none;
  border-color: #2196F3;
  box-shadow: 0 0 0 2px rgba(33, 150, 243, 0.1);
}

.errorMessage {
  background: #ffebee;
  color: #c62828;
  padding: 0.75rem;
  border-radius: 6px;
  margin-top: 1rem;
  border: 1px solid #ffcdd2;
  font-size: 0.9rem;
  line-height: 1.4;
  white-space: pre-line;
}
.profileInfoWithImage {
  display: flex;
  align-items: flex-start;
  gap: 18px;
  font-size: 1.07rem;
  color: #444;
  line-height: 1.9;
  margin: 0 0 18px 0;
  direction: rtl;
  text-align: right;
}

@media (max-width: 600px) {
  .profileInfoWithImage {
    flex-direction: column-reverse;
    align-items: center;
    text-align: center;
    gap: 16px;
  }
}
@media (max-width: 600px) {
  .mobileImageBelow {
    display: block !important;
    margin-top: 18px !important;
    margin-bottom: 0 !important;
    width: 100px !important;
    height: 100px !important;
    order: 2 !important;
    align-self: center !important;
  }
  .profileInfoWithImage {
    flex-direction: column !important;
    align-items: center !important;
    text-align: center !important;
  }
}
.profileHeaderImage {
  width: 100px;
  height: 100px;
  object-fit: cover;
}
@media (max-width: 768px) {
  .profileHeaderImage {
    width: 150px !important;
    height: 150px !important;
  }
}
@media (max-width: 600px) {
  .centerOnMobile {
    display: block;
    margin-left: auto !important;
    margin-right: auto !important;
    float: none !important;
  }
}
