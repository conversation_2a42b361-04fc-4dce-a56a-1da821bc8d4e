import{r as u,j as t}from"./index-Bd3HN_hN.js";import{f as L,d as E}from"./index-DQWGoQ3q.js";import{s as n}from"./ReportDetails.module-BodVG5Gz.js";import"./iconBase-BmtohqY9.js";const O=["عاجل","متوسط","غير عاجل"],Q=({currentUser:P,caseItem:w,deferrals:D,actions:m,setActions:R,history:H,setHistory:U,onSave:y,onCancel:C,templateActions:q=[]})=>{const[j,B]=u.useState(""),[f,h]=u.useState(""),[k,F]=u.useState("متوسط"),[o,T]=u.useState(""),[r,$]=u.useState(""),[l,z]=u.useState(""),[v,c]=u.useState(null);u.useEffect(()=>{if(l==="deferral"&&o){const e=D.find(a=>a.id===o);if(e&&e.date){const a=new Date(e.date);a.setDate(a.getDate()-1),h(a.toISOString().split("T")[0])}}else if(l==="action"&&r){const e=m.find(a=>a.id===r);if(e&&e.deadline){const a=new Date(e.deadline);if(a.setDate(a.getDate()+1),h(a.toISOString().split("T")[0]),e.linkedDeferralId){const p=D.find(d=>d.id===e.linkedDeferralId);if(p&&p.date){const d=new Date(p.date);d.setDate(d.getDate()-1),a>=d&&(c("موعد الإجراء يجب أن يكون قبل تاريخ التأجيل المرتبط بالإجراء المتسلسل بيوم واحد."),h(""))}}}}else(l==="custom"||!l)&&h(new Date().toISOString().split("T")[0])},[l,o,r,D,m]);const N=(e,a,p)=>{const d=new Date(e);if(a){const i=D.find(s=>s.id===a);if(i&&i.date){new Date(i.date);const s=new Date(i.date);if(s.setDate(s.getDate()-1),d.toDateString()!==s.toDateString())return c("موعد الإجراء يجب أن يكون قبل تاريخ التأجيل بيوم واحد بالضبط."),!1}}if(p){const i=m.find(s=>s.id===p);if(i&&i.deadline){const s=new Date(i.deadline),A=new Date(i.deadline);if(A.setDate(A.getDate()+1),d<=s)return c("موعد الإجراء يجب أن يكون بعد تاريخ الإجراء المرتبط."),!1;if(i.linkedDeferralId){const x=D.find(g=>g.id===i.linkedDeferralId);if(x&&x.date){const g=new Date(x.date),S=new Date(x.date);if(S.setDate(S.getDate()-1),d>=g)return c("موعد الإجراء يجب أن يكون قبل تاريخ التأجيل المرتبط بالإجراء المتسلسل بيوم واحد."),!1}}}}if(!a&&!p){const i=D.find(s=>!s.isDeleted);if(i&&i.date){new Date(i.date);const s=new Date(i.date);if(s.setDate(s.getDate()-1),d>s)return c("موعد الإجراء يجب أن يكون قبل تاريخ التأجيل بيوم واحد على الأقل."),!1}}return c(null),!0},b=(D||[]).map((e,a)=>({id:e.id||`${w.id}-defer-${a}`,content:e.content||(e.date&&e.reasons?`${e.date} - ${e.reasons.join("، ")}`:`تأجيل ${a+1}`),isDeleted:e.isDeleted||!1,date:e.date})).filter(e=>!e.isDeleted),I=(m||[]).map(e=>({id:e.id,description:e.description||"إجراء بدون وصف",isDeleted:e.isDeleted||!1,deadline:e.deadline,linkedDeferralId:e.linkedDeferralId})).filter(e=>!e.isDeleted);return t.jsxs("div",{className:n.addActionForm,children:[v&&t.jsx("div",{style:{color:"red",marginBottom:"15px"},children:v}),t.jsxs("div",{className:n.actionField,children:[t.jsx("label",{children:"إذا أردت ربط الإجراء، اختر أحد الخيارات:"}),t.jsxs("div",{className:n.linkButtons,children:[t.jsx("button",{type:"button",className:`${n.linkButton} ${l==="deferral"?n.linkButtonActive:""}`,onClick:()=>handleLinkTypeChange("deferral"),children:"ربط بتأجيل"}),t.jsx("button",{type:"button",className:`${n.linkButton} ${l==="action"?n.linkButtonActive:""}`,onClick:()=>handleLinkTypeChange("action"),children:"ربط بإجراء"}),t.jsx("button",{type:"button",className:`${n.linkButton} ${l==="custom"?n.linkButtonActive:""}`,onClick:()=>handleLinkTypeChange("custom"),children:"تاريخ آخر"})]}),l==="deferral"&&t.jsxs(t.Fragment,{children:[t.jsxs("select",{value:o,onChange:e=>T(e.target.value),className:n.actionInput,children:[t.jsx("option",{value:"",children:"اختر تأجيل"}),b.length>0?b.map(e=>t.jsx("option",{value:e.id,children:e.content},e.id)):t.jsx("option",{disabled:!0,children:"لا توجد تأجيلات متاحة"})]}),o&&t.jsxs("div",{className:n.actionField,children:[t.jsx("label",{children:"موعد الإجراء (محدد تلقائيًا):"}),t.jsx("input",{type:"date",value:f,className:n.actionInput,disabled:!0})]})]}),l==="action"&&t.jsxs(t.Fragment,{children:[t.jsxs("select",{value:r,onChange:e=>$(e.target.value),className:n.actionInput,children:[t.jsx("option",{value:"",children:"اختر إجراء"}),I.length>0?I.map(e=>t.jsx("option",{value:e.id,children:e.description},e.id)):t.jsx("option",{disabled:!0,children:"لا توجد إجراءات متاحة"})]}),r&&t.jsxs("div",{className:n.actionField,children:[t.jsx("label",{children:"موعد الإجراء (محدد تلقائيًا):"}),t.jsx("input",{type:"date",value:f,className:n.actionInput,disabled:!0})]})]}),(l==="custom"||!l)&&t.jsx("input",{type:"date",value:f,onChange:e=>{const a=e.target.value;N(a,o,r)&&h(a)},className:n.actionInput,min:new Date().toISOString().split("T")[0]})]}),t.jsxs("div",{className:n.actionField,children:[t.jsx("label",{children:"وصف الإجراء:"}),t.jsx("input",{type:"text",value:j,onChange:e=>B(e.target.value),placeholder:"أدخل وصف الإجراء",className:n.actionInput})]}),t.jsxs("div",{className:n.actionField,children:[t.jsx("label",{children:"الأولوية:"}),t.jsx("select",{value:k,onChange:e=>F(e.target.value),className:n.actionInput,children:O.map(e=>t.jsx("option",{value:e,children:e},e))})]}),t.jsxs("div",{className:n.actionFormButtons,children:[t.jsxs("button",{onClick:()=>{if(!j||!f){c("يرجى ملء جميع الحقول المطلوبة.");return}N(f,o,r)&&y(j,f,k,l,o,r,c)},className:n.addActionButton,disabled:!!v,children:[t.jsx(L,{className:n.buttonIcon}),t.jsx("span",{children:"إضافة الإجراء"})]}),t.jsxs("button",{onClick:()=>{C(),c(null)},className:n.cancelButton,children:[t.jsx(E,{className:n.buttonIcon}),t.jsx("span",{children:"إلغاء"})]})]})]})};export{Q as default};
