import React, { useState, useEffect } from 'react';
import { FaChevronDown, FaChevronUp, FaGavel, FaExternalLinkAlt } from 'react-icons/fa';
import { useNavigate } from 'react-router-dom';
import { getAllCases } from '../services/StorageService';
import styles from './PreviousDegreeBox.module.css';

const PreviousDegreeBox = ({ caseData, currentUser }) => {
  const [isExpanded, setIsExpanded] = useState(false);
  const [relatedCase, setRelatedCase] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const navigate = useNavigate();

  // تحويل اسم الدرجة إلى وصف واضح
  const getDegreeDescription = (degree) => {
    if (!degree) return '—';

    const degreeMap = {
      // الدرجة الأولى
      'ابتدائي': 'ملف أول درجة',
      'ابتدائية': 'ملف أول درجة',
      'جنايات': 'ملف أول درجة',
      'جنح': 'ملف أول درجة',
      'مخالفات': 'ملف أول درجة',
      'أحوال شخصية': 'ملف أول درجة',
      'تجاري': 'ملف أول درجة',
      'تجارية': 'ملف أول درجة',
      'عمالي': 'ملف أول درجة',
      'عمالية': 'ملف أول درجة',
      'إداري': 'ملف أول درجة',
      'إدارية': 'ملف أول درجة',
      'مدني': 'ملف أول درجة',
      'مدنية': 'ملف أول درجة',

      // الدرجة الثانية
      'استئناف': 'ملف ثاني درجة',
      'استئنافي': 'ملف ثاني درجة',
      'استئنافية': 'ملف ثاني درجة',
      'عليا إدارية': 'ملف ثاني درجة',
      'محكمة الاستئناف': 'ملف ثاني درجة',

      // الدرجة الثالثة
      'نقض': 'ملف ثالث درجة',
      'محكمة النقض': 'ملف ثالث درجة',
      'دستورية عليا': 'ملف ثالث درجة',
      'المحكمة الدستورية العليا': 'ملف ثالث درجة'
    };

    return degreeMap[degree] || degree;
  };

  // تحديد نوع العلاقة ومعلومات القضية المرتبطة
  const getRelationshipInfo = () => {
    // إذا كانت القضية الحالية محولة من قضية أخرى (لديها originalCaseId)
    if (caseData?.originalCaseId && caseData?.originalCaseDegree) {
      return {
        type: 'previous', // القضية المرتبطة هي القضية السابقة
        relatedCaseId: caseData.originalCaseId,
        relatedCaseDegree: caseData.originalCaseDegree,
        relatedCaseNumber: caseData.originalCaseNumber,
        title: getDegreeDescription(caseData.originalCaseDegree),
        description: 'القضية الأصلية'
      };
    }

    // إذا كانت القضية الحالية مخفية (تم تحويلها لقضية جديدة)
    if (caseData?.isHidden) {
      return {
        type: 'next', // القضية المرتبطة هي القضية الجديدة
        relatedCaseId: null, // سنبحث عنها
        relatedCaseDegree: null, // سنحددها لاحقاً
        relatedCaseNumber: null,
        title: 'القضية الجديدة',
        description: 'القضية المحولة إليها'
      };
    }

    return null;
  };

  const relationshipInfo = getRelationshipInfo();

  // البحث عن القضية الجديدة إذا كانت القضية الحالية مخفية
  useEffect(() => {
    const findRelatedCase = async () => {
      if (!relationshipInfo || !currentUser) return;

      if (relationshipInfo.type === 'next') {
        setIsLoading(true);
        try {
          // البحث في جميع القضايا (بما في ذلك المخفية) عن القضية التي تحتوي على originalCaseId = caseData.id
          const allCases = await getAllCases(currentUser.uid);

          const newCase = allCases.find(c =>
            c.originalCaseId === caseData.id && !c.isHidden
          );

          if (newCase) {
            setRelatedCase(newCase);
          }
        } catch (error) {
          console.error('Error finding related case:', error);
        } finally {
          setIsLoading(false);
        }
      }
    };

    findRelatedCase();
  }, [caseData?.id, currentUser?.uid, relationshipInfo?.type]);

  // إخفاء المكون إذا لم توجد علاقة
  if (!relationshipInfo) {
    return null;
  }

  const handleViewRelatedCase = () => {
    if (relationshipInfo.type === 'previous') {
      // التوجه للقضية السابقة
      navigate(`/case-details/${relationshipInfo.relatedCaseId}`);
    } else if (relationshipInfo.type === 'next' && relatedCase) {
      // التوجه للقضية الجديدة
      navigate(`/case-details/${relatedCase.id}`);
    }
  };

  const toggleExpanded = () => {
    setIsExpanded(!isExpanded);
  };

  // تحديد البيانات المعروضة
  const getDisplayData = () => {
    if (relationshipInfo.type === 'previous') {
      return {
        title: relationshipInfo.title,
        caseNumber: relationshipInfo.relatedCaseNumber || '—',
        degree: getDegreeDescription(relationshipInfo.relatedCaseDegree),
        transferDate: caseData.createdAt ? new Date(caseData.createdAt).toLocaleDateString('ar-EG') : '—',
        buttonText: 'عرض تفاصيل القضية الأصلية'
      };
    } else if (relationshipInfo.type === 'next') {
      return {
        title: relatedCase ? getDegreeDescription(relatedCase.caseDegree) || 'القضية الجديدة' : 'القضية الجديدة',
        caseNumber: relatedCase ? relatedCase.fullCaseNumber || '—' : (isLoading ? 'جاري البحث...' : 'غير موجودة'),
        degree: relatedCase ? getDegreeDescription(relatedCase.caseDegree) || '—' : '—',
        transferDate: relatedCase ? new Date(relatedCase.createdAt).toLocaleDateString('ar-EG') : '—',
        buttonText: 'عرض تفاصيل القضية الجديدة'
      };
    }
    return null;
  };

  const displayData = getDisplayData();
  if (!displayData) return null;

  return (
    <div className={styles.previousDegreeBox}>
      {/* العنوان */}
      <div className={styles.header} onClick={toggleExpanded}>
        <div className={styles.titleSection}>
          <FaGavel className={styles.icon} />
          <span className={styles.title}>{displayData.title}</span>
        </div>
        <div className={styles.expandIcon}>
          {isExpanded ? <FaChevronUp /> : <FaChevronDown />}
        </div>
      </div>

      {/* المحتوى */}
      {isExpanded && (
        <div className={styles.content}>
          <div className={styles.infoRow}>
            <div className={styles.rowIcon}>
              <FaGavel />
            </div>
            <span className={styles.label}>رقم القضية:</span>
            <span className={styles.value}>{displayData.caseNumber}</span>
          </div>

          <div className={styles.infoRow}>
            <div className={styles.rowIcon}>
              <FaExternalLinkAlt />
            </div>
            <span className={styles.label}>الدرجة:</span>
            <span className={styles.value}>{displayData.degree}</span>
          </div>

          <div className={styles.infoRow}>
            <div className={styles.rowIcon}>
              <FaExternalLinkAlt />
            </div>
            <span className={styles.label}>تاريخ التحويل:</span>
            <span className={styles.value}>{displayData.transferDate}</span>
          </div>

          {/* زر عرض التفاصيل */}
          <div className={styles.actionRow}>
            <button
              className={styles.viewButton}
              onClick={handleViewRelatedCase}
              title={displayData.buttonText}
              disabled={relationshipInfo.type === 'next' && !relatedCase && !isLoading}
            >
              <FaExternalLinkAlt />
              {displayData.buttonText}
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

export default PreviousDegreeBox;
