rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // القاعدة العامة: افتراضياً ممنوع أي قراءة أو كتابة على أي مستند
    match /{document=**} {
      allow read, write: if false;
    }

    // قواعد المستخدمين
    match /users/{userId} {
      allow read: if request.auth != null;
      allow write: if request.auth != null && request.auth.uid == userId;
    }

    // قواعد القضايا
    match /cases/{caseId} {
      // السماح بالقراءة للمستخدمين المصادق عليهم
      allow read: if request.auth != null;
      
      // السماح بالإنشاء للمستخدمين المصادق عليهم
      allow create: if request.auth != null;
      
      // السماح بالتحديث والحذف لمالك القضية أو مدير أو محرر المجموعة
      allow update, delete: if request.auth != null && 
        (resource.data.createdBy == request.auth.uid ||
         (resource.data.groupId != null && 
          (
            get(/databases/$(database)/documents/groups/$(resource.data.groupId)).data.managers[request.auth.uid] == true ||
            get(/databases/$(database)/documents/groups/$(resource.data.groupId)).data.editors[request.auth.uid] == true
          )
         )
        );
    }

    // قواعد المجموعات
    match /groups/{groupId} {
      allow read: if request.auth != null;
      allow create: if request.auth != null;
      allow update, delete: if request.auth != null && 
        (resource.data.createdBy == request.auth.uid || 
         resource.data.managers[request.auth.uid] == true);
    }

    // قواعد الأعضاء
    match /members/{memberId} {
      allow read: if request.auth != null;
      allow create: if request.auth != null;
      allow update, delete: if request.auth != null && 
        (get(/databases/$(database)/documents/groups/$(resource.data.groupId)).data.createdBy == request.auth.uid ||
         get(/databases/$(database)/documents/groups/$(resource.data.groupId)).data.managers[request.auth.uid] == true);
    }

    // قواعد مجموعة 'deferralTemplates'
    match /deferralTemplates/{templateId} {
      allow create: if request.auth != null && request.resource.data.userId == request.auth.uid;
      allow read: if request.auth != null && resource.data.userId == request.auth.uid;
      allow update: if request.auth != null && resource.data.userId == request.auth.uid;
      allow delete: if request.auth != null && resource.data.userId == request.auth.uid;
      allow list: if request.auth != null;
    }

    match /stats/{docId} {
      allow read: if request.auth != null;
      allow write: if request.auth != null && request.auth.uid == null;
    }

    // قواعد خاصة بالقضايا المرتبطة بالمجموعات
    match /groupCases/{caseId} {
      // السماح بالقراءة لأي عضو في المجموعة
      allow read: if request.auth != null &&
                   exists(/databases/$(database)/documents/members/$(request.auth.uid + '_' + resource.data.groupId));

      // السماح بالإنشاء والتحديث والحذف لمنشئ المجموعة والمديرين
      allow create, update, delete: if request.auth != null &&
                                     (get(/databases/$(database)/documents/groups/$(resource.data.groupId)).data.createdBy == request.auth.uid ||
                                      get(/databases/$(database)/documents/groups/$(resource.data.groupId)).data.managers[request.auth.uid] == true);
    }

    // قواعد خاصة بالإشعارات المرتبطة بالمجموعات
    match /groupNotifications/{notificationId} {
      // السماح بالقراءة فقط للمستخدم المرسل إليه الإشعار
      allow read: if request.auth != null &&
                   resource.data.userId == request.auth.uid;

      // السماح بالكتابة لمنشئ المجموعة والمديرين
      allow write: if request.auth != null &&
                    (get(/databases/$(database)/documents/groups/$(resource.data.groupId)).data.createdBy == request.auth.uid ||
                     get(/databases/$(database)/documents/groups/$(resource.data.groupId)).data.managers[request.auth.uid] == true);
    }

    // قواعد خاصة بالمهام في المجموعات
    match /groupTasks/{taskId} {
      // السماح بالقراءة لأي عضو في المجموعة
      allow read: if request.auth != null &&
                   exists(/databases/$(database)/documents/members/$(request.auth.uid + '_' + resource.data.groupId));

      // السماح بالإنشاء والتحديث والحذف لمنشئ المجموعة والمديرين
      allow create, update, delete: if request.auth != null &&
                                     (get(/databases/$(database)/documents/groups/$(resource.data.groupId)).data.createdBy == request.auth.uid ||
                                      get(/databases/$(database)/documents/groups/$(resource.data.groupId)).data.managers[request.auth.uid] == true);
    }

    // قواعد التقارير
    match /reports/{reportId} {
      // السماح بالقراءة للمستخدمين المصادق عليهم
      allow read: if request.auth != null;
      
      // السماح بالإنشاء للمستخدمين المصادق عليهم
      allow create: if request.auth != null;
      
      // السماح بالتحديث والحذف لمالك التقرير أو مدير المجموعة
      allow update, delete: if request.auth != null && 
        (resource.data.createdBy == request.auth.uid ||
         (resource.data.groupId != null && 
          get(/databases/$(database)/documents/groups/$(resource.data.groupId)).data.managers[request.auth.uid] == true));
    }

    // قواعد نظام توزيع المهام
    match /taskAssignments/{assignmentId} {
      // السماح بالقراءة للمستخدمين المصادق عليهم
      allow read: if request.auth != null;
      
      // السماح بالإنشاء للمستخدمين المصادق عليهم
      allow create: if request.auth != null && request.auth.uid != null;
      
      // السماح بالتحديث والحذف للمستخدمين المصادق عليهم
      allow update, delete: if request.auth != null && request.auth.uid != null;
    }
  }
}