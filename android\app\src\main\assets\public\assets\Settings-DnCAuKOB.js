import{r as p,j as t,c as N,d as j,q as F,w as q,g as w,f as b,i as L,a as z,D}from"./index-Bd3HN_hN.js";import{l as k,f as R,c as $,K as Q,L as K}from"./index-DQWGoQ3q.js";import{T as O}from"./TopBar-DgdxUhb0.js";import"./iconBase-BmtohqY9.js";const P="_deferralTemplates_komlq_1",G="_content_komlq_25",Y="_templateForm_komlq_39",H="_formField_komlq_61",J="_input_komlq_83",M="_actionRow_komlq_113",U="_notificationField_komlq_133",V="_removeButton_komlq_145",W="_addActionButton_komlq_171",X="_saveButton_komlq_197",Z="_templatesList_komlq_225",tt="_templateItem_komlq_245",et="_templateActions_komlq_261",st="_editButton_komlq_273",at="_deleteButton_komlq_291",a={deferralTemplates:P,content:G,templateForm:Y,formField:H,input:J,actionRow:M,notificationField:U,removeButton:V,addActionButton:W,saveButton:X,templatesList:Z,templateItem:tt,templateActions:et,editButton:st,deleteButton:at},u={SEQUENTIAL:"تسلسلي",DIRECT:"مباشر"},nt=({currentUser:c})=>{const[r,h]=p.useState([]),[s,d]=p.useState({reason:"",actions:[{description:"",linkage:{type:u.SEQUENTIAL}}]}),[x,g]=p.useState(null),[y,m]=p.useState(!0),[T,f]=p.useState(null),v=async()=>{if(!c||!c.uid){m(!1),f("لا يوجد مستخدم مسجل الدخول.");return}try{m(!0);const e=N(j,"deferralTemplates"),n=F(e,q("userId","==",c.uid)),l=(await w(n)).docs.map(_=>({id:_.id,..._.data()}));h(l),f(null)}catch(e){f("خطأ في جلب القوالب: "+e.message)}finally{m(!1)}};p.useEffect(()=>{v()},[c]);const B=()=>{s.actions.length>=3||d({...s,actions:[...s.actions,{description:"",linkage:{type:u.SEQUENTIAL}}]})},A=(e,n,o)=>{const l=[...s.actions];l[e]={...l[e],[n]:o},d({...s,actions:l})},I=e=>{d({...s,actions:s.actions.filter((n,o)=>o!==e)})},S=async()=>{if(!s.reason||s.actions.some(e=>!e.description)){alert("يرجى ملء جميع الحقول المطلوبة");return}try{if(m(!0),x){const e=b(j,"deferralTemplates",x);await L(e,{reason:s.reason,actions:s.actions,updatedAt:new Date().toISOString()}),alert("تم تعديل القالب بنجاح")}else await z(N(j,"deferralTemplates"),{userId:c.uid,reason:s.reason,actions:s.actions,createdAt:new Date().toISOString()}),alert("تم إضافة القالب بنجاح");d({reason:"",actions:[{description:"",linkage:{type:u.SEQUENTIAL}}]}),g(null),v()}catch(e){alert("خطأ في حفظ القالب: "+e.message)}finally{m(!1)}},E=e=>{d({reason:e.reason,actions:e.actions.map(n=>{var o,l;return{...n,linkage:{type:((o=n.linkage)==null?void 0:o.type)||((l=n.notification)==null?void 0:l.type)||u.SEQUENTIAL}}})}),g(e.id)},C=async e=>{try{m(!0),await D(b(j,"deferralTemplates",e)),v(),alert("تم حذف القالب بنجاح")}catch(n){alert("خطأ في حذف القالب: "+n.message)}finally{m(!1)}};return y?t.jsx("div",{children:"جاري تحميل القوالب..."}):T?t.jsx("div",{style:{color:"red"},children:T}):t.jsx("div",{className:a.deferralTemplates,children:t.jsxs("div",{className:a.content,children:[t.jsxs("div",{className:a.templateForm,children:[t.jsxs("div",{className:a.formField,children:[t.jsx("label",{children:"سبب التأجيل:"}),t.jsx("input",{type:"text",value:s.reason,onChange:e=>d({...s,reason:e.target.value}),placeholder:"أدخل سبب التأجيل",className:a.input})]}),t.jsxs("div",{className:a.formField,children:[t.jsx("label",{children:"الإجراءات المرتبطة:"}),s.actions.map((e,n)=>t.jsxs("div",{className:a.actionRow,children:[t.jsx("input",{type:"text",value:e.description,onChange:o=>A(n,"description",o.target.value),placeholder:"وصف الإجراء",className:a.input}),s.actions.length>1&&n===0&&t.jsxs("div",{className:a.notificationField,children:[t.jsxs("label",{children:["طريقة الارتباط: ",t.jsx("span",{style:{fontSize:"0.9em",color:"#666"},children:"(تسلسلي: كل إجراء لازم يخلّص قبل اللي بعده - مباشر: كل إجراء مستقل لوحده)"})]}),t.jsxs("select",{value:e.linkage.type,onChange:o=>{const l=s.actions.map((_,ht)=>({..._,linkage:{..._.linkage,type:o.target.value}}));d({...s,actions:l})},className:a.input,children:[t.jsx("option",{value:u.SEQUENTIAL,children:"تسلسلي"}),t.jsx("option",{value:u.DIRECT,children:"مباشر"})]})]}),s.actions.length>1&&t.jsx("button",{onClick:()=>I(n),className:a.removeButton,children:t.jsx(k,{})})]},n)),s.actions.length<3&&t.jsxs("button",{onClick:B,className:a.addActionButton,children:[t.jsx(R,{})," إضافة إجراء"]})]}),t.jsxs("button",{onClick:S,className:a.saveButton,children:[t.jsx($,{})," ",x?"حفظ التعديلات":"حفظ القالب"]})]}),t.jsxs("div",{className:a.templatesList,children:[t.jsx("h3",{children:"القوالب المحفوظة:"}),r.length===0?t.jsx("p",{children:"لا توجد قوالب محفوظة بعد."}):r.map(e=>t.jsxs("div",{className:a.templateItem,children:[t.jsxs("div",{children:[t.jsx("strong",{children:"السبب:"})," ",e.reason]}),t.jsxs("div",{children:[t.jsx("strong",{children:"الإجراءات:"}),t.jsx("ul",{children:e.actions.map((n,o)=>t.jsxs("li",{children:[n.description," -",o===0&&`${e.actions[0].linkage.type}`]},o))})]}),t.jsxs("div",{className:a.templateActions,children:[t.jsx("button",{onClick:()=>E(e),className:a.editButton,children:"تعديل"}),t.jsx("button",{onClick:()=>C(e.id),className:a.deleteButton,children:t.jsx(k,{})})]})]},e.id))]})]})})},ot="_settingsContainer_1ez11_1",it="_content_1ez11_27",lt="_title_1ez11_41",ct="_icon_1ez11_63",rt="_tabs_1ez11_71",dt="_tabButton_1ez11_93",mt="_activeTab_1ez11_127",pt="_tabIcon_1ez11_139",ut="_tabContent_1ez11_147",_t="_section_1ez11_165",i={settingsContainer:ot,content:it,title:lt,icon:ct,tabs:rt,tabButton:dt,activeTab:mt,tabIcon:pt,tabContent:ut,section:_t},gt=({currentUser:c})=>{const[r,h]=p.useState("general");return c?t.jsxs("div",{className:i.settingsContainer,children:[t.jsx(O,{currentUser:c}),t.jsxs("div",{className:i.content,children:[t.jsxs("h1",{className:i.title,children:[t.jsx(Q,{className:i.icon}),"الإعدادات"]}),t.jsxs("div",{className:i.tabs,children:[t.jsx("button",{className:`${i.tabButton} ${r==="general"?i.activeTab:""}`,onClick:()=>h("general"),children:"إعدادات عامة"}),t.jsxs("button",{className:`${i.tabButton} ${r==="deferralTemplates"?i.activeTab:""}`,onClick:()=>h("deferralTemplates"),children:[t.jsx(K,{className:i.tabIcon}),"قوالب التأجيلات"]})]}),t.jsxs("div",{className:i.tabContent,children:[r==="general"&&t.jsxs("div",{className:i.section,children:[t.jsx("h2",{children:"إعدادات عامة"}),t.jsx("p",{children:"لم يتم إضافة إعدادات عامة بعد. يمكنك إضافة إعدادات مثل تغيير كلمة المرور أو إعدادات الحساب هنا."})]}),r==="deferralTemplates"&&t.jsx("div",{className:i.section,children:t.jsx(nt,{currentUser:c})})]})]})]}):t.jsx("div",{children:"جاري التحقق من حالة المستخدم..."})};export{gt as default};
