// Enhanced Form Field Component with Validation
import React from 'react';

const FormField = ({ 
  label, 
  name, 
  type = 'text',
  value, 
  onChange, 
  onBlur,
  placeholder,
  required = false,
  disabled = false,
  options = [],
  error = null,
  success = false,
  className = '',
  helperText = '',
  rows = 4,
  maxLength = null,
  min = null,
  max = null,
  step = null,
  children
}) => {
  const fieldId = `field-${name}`;
  const hasError = !!error;
  const isSelect = type === 'select';
  const isTextarea = type === 'textarea';
  
  const handleChange = (e) => {
    if (onChange) {
      onChange(name, e.target.value);
    }
  };

  const handleBlur = () => {
    if (onBlur) {
      onBlur(name);
    }
  };

  const getFieldClassName = () => {
    let classes = 'input-field';
    if (className) classes += ` ${className}`;
    if (hasError) classes += ' error';
    if (success && !hasError) classes += ' success';
    return classes;
  };

  const getInputClassName = () => {
    let classes = '';
    if (hasError) classes += ' error';
    if (success && !hasError) classes += ' success';
    return classes;
  };

  const renderField = () => {
    const commonProps = {
      id: fieldId,
      name,
      value: value || '',
      onChange: handleChange,
      onBlur: handleBlur,
      placeholder,
      disabled,
      className: getInputClassName(),
      'aria-invalid': hasError,
      'aria-describedby': error ? `${fieldId}-error` : helperText ? `${fieldId}-helper` : undefined
    };

    if (isSelect) {
      return (
        <select {...commonProps}>
          <option value="">{placeholder || `اختر ${label}`}</option>
          {options.map((option, index) => (
            <option key={index} value={option.value || option}>
              {option.label || option}
            </option>
          ))}
        </select>
      );
    }

    if (isTextarea) {
      return (
        <textarea 
          {...commonProps}
          rows={rows}
          maxLength={maxLength}
          style={{ resize: 'vertical', minHeight: '120px' }}
        />
      );
    }

    return (
      <input 
        {...commonProps}
        type={type}
        min={min}
        max={max}
        step={step}
        maxLength={maxLength}
      />
    );
  };

  return (
    <div className={getFieldClassName()}>
      <label htmlFor={fieldId}>
        {label}
        {required && <span className="required">*</span>}
        {!required && <span className="optional">(اختياري)</span>}
      </label>
      
      {children || renderField()}
      
      {error && (
        <div id={`${fieldId}-error`} className="field-error" role="alert">
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
            <circle cx="12" cy="12" r="10"></circle>
            <line x1="15" y1="9" x2="9" y2="15"></line>
            <line x1="9" y1="9" x2="15" y2="15"></line>
          </svg>
          {error}
        </div>
      )}
      
      {success && !hasError && (
        <div className="field-success">
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
            <polyline points="20 6 9 17 4 12"></polyline>
          </svg>
          تم التحقق
        </div>
      )}
      
      {helperText && !error && (
        <div id={`${fieldId}-helper`} className="helper-note">
          {helperText}
        </div>
      )}
      
      {maxLength && type === 'textarea' && (
        <div className="character-count">
          {(value || '').length} / {maxLength}
        </div>
      )}
    </div>
  );
};

export default FormField;