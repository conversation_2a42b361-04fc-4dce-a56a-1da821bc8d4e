/* Google Material Design for Notifications Page - Always Light Theme */
@import '../styles/variables.css';

/* Global Styles */
* {
  box-sizing: border-box;
}

/* Force Light Theme Variables */
:root {
  --notifications-bg: #ffffff;
  --notifications-text: #333333;
  --notifications-border: #e9ecef;
  --notifications-shadow: 0 2px 8px rgba(0,0,0,0.1);
  --notifications-hover-shadow: 0 4px 12px rgba(0,0,0,0.15);
  --page-background: #ffffff;
  --dark-color: #2a2e70;
  --primary-color: #2563eb;
  --neutral-200: #e2e8f0;
  --neutral-600: #475569;
  --neutral-700: #334155;
  --neutral-800: #1e293b;
  --neutral-900: #0f172a;
  --neutral-100: #f1f5f9;
  --secondary-color: #10b981;
  --shadow-medium: rgba(0, 0, 0, 0.1);
  --shadow-light: rgba(0, 0, 0, 0.05);
  --transition-normal: all 0.3s ease;
  --font-weight-medium: 500;
  --font-weight-normal: 400;
  --radius-sm: 6px;
  --animation-fade-in: fadeIn 0.3s ease-out forwards;
}

/* Page Container - Always Light Theme */
.pageContainer {
  background: var(--notifications-bg);
  min-height: 100vh;
  padding: 0;
  font-family: var(--font-family-primary);
  color: var(--notifications-text);
}

.contentWrapper {
  max-width: 1200px;
  margin: 0 auto;
  padding: 12px;
  margin-top: 15px;
}

/* Header Section - بدون إطار مرئي مثل ReportsOverview */
.header {
  margin-bottom: 15px;
  text-align: center;
  background: transparent;
  border: none;
  box-shadow: none;
  padding: 8px 0;
}

.pageTitle {
  font-size: 32px;
  font-weight: 500;
  color: var(--dark-color);
  margin-bottom: 40px;
  text-align: center;
  padding: 8px 0;
  position: relative;
  margin: 0;
  letter-spacing: -0.5px;
}

.pageTitle::after {
  content: '';
  position: absolute;
  bottom: -4px;
  left: 50%;
  transform: translateX(-50%);
  width: 180px;
  height: 2px;
  background-color: #2a2e70;
  border-radius: 4px;
}



/* Filter Tabs - بتصميم دائري مثل خانة البحث */
.filterTabs {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 40px;
  background-color: transparent;
  padding: 0;
  border: none;
  box-shadow: none;
  gap: 20px;
}

.filterTab {
  padding: 8px 36px 8px 20px;
  border: 1px solid var(--neutral-200);
  border-radius: 30px; /* حواف دائرية مثل خانة البحث */
  font-size: 14px;
  background-color: var(--page-background);
  transition: var(--transition-normal);
  height: 52px;
  display: flex;
  align-items: center;
  gap: 12px;
  cursor: pointer;
  font-family: 'Roboto', sans-serif;
  font-weight: 500;
  color: #6c757d;
  min-width: 180px;
  justify-content: space-between;
  box-shadow: 
    0 20px 60px var(--shadow-medium),
    0 10px 30px var(--shadow-light); /* نفس shadow الكروت */
  position: relative;
  overflow: hidden;
}

.filterTab::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, transparent, rgba(42, 46, 112, 0.05));
  opacity: 0;
  transition: opacity 0.3s ease;
}

.filterTab:hover {
  border-color: var(--primary-color);
  transform: translateY(-2px);
  box-shadow: 
    0 25px 70px var(--shadow-medium),
    0 12px 35px var(--shadow-light);
}

.filterTab:hover::before {
  opacity: 1;
}

.filterTab.activeTab {
  background: linear-gradient(135deg, #2a2e70, #3b4080);
  color: white;
  border-color: #2a2e70;
  box-shadow: 
    0 25px 70px var(--shadow-medium),
    0 12px 35px rgba(42, 46, 112, 0.3);
  transform: translateY(-2px);
}

.filterTab.activeTab::before {
  background: linear-gradient(135deg, rgba(255,255,255,0.1), rgba(255,255,255,0.05));
  opacity: 1;
}

.tabCount {
  background: rgba(255,255,255,0.1);
  color: inherit;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 700;
  min-width: 24px;
  text-align: center;
  backdrop-filter: blur(10px);
}

.filterTab.activeTab .tabCount {
  background: rgba(255,255,255,0.2);
  color: white;
}

/* Notifications Container */
.notificationsContainer {
  padding: 24px;
  min-height: 400px;
}

/* Notifications List - masonry layout مثل ReportsOverview */
.notificationsList {
  column-count: 3;
  column-gap: 20px;
  animation: slideInUp 0.4s ease-out;
}

/* للشاشات المتوسطة - عمودين */
@media (max-width: 999px) and (min-width: 600px) {
  .notificationsList {
    column-count: 2;
  }
}

/* للشاشات الصغيرة - عمود واحد */
@media (max-width: 599px) {
  .notificationsList {
    column-count: 1;
  }
}

/* Notification Cards - مطابق تماماً لـ ReportsOverview */
.notificationCard {
  background-color: var(--page-background);
  border-radius: 20px;
  padding: 2rem;
  box-shadow: 
    0 20px 60px var(--shadow-medium),
    0 10px 30px var(--shadow-light);
  cursor: pointer;
  transition: all var(--transition-normal);
  text-align: right;
  position: relative;
  display: block;
  width: 100%;
  border: 1px solid var(--neutral-200);
  animation: var(--animation-fade-in);
  overflow: hidden;
  margin-bottom: 20px;
  break-inside: avoid;
}

.notificationCard:hover {
  transform: translateY(-4px);

}

.notificationCard:active {
  transform: translateY(-2px);
}

/* Priority Colors - حواف ملونة حسب الأولوية */
.notificationCard.high {
  border: 1px solid #dc3545;
}

.notificationCard.high:hover {
  border-color: #c82333;
  box-shadow: 
    0 2px 10px rgba(220, 53, 69, 0.2),
    0 1px 10px rgba(220, 53, 69, 0.15);
}

.notificationCard.medium {
  border: 1px solid #ffc107;
}

.notificationCard.medium:hover {
  border-color: #e0a800;
  box-shadow: 
    0 2px 10px rgba(255, 193, 7, 0.2),
    0 1px 10px rgba(255, 193, 7, 0.15);
}

.notificationCard.low {
  border: 1px solid #28a745;
}

.notificationCard.low:hover {
  border-color: #1e7e34;
  box-shadow: 
    0 25px 10px rgba(40, 167, 69, 0.2),
    0 15px 10px rgba(40, 167, 69, 0.15);
}

/* الكروت بدون أولوية محددة */
.notificationCard:not(.high):not(.medium):not(.low) {
  border: 1px solid var(--neutral-200);
}

.notificationCard:not(.high):not(.medium):not(.low):hover {
  border-color: var(--primary-color);
}

/* Card Header - مثل ReportsOverview */
.cardHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  border-bottom: 1px solid var(--neutral-200);
  padding-bottom: 12px;
}

.notificationMeta {
  flex: 1;
}

.typeIcon {
  color: var(--primary-color);
  font-size: 18px;
}

.notificationType {
  font-size: 23px;
  font-weight: var(--font-weight-medium);
  color: var(--neutral-900);
  margin: 0;
  display: flex;
  align-items: center;
  gap: 8px;
}

/* Account Badge */
.accountBadge {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.onlineBadge {
  background: rgba(52, 168, 83, 0.1);
  color: #137333;
  border: 1px solid rgba(52, 168, 83, 0.3);
}

.localBadge {
  background: rgba(251, 188, 4, 0.1);
  color: #b06000;
  border: 1px solid rgba(251, 188, 4, 0.3);
}

.date {
  font-size: 18px;
  color: var(--neutral-600);
  display: flex;
  align-items: center;
  gap: 6px;
}

/* Card Body */
.cardBody {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

/* Case Info - مثل ReportsOverview */
.caseInfo {
  margin-bottom: 16px;
}

.caseNumber {
  font-size: 23px;
  font-weight: var(--font-weight-medium);
  color: var(--neutral-900);
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
}

.caseIcon {
  color: var(--primary-color);
  font-size: 18px;
}

.clientInfo,
.courtInfo {
  display: flex;
  align-items: center;
  gap: 10px;
  font-size: 19px;
  margin-bottom: 12px;
}

.clientInfo:last-child,
.courtInfo:last-child {
  margin-bottom: 0;
}

.infoLabel {
  font-weight: var(--font-weight-medium);
  color: var(--secondary-color);
  margin-left: 4px;
}

.infoValue {
  color: var(--neutral-800);
  font-weight: var(--font-weight-normal);
}

.infoIcon {
  color: var(--primary-color);
  font-size: 23px;
}

/* Notification Details - مثل ReportsOverview */
.notificationDetails {
  margin-top: 12px;
}

.detailItem {
  display: flex;
  flex-direction: column;
  gap: 4px;
  margin-bottom: 12px;
}

.detailItem:last-child {
  margin-bottom: 0;
}

.detailLabel {
  font-size: 21px;
  font-weight: var(--font-weight-medium);
  color: var(--neutral-700);
  margin-bottom: 12px;
  margin-top: 0px;
}

.detailValue {
  font-size: 16px;
  color: #555269;
  line-height: 1.4;
  background-color: var(--neutral-100);
  padding: 4px 8px;
  border-radius: var(--radius-sm);
}

/* Notification Actions */
.notificationActions {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
  margin-top: 8px;
}

.completeButton {
  background: linear-gradient(135deg, #28a745 0%, #1e7e34 100%);
  color: white;
  border: none;
  border-radius: 30px;
  padding: 12px 24px;
  font-family: 'Roboto', sans-serif;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all var(--transition-normal);
  display: flex;
  align-items: center;
  gap: 8px;
  text-transform: uppercase;
  letter-spacing: 0.25px;
  box-shadow: 
    0 10px 30px rgba(40, 167, 69, 0.3),
    0 5px 15px rgba(40, 167, 69, 0.2);
  position: relative;
  overflow: hidden;
}

.completeButton::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background: rgba(255,255,255,0.3);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  transition: width 0.3s, height 0.3s;
}

.completeButton:active::before {
  width: 300px;
  height: 300px;
}

.completeButton:hover {
  background: linear-gradient(135deg, #218838 0%, #1e7e34 100%);
  box-shadow: 
    0 15px 40px rgba(40, 167, 69, 0.4),
    0 8px 20px rgba(40, 167, 69, 0.3);
  transform: translateY(-2px);
}

.completeButton:active {
  transform: translateY(0);
}

.buttonIcon {
  font-size: 16px;
}

/* Loading State - Now using LoadingSpinner component */
/* Old loading styles removed - using unified LoadingSpinner component */

/* Empty State */
.emptyState {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  gap: 20px;
  text-align: center;
}

.emptyIcon {
  font-size: 64px;
  color: #adb5bd;
  margin-bottom: 16px;
}

.emptyTitle {
  font-size: 24px;
  font-weight: 400;
  color: #495057;
  margin: 0 0 8px 0;
}

.emptyMessage {
  font-size: 16px;
  color: #6c757d;
  line-height: 1.5;
  max-width: 400px;
  margin: 0;
}

/* Error State */
.errorContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  gap: 20px;
  text-align: center;
}

.errorIcon {
  font-size: 64px;
  color: #dc3545;
  margin-bottom: 16px;
}

.errorTitle {
  font-size: 24px;
  font-weight: 400;
  color: #dc3545;
  margin: 0 0 8px 0;
}

.errorMessage {
  font-size: 16px;
  color: #6c757d;
  line-height: 1.5;
  max-width: 400px;
  margin: 0;
}

/* Animations */
@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.notificationCard:nth-child(1) { animation-delay: 0.1s; }
.notificationCard:nth-child(2) { animation-delay: 0.2s; }
.notificationCard:nth-child(3) { animation-delay: 0.3s; }
.notificationCard:nth-child(4) { animation-delay: 0.4s; }
.notificationCard:nth-child(5) { animation-delay: 0.5s; }
.notificationCard:nth-child(6) { animation-delay: 0.6s; }

/* Responsive Design */
@media (max-width: 768px) {
  .contentWrapper {
    padding: 16px;
  }

  .header {
    margin-bottom: 20px;
  }

  .pageTitle {
    font-size: 28px;
  }

  .filterTabs {
    flex-direction: row;
    flex-wrap: wrap;
    gap: 8px;
    justify-content: center;
  }

  .filterTab {
    min-width: 120px;
    flex: 1;
    max-width: 150px;
    padding: 8px 16px;
    font-size: 12px;
    height: 44px;
  }

  .notificationsContainer {
    padding: 16px;
  }

  .notificationCard {
    padding: 1.5rem;
  }

  .cardHeader {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .notificationMeta {
    width: 100%;
  }

  .date {
    align-self: flex-end;
  }

  .notificationActions {
    justify-content: stretch;
  }

  .completeButton {
    flex: 1;
    justify-content: center;
  }
}

@media (max-width: 480px) {
  .contentWrapper {
    padding: 12px;
  }

  .pageTitle {
    font-size: 24px;
  }

  .filterTabs {
    gap: 6px;
  }

  .filterTab {
    min-width: 100px;
    font-size: 11px;
    height: 40px;
    padding: 6px 12px;
  }

  .tabCount {
    font-size: 10px;
    padding: 2px 6px;
  }

  .notificationsContainer {
    padding: 12px;
  }

  .notificationCard {
    padding: 1.2rem;
  }

  .notificationType {
    font-size: 18px;
  }

  .caseNumber {
    font-size: 18px;
  }

  .clientInfo, .courtInfo {
    font-size: 14px;
  }

  .typeIcon, .caseIcon, .infoIcon {
    font-size: 16px;
  }

  .date {
    font-size: 16px;
  }

  .detailLabel {
    font-size: 18px;
  }

  .detailValue {
    font-size: 14px;
  }
}

/* Always Light Theme - No Dark Mode Support */

/* Accessibility Improvements */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* Focus Visible for Keyboard Navigation */
.filterTab:focus-visible,
.completeButton:focus-visible,
.notificationCard:focus-visible {
  outline: 2px solid #4285f4;
  outline-offset: 2px;
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
  .notificationCard {
    border-width: 2px;
  }
  
  .completeButton {
    border: 2px solid #28a745;
  }
  
  .filterTab.activeTab {
    border: 2px solid #4285f4;
  }
}