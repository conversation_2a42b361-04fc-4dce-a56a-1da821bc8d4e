import{r as l,u as T,j as e,q as $,c as C,d as L,w as q,g as F,a as I}from"./index-Bd3HN_hN.js";import{T as R}from"./TopBar-DgdxUhb0.js";import{c as w,a as d,b as k,d as A}from"./CaseFilters-DVgljPUG.js";import"./iconBase-BmtohqY9.js";const J=({casesList:B=[],setCasesList:M=()=>{},currentUser:c})=>{var D;console.log("CaseRegistration component rendered");const p=new Date().getFullYear().toString(),[s,o]=l.useState({caseNumber:"",caseYear:p,clientName:"",caseDescription:"",caseCategory:"",opponentName:"",caseDegree:"",firstSessionDate:"",circleNumber:"",courtLocation:"",caseStatus:"قيد النظر",reportNumber:"",reportLocation:""}),[h,j]=l.useState(""),[m,f]=l.useState(!1),[u,N]=l.useState(!1),[y,t]=l.useState(null),[b,v]=l.useState(null),x=T();l.useEffect(()=>{if(!m&&s.courtLocation){const a=w.find(n=>n.name===s.courtLocation);a&&o(n=>({...n,caseDegree:a.degree}))}},[s.courtLocation,m]),l.useEffect(()=>{s.caseDegree&&d[s.caseDegree]&&(d[s.caseDegree].includes(s.caseCategory)||o(a=>({...a,caseCategory:d[s.caseDegree][0]})))},[s.caseDegree]),l.useEffect(()=>{j(s.caseNumber?`${s.caseNumber}/${s.caseYear}`:s.caseYear)},[s.caseNumber,s.caseYear]);const r=a=>{const{name:n,value:S}=a.target;o(g=>({...g,[n]:S})),n==="caseDegree"&&f(!0)},Y=async a=>{if(a.preventDefault(),t(null),v(null),!c){alert("يجب تسجيل الدخول لحفظ القضايا."),x("/login");return}if(!s.clientName.trim()){t("يجب إدخال اسم الموكل");return}let n="";if(s.caseStatus==="قيد النظر")n=`قيد النظر-${Date.now()}`;else{if(!s.caseNumber.trim()){t("يجب إدخال رقم القضية.");return}if(!s.caseYear||!/^\d{4}$/.test(s.caseYear)){t("السنة يجب أن تكون مكونة من 4 أرقام.");return}n=`${s.caseNumber.trim()}/${s.caseYear}`}try{const i=$(C(L,"cases"),q("userId","==",c.uid),q("fullCaseNumber","==",n));if(!(await F(i)).empty){t("رقم القضية موجود بالفعل! يرجى اختيار رقم قضية مختلف.");return}}catch(i){t("حدث خطأ أثناء التحقق من رقم القضية: "+i.message);return}if(s.caseStatus==="دعوى قضائية"){if(!s.circleNumber.trim()){t("يجب إدخال رقم الدائرة.");return}if(!s.caseDegree){t("يجب اختيار درجة الدعوى.");return}if(!s.caseCategory){t("يجب اختيار نوع الدعوى.");return}if(!s.courtLocation){t("يجب اختيار مكان المحكمة.");return}if(!s.firstSessionDate){t("يجب إدخال تاريخ أول جلسة.");return}if(!s.caseDescription.trim()){t("يجب إدخال الوصف.");return}}if(s.caseStatus==="محضر"){if(!s.reportLocation.trim()){t("يجب إدخال مكان الجهة المختصة.");return}if(!s.reportNumber.trim()){t("يجب إدخال رقم المحضر.");return}if(!s.caseDescription.trim()){t("يجب إدخال وصف المحضر.");return}}if(s.caseStatus==="قيد النظر"){if(!s.reportLocation.trim()){t("يجب إدخال مكان الجهة المختصة.");return}if(!s.caseDescription.trim()){t("يجب إدخال الوصف القضائي.");return}}if(!window.confirm("هل أنت متأكد من حفظ القضية؟"))return;const g={fullCaseNumber:n,caseNumber:s.caseStatus==="قيد النظر"?null:s.caseNumber.trim(),caseYear:s.caseStatus==="قيد النظر"?p:s.caseYear,clientName:s.clientName.trim(),opponentName:s.opponentName.trim()||null,caseDescription:s.caseDescription.trim()||null,caseCategory:s.caseCategory||null,caseDegree:s.caseDegree||null,courtLocation:s.courtLocation||null,circleNumber:s.caseStatus==="دعوى قضائية"&&s.circleNumber.trim()||null,firstSessionDate:s.caseStatus==="دعوى قضائية"&&s.firstSessionDate?s.firstSessionDate:null,caseStatus:s.caseStatus||"قيد النظر",reportNumber:s.caseStatus==="محضر"&&s.reportNumber.trim()||null,reportLocation:(s.caseStatus==="محضر"||s.caseStatus==="قيد النظر")&&s.reportLocation.trim()||null,deferrals:[],actions:[],history:[],createdAt:new Date().toISOString(),updatedAt:new Date().toISOString(),userId:c.uid};N(!0);try{const i=await I(C(L,"cases"),g);console.log("Document written with ID: ",i.id),o({caseNumber:"",caseYear:p,clientName:"",caseDescription:"",caseCategory:"",opponentName:"",caseDegree:"",firstSessionDate:"",circleNumber:"",courtLocation:"",caseStatus:"قيد النظر",reportNumber:"",reportLocation:""}),j(""),f(!1),v("تم حفظ القضية بنجاح!"),setTimeout(()=>{x(`/case-details/${i.id}`)},1500)}catch(i){console.error("Error adding document: ",i),t("حدث خطأ أثناء حفظ القضية: "+i.message)}finally{N(!1)}},E=()=>{x("/dashboard",{state:{refresh:!0}})};return e.jsxs("div",{children:[e.jsx(R,{currentUser:c,casesList:B}),e.jsx("div",{className:"container",style:{padding:"20px",marginTop:"10px"},children:e.jsxs("div",{className:"case-registration-container",children:[e.jsx("h2",{children:"تسجيل قضية جديدة"}),y&&e.jsx("div",{style:{color:"red",marginBottom:"15px"},children:y}),b&&e.jsx("div",{style:{color:"green",marginBottom:"15px"},children:b}),u&&e.jsxs("div",{style:{textAlign:"center",marginBottom:"15px"},children:[e.jsx("div",{style:{border:"4px solid rgba(0, 0, 0, 0.1)",borderLeft:"4px solid #000",borderRadius:"50%",width:"30px",height:"30px",animation:"spin 1s linear infinite",margin:"0 auto"}}),e.jsx("p",{children:"جاري حفظ القضية..."}),e.jsx("style",{children:`
                  @keyframes spin {
                    0% { transform: rotate(0deg); }
                    100% { transform: rotate(360deg); }
                  }
                `})]}),e.jsxs("form",{onSubmit:Y,children:[e.jsxs("div",{style:{display:"flex",gap:"20px",marginBottom:"15px"},children:[e.jsxs("div",{className:"input-group",style:{flex:1},children:[e.jsx("label",{children:"الحالة:"}),e.jsx("select",{name:"caseStatus",value:s.caseStatus,onChange:r,children:k.map(a=>e.jsx("option",{value:a,children:a},a))})]}),s.caseStatus!=="قيد النظر"&&e.jsxs("div",{className:"input-group",style:{flex:1},children:[e.jsxs("label",{children:["رقم القضية: ",e.jsx("span",{className:"required-star",children:"*"})]}),e.jsxs("div",{className:"case-number-inputs",children:[e.jsx("input",{type:"text",name:"caseNumber",value:s.caseNumber,onChange:r,placeholder:"0000",style:{width:"60px"}}),e.jsx("span",{className:"separator",children:"/"}),e.jsx("input",{type:"text",name:"caseYear",value:s.caseYear,onChange:r,placeholder:"2025",style:{width:"60px"}})]}),h&&s.caseStatus!=="قيد النظر"&&e.jsxs("div",{className:"generated-number",children:["رقم القضية الكامل: ",e.jsx("strong",{children:h})]})]}),s.caseStatus==="قيد النظر"&&e.jsx("div",{className:"input-group",style:{flex:1}})]}),e.jsxs("div",{style:{display:"flex",gap:"20px",marginBottom:"15px"},children:[e.jsxs("div",{className:"input-group",style:{flex:1},children:[e.jsxs("label",{children:["اسم الموكل: ",e.jsx("span",{className:"required-star",children:"*"})]}),e.jsx("input",{type:"text",name:"clientName",value:s.clientName,onChange:r})]}),e.jsxs("div",{className:"input-group",style:{flex:1},children:[e.jsx("label",{children:"اسم الخصم (اختياري):"}),e.jsx("input",{type:"text",name:"opponentName",value:s.opponentName,onChange:r})]})]}),s.caseStatus==="قيد النظر"&&e.jsxs(e.Fragment,{children:[e.jsxs("div",{style:{display:"flex",gap:"20px",marginBottom:"15px"},children:[e.jsxs("div",{className:"input-group",style:{flex:1},children:[e.jsxs("label",{children:["مكان الجهة المختصة: ",e.jsx("span",{className:"required-star",children:"*"})]}),e.jsx("input",{type:"text",name:"reportLocation",value:s.reportLocation,onChange:r,placeholder:"أدخل مكان القسم أو المنطقة أو المحكمة"})]}),e.jsx("div",{className:"input-group",style:{flex:1}})]}),e.jsxs("div",{className:"input-group",style:{marginBottom:"15px"},children:[e.jsxs("label",{children:["الوصف: ",e.jsx("span",{className:"required-star",children:"*"})]}),e.jsx("input",{type:"text",name:"caseDescription",value:s.caseDescription,onChange:r})]})]}),s.caseStatus==="محضر"&&e.jsxs(e.Fragment,{children:[e.jsxs("div",{style:{display:"flex",gap:"20px",marginBottom:"15px"},children:[e.jsxs("div",{className:"input-group",style:{flex:1},children:[e.jsxs("label",{children:["مكان الجهة المختصة: ",e.jsx("span",{className:"required-star",children:"*"})]}),e.jsx("input",{type:"text",name:"reportLocation",value:s.reportLocation,onChange:r,placeholder:"أدخل مكان القسم أو المنطقة أو المحكمة"})]}),e.jsxs("div",{className:"input-group",style:{flex:1},children:[e.jsxs("label",{children:["رقم المحضر: ",e.jsx("span",{className:"required-star",children:"*"})]}),e.jsx("input",{type:"text",name:"reportNumber",value:s.reportNumber,onChange:r,placeholder:"أدخل رقم المحضر"})]})]}),e.jsxs("div",{className:"input-group",style:{marginBottom:"15px"},children:[e.jsxs("label",{children:["وصف المحضر: ",e.jsx("span",{className:"required-star",children:"*"})]}),e.jsx("input",{type:"text",name:"caseDescription",value:s.caseDescription,onChange:r})]})]}),s.caseStatus==="دعوى قضائية"&&e.jsxs(e.Fragment,{children:[e.jsxs("div",{style:{display:"flex",gap:"20px",marginBottom:"15px"},children:[e.jsxs("div",{className:"input-group",style:{flex:1},children:[e.jsxs("label",{children:["رقم الدائرة: ",e.jsx("span",{className:"required-star",children:"*"})]}),e.jsx("input",{type:"text",name:"circleNumber",value:s.circleNumber,onChange:r})]}),e.jsx("div",{className:"input-group",style:{flex:1}})]}),e.jsxs("div",{style:{display:"flex",gap:"20px",marginBottom:"15px"},children:[e.jsxs("div",{className:"input-group",style:{flex:1},children:[e.jsxs("label",{children:["درجة الدعوى: ",e.jsx("span",{className:"required-star",children:"*"})]}),e.jsxs("select",{name:"caseDegree",value:s.caseDegree,onChange:r,children:[e.jsx("option",{value:"",children:"-- اختر درجة الدعوى --"}),A.map(a=>e.jsx("option",{value:a,children:a},a))]}),e.jsx("small",{style:{color:"#666",display:"block",marginTop:"5px"},children:m?"لقد قمت بتعديل الدرجة يدوياً":"سيتم تعيين الدرجة تلقائياً حسب المحكمة"})]}),e.jsxs("div",{className:"input-group",style:{flex:1},children:[e.jsxs("label",{children:["نوع الدعوى: ",e.jsx("span",{className:"required-star",children:"*"})]}),e.jsxs("select",{name:"caseCategory",value:s.caseCategory,onChange:r,disabled:!s.caseDegree,children:[e.jsx("option",{value:"",children:"-- اختر نوع الدعوى --"}),s.caseDegree&&((D=d[s.caseDegree])==null?void 0:D.map(a=>e.jsx("option",{value:a,children:a},a)))]})]})]}),e.jsxs("div",{style:{display:"flex",gap:"20px",marginBottom:"15px"},children:[e.jsxs("div",{className:"input-group",style:{flex:1},children:[e.jsxs("label",{children:["مكان المحكمة: ",e.jsx("span",{className:"required-star",children:"*"})]}),e.jsxs("select",{name:"courtLocation",value:s.courtLocation,onChange:r,children:[e.jsx("option",{value:"",children:"-- اختر المحكمة --"}),w.map(a=>e.jsx("option",{value:a.name,children:a.name},a.name))]})]}),e.jsxs("div",{className:"input-group",style:{flex:1},children:[e.jsxs("label",{children:["تاريخ أول جلسة: ",e.jsx("span",{className:"required-star",children:"*"})]}),e.jsx("input",{type:"date",name:"firstSessionDate",value:s.firstSessionDate,onChange:r})]})]}),e.jsxs("div",{className:"input-group",style:{marginBottom:"15px"},children:[e.jsxs("label",{children:["الوصف القضائي: ",e.jsx("span",{className:"required-star",children:"*"})]}),e.jsx("input",{type:"text",name:"caseDescription",value:s.caseDescription,onChange:r})]})]}),e.jsxs("div",{className:"button-group",style:{display:"flex",gap:"15px",justifyContent:"flex-end",marginTop:"20px"},children:[e.jsx("button",{type:"submit",className:"case-save-btn",disabled:u,children:u?"جاري الحفظ...":"حفظ القضية"}),e.jsx("button",{type:"button",onClick:E,className:"case-cancel-btn",disabled:u,children:"إلغاء"})]})]})]})})]})};export{J as default};
