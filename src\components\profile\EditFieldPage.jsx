import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { FaSave, FaTimes, FaUser, FaPhone, FaBuilding, FaArrowRight, FaEnvelope, FaInfoCircle, FaShieldAlt, FaLock } from 'react-icons/fa';
import TopBar from '../topbar/TopBar';
import styles from './ProfilePage.module.css';
import { db } from '../../config/firebaseConfig';
import { doc, getDoc, updateDoc } from 'firebase/firestore';
import { getActiveAccount } from '../../services/StorageService';

const EditFieldPage = ({ currentUser }) => {
  const { fieldName } = useParams();
  const navigate = useNavigate();
  const [value, setValue] = useState('');
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [localUserData, setLocalUserData] = useState(null);
  const activeAccount = getActiveAccount();



  useEffect(() => {
    const fetchUserData = async () => {
      if (!currentUser || !currentUser.uid) {
        setError('المستخدم غير مسجل الدخول. يرجى تسجيل الدخول مرة أخرى.');
        setLoading(false);
        return;
      }

      setLoading(true);
      setError(null);

      try {
        if (activeAccount === 'online') {
          // تحميل بيانات الحساب الأونلاين
          const userRef = doc(db, 'users', currentUser.uid);
          const userSnap = await getDoc(userRef);
          if (userSnap.exists()) {
            const data = userSnap.data();
            setValue(data[fieldName] || '');
          }
        } else {
          // تحميل بيانات الحساب المحلي
          const localData = localStorage.getItem('localUserData_' + currentUser.uid);
          if (localData) {
            const parsedLocalData = JSON.parse(localData);
            setLocalUserData(parsedLocalData);
            setValue(parsedLocalData[fieldName] || '');
          }
        }
      } catch (e) {
        console.error('خطأ في جلب بيانات المستخدم:', e.message);
        setError('حدث خطأ في جلب البيانات. يرجى المحاولة مرة أخرى.');
      }

      setLoading(false);
    };

    fetchUserData();
  }, [currentUser, fieldName, activeAccount]);

  const handleChange = (e) => {
    setValue(e.target.value);
  };

  const handleSave = async () => {
    if (!currentUser?.uid) {
      setError('المستخدم غير مسجل الدخول.');
      return;
    }

    // التحقق من صحة البيانات
    if (fieldName === 'name' && !value.trim()) {
      setError('الاسم مطلوب.');
      return;
    }

    try {
      setLoading(true);
      setError(null);

      // إنشاء كائن التحديث
      const updateData = {
        [fieldName]: value
      };

      if (activeAccount === 'online') {
        if (navigator.onLine) {
          // تحديث البيانات في Firestore
          const userRef = doc(db, 'users', currentUser.uid);
          await updateDoc(userRef, {
            ...updateData,
            lastUpdateTime: new Date().toISOString()
          });
          alert('تم حفظ البيانات بنجاح في الحساب الأونلاين.');
        } else {
          setError('لا يمكن حفظ البيانات في الحساب الأونلاين بدون اتصال بالإنترنت.');
          setLoading(false);
          return;
        }
      } else {
        // تحديث البيانات في الحساب المحلي
        const updatedLocalData = {
          ...(localUserData || {}),
          uid: currentUser.uid,
          ...updateData,
          lastUpdatedAt: new Date().toISOString()
        };
        localStorage.setItem('localUserData_' + currentUser.uid, JSON.stringify(updatedLocalData));
        alert('تم حفظ البيانات بنجاح في الحساب المحلي.');
      }

      // العودة إلى صفحة البروفايل
      navigate('/profile');
    } catch (e) {
      setError('خطأ في تحديث البيانات: ' + e.message);
    } finally {
      setLoading(false);
    }
  };

  const handleCancel = () => {
    navigate('/profile');
  };

  // تحديد النص الوصفي حسب نوع الحقل
  const getFieldDescription = () => {
    switch (fieldName) {
      case 'name':
        return (
          <div className="field-description">
            <FaInfoCircle style={{ color: '#1a73e8', marginLeft: '8px' }} />
            <span>إنّ التغييرات التي يتم إجراؤها على لقبكَ ستظهر في حسابكَ على تطبيق الاجندة القضائية.</span>
          </div>
        );
      case 'phone':
        return (
          <div className="field-description">
            <FaShieldAlt style={{ color: '#1a73e8', marginLeft: '8px' }} />
            <span>يستخدم رقم الهاتف في استرجاع حسابك وتأمين بياناتك.</span>
          </div>
        );
      case 'company':
        return (
          <div className="field-description">
            <FaBuilding style={{ color: '#1a73e8', marginLeft: '8px' }} />
            <span>معلومات الشركة تساعدنا في تقديم خدمة أفضل لك.</span>
          </div>
        );
      case 'email':
        return (
          <div className="field-description">
            <FaEnvelope style={{ color: '#1a73e8', marginLeft: '8px' }} />
            <span>البريد الإلكتروني لحساب تطبيق الاجندة القضائية. العنوان المُستخدم لمساعدتك في التعرّف على حسابك.</span>
          </div>
        );
      default:
        return null;
    }
  };

  // تحديد النص التوضيحي حسب نوع الحقل
  const getFieldFooterText = () => {
    switch (fieldName) {
      case 'name':
        return (
          <div className="field-footer">
            <FaUser style={{ color: '#5f6368', marginLeft: '8px' }} />
            <span>المستخدمون الذين يمكنهم الاطّلاع على اسمك: يمكن لأي مستخدم الاطّلاع على هذه المعلومات عندما يتواصل معك أو يعرض المحتوى الذي تنشئه في خدمات تطبيق الاجندة القضائية.</span>
          </div>
        );
      case 'phone':
        return (
          <div className="field-footer">
            <FaLock style={{ color: '#5f6368', marginLeft: '8px' }} />
            <span>لا يمكن لأحد أن يرى رقم هاتفك. يتم استخدامه فقط لأغراض الأمان والتحقق.</span>
          </div>
        );
      case 'company':
        return (
          <div className="field-footer">
            <FaInfoCircle style={{ color: '#5f6368', marginLeft: '8px' }} />
            <span>معلومات الشركة تظهر في ملفك الشخصي وتساعد في تخصيص تجربتك.</span>
          </div>
        );
      case 'email':
        return (
          <div className="field-footer">
            <FaInfoCircle style={{ color: '#5f6368', marginLeft: '8px' }} />
            <span>لا يمكنك تغيير هذا العنوان. يرجى التواصل مع الدعم الفني إذا كنت بحاجة إلى تغيير بريدك الإلكتروني.</span>
          </div>
        );
      default:
        return null;
    }
  };

  if (loading) {
    return (
      <div className={styles.pageWrapper}>
        <TopBar currentUser={currentUser} />
        <div className={styles.loadingContainer}><div className={styles.spinner}></div><p>جاري التحميل...</p></div>
      </div>
    );
  }

  return (
    <div className={styles.pageWrapper}>
      <TopBar currentUser={currentUser} />
      <div className={styles.mainContainer}>
        <div className={styles.accountManagementSection}>
          <div className={styles.sidebarNavItem} style={{ visibility: 'hidden' }}>
            <div className={styles.sidebarNavIcon}><FaUser /></div>
            <span>المعلومات الشخصية</span>
          </div>
        </div>
        <div className={styles.mainContentArea}>
          <div className={styles.pageHeader}>
            <h2 className={styles.pageTitle}>
              تعديل
            </h2>
          </div>
          
          {error && <div className={styles.errorMessage}>{error}</div>}
          
          <div className={styles.personalInfoSection}>
            <div className={styles.editFieldForm}>
              <h3 className={styles.sectionTitle}>تعديل البيانات</h3>
              
              <div style={{ 
                color: '#5f6368', 
                fontSize: '14px', 
                marginBottom: '15px',
                lineHeight: '1.5',
                display: 'flex',
                alignItems: 'flex-start',
                backgroundColor: '#f8f9fa',
                padding: '12px 15px',
                borderRadius: '8px'
              }}>
                {getFieldDescription()}
              </div>
              
              <div className={styles.formGroup}>
                <input 
                  type="text" 
                  value={value} 
                  onChange={handleChange} 
                  className={styles.formInput}
                  autoFocus
                  style={{ marginBottom: '10px' }}
                  disabled={fieldName === 'email'}
                />
              </div>
              
              <div style={{ 
                color: '#5f6368', 
                fontSize: '12px', 
                marginBottom: '20px',
                lineHeight: '1.5',
                display: 'flex',
                alignItems: 'flex-start',
                backgroundColor: '#f1f3f4',
                padding: '10px 12px',
                borderRadius: '8px'
              }}>
                {getFieldFooterText()}
              </div>
              
              <div className={styles.buttonRow}>
                <button 
                  onClick={handleSave} 
                  style={{
                    background: '#1a73e8',
                    color: '#fff',
                    border: 'none',
                    borderRadius: '20px',
                    padding: '8px 16px',
                    fontSize: '14px',
                    cursor: 'pointer',
                    display: 'flex',
                    alignItems: 'center',
                    gap: '5px'
                  }}
                  disabled={loading || fieldName === 'email'}
                >
                  <FaSave style={{ fontSize: '12px' }} /> حفظ
                </button>
                <button 
                  onClick={handleCancel} 
                  style={{
                    background: '#f1f3f4',
                    color: '#5f6368',
                    border: '1px solid #dadce0',
                    borderRadius: '20px',
                    padding: '8px 16px',
                    fontSize: '14px',
                    cursor: 'pointer',
                    display: 'flex',
                    alignItems: 'center',
                    gap: '5px'
                  }}
                >
                  <FaTimes style={{ fontSize: '12px' }} /> إلغاء
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
      <style jsx>{`
        .field-description {
          display: flex;
          align-items: flex-start;
          gap: 8px;
        }
        .field-description span {
          flex: 1;
        }
        .field-footer {
          display: flex;
          align-items: flex-start;
          gap: 8px;
        }
        .field-footer span {
          flex: 1;
        }
      `}</style>
    </div>
  );
};

export default EditFieldPage;