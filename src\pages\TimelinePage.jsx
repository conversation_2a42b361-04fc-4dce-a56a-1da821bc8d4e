import React, { useState, useEffect, useMemo, useRef } from 'react';
import styles from './TimelinePage.module.css';

const getRandomColor = (usedColors = new Set()) => {
  const colors = [
    '#1976d2',
    '#f06292',
    '#4caf50',
    '#ff9800',
    '#ab47bc',
    '#26c6da',
    '#ef5350',
    '#ffca28',
  ];
  let color;
  if (usedColors.size >= colors.length) {
    const availableColors = colors.filter(c => !usedColors.has(c));
    if (availableColors.length > 0) {
      color = availableColors[Math.floor(Math.random() * availableColors.length)];
    } else {
      color = colors[Math.floor(Math.random() * colors.length)];
    }
  } else {
    do {
      color = colors[Math.floor(Math.random() * colors.length)];
    } while (usedColors.has(color));
  }
  usedColors.add(color);
  return color;
};

const TimelinePage = ({ casesList }) => {
  const [filteredEvents, setFilteredEvents] = useState([]);
  const [tooltip, setTooltip] = useState({
    visible: false,
    event: null,
    position: { top: 0, left: 0 }
  });
  const [svgDimensions, setSvgDimensions] = useState({ width: 800, height: 700 });
  const svgRef = useRef(null);
  const containerRef = useRef(null);
  const tooltipRef = useRef(null);

  const TODAY = new Date();

  useEffect(() => {
    const updateSvgDimensions = () => {
      if (containerRef.current) {
        const containerWidth = containerRef.current.offsetWidth;
        const windowWidth = window.innerWidth;
        let newWidth, newHeight;

        if (windowWidth <= 480) {
          newWidth = containerWidth * 1.3;
          newHeight = newWidth * 1.4;
        } else if (windowWidth <= 768) {
          newWidth = containerWidth * 1.1;
          newHeight = newWidth * 1.2;
        } else {
          newWidth = Math.min(containerWidth, 800);
          newHeight = newWidth * (700 / 800);
        }

        setSvgDimensions({ width: newWidth, height: newHeight });
      }
    };

    const timer = setTimeout(() => {
      updateSvgDimensions();
    }, 100);

    window.addEventListener('resize', updateSvgDimensions);
    window.addEventListener('load', updateSvgDimensions);

    updateSvgDimensions();

    return () => {
      clearTimeout(timer);
      window.removeEventListener('resize', updateSvgDimensions);
      window.removeEventListener('load', updateSvgDimensions);
    };
  }, []);

  const extractedEvents = useMemo(() => {
    const currentAllEvents = [];
    const eventColors = new Map();
    const localUsedActionColors = new Set();

    casesList.forEach(caseItem => {
      const identifier = caseItem.fullCaseNumber || 'غير محدد';
      const clientName = caseItem.clientName || 'غير محدد';

      // 1. استخراج التأجيلات باستخدام lastReport وlastReportDate
      if (caseItem.lastReport && caseItem.lastReportDate && !isNaN(new Date(caseItem.lastReportDate).getTime())) {
        const eventId = `${identifier}-defer-${caseItem.id}`;
        const color = eventColors.get(eventId) || getRandomColor(new Set([...Array.from(eventColors.values()), ...Array.from(localUsedActionColors)]));
        eventColors.set(eventId, color);

        currentAllEvents.push({
          type: 'تأجيل',
          date: new Date(caseItem.lastReportDate).toISOString().split('T')[0],
          reasons: caseItem.lastReport,
          clientName,
          caseNumber: identifier,
          courtLocation: caseItem.courtLocation || 'غير محدد',
          eventId,
          color,
          positionAngle: Math.random() * 360,
        });
      }

      // 2. استخراج الإجراءات باستخدام lastAction وlastActionDate
      if (caseItem.lastAction && caseItem.lastActionDate && !isNaN(new Date(caseItem.lastActionDate).getTime())) {
        const eventId = `${identifier}-action-${caseItem.id}`;
        let color;

        // التحقق من وجود ربط مع تأجيل
        if (caseItem.linkedReport) {
          const linkedEventId = `${identifier}-defer-${caseItem.id}`;
          const linkedDeferral = currentAllEvents.find(e => e.eventId === linkedEventId && e.type === 'تأجيل');
          if (linkedDeferral) {
            color = linkedDeferral.color;
            eventColors.set(eventId, color);
          }
        }

        // إذا لم يكن هناك ربط، اختيار لون جديد
        if (!color) {
          color = getRandomColor(new Set([...Array.from(eventColors.values()), ...Array.from(localUsedActionColors)]));
          localUsedActionColors.add(color);
          eventColors.set(eventId, color);
        }

        currentAllEvents.push({
          type: 'إجراء',
          description: caseItem.lastAction,
          deadline: new Date(caseItem.lastActionDate).toISOString().split('T')[0],
          priority: 'عادي',
          timestamp: caseItem.lastActionDate,
          clientName,
          caseNumber: identifier,
          courtLocation: caseItem.courtLocation || 'غير محدد',
          eventId,
          color,
          linkedDeferralId: caseItem.linkedReport ? `${identifier}-defer-${caseItem.id}` : '',
          linkedActionId: '',
          positionAngle: Math.random() * 360,
        });
      }
    });

    return currentAllEvents.sort((a, b) => {
      const dateA = new Date(a.type === 'تأجيل' ? a.date : a.deadline);
      const dateB = new Date(b.type === 'تأجيل' ? b.date : b.deadline);
      return dateA - dateB;
    });
  }, [casesList]);

  useEffect(() => {
    setFilteredEvents(extractedEvents);
    setTooltip({ visible: false, event: null, position: { top: 0, left: 0 } });
  }, [extractedEvents]);

  const handleEventClick = (eventData, svgElement) => {
    if (!svgElement || !svgRef.current || !containerRef.current) return;

    if (tooltip.visible && tooltip.event?.eventId === eventData.eventId) {
      setTooltip({ visible: false, event: null, position: { top: 0, left: 0 } });
      return;
    }

    const svgRect = svgRef.current.getBoundingClientRect();
    const containerRect = containerRef.current.getBoundingClientRect();
    const elementRect = svgElement.getBoundingClientRect();

    let tooltipLeft = (elementRect.left + elementRect.right) / 2 - containerRect.left;
    let tooltipTop = (elementRect.top + elementRect.bottom) / 2 - containerRect.top;

    const tempTooltipWidth = 200;
    const tempTooltipHeight = 100;

    if (tooltipLeft + tempTooltipWidth > svgRect.width) {
      tooltipLeft = svgRect.width - tempTooltipWidth - 10;
    }
    if (tooltipLeft < 0) {
      tooltipLeft = 10;
    }

    if (tooltipTop + tempTooltipHeight > svgRect.height) {
      tooltipTop = elementRect.top - containerRect.top - tempTooltipHeight - 10;
    }
    if (tooltipTop < 0) {
      tooltipTop = elementRect.bottom - containerRect.top + 10;
    }
    if (tooltipTop < 0) tooltipTop = 10;
    if (tooltipTop + tempTooltipHeight > svgRect.height) tooltipTop = svgRect.height - tempTooltipHeight - 10;

    setTooltip({
      visible: true,
      event: eventData,
      position: {
        left: Math.max(10, Math.min(tooltipLeft, svgRect.width - tempTooltipWidth - 10)),
        top: Math.max(10, Math.min(tooltipTop, svgRect.height - tempTooltipHeight - 10))
      }
    });
  };

  const handleTooltipClick = () => {
    setTooltip({ visible: false, event: null, position: { top: 0, left: 0 } });
  };

  const renderRadarViewSvg = () => {
    const { width: svgWidth, height: svgHeight } = svgDimensions;
    const viewBox = `0 0 ${svgWidth} ${svgHeight}`;

    const svgCenterX = svgWidth / 2;
    const svgCenterY = svgHeight / 2 + 30;
    const margin = -60;
    const svgMaxRadius = Math.min(svgWidth, svgHeight) / 2 - margin;

    const daysRange = 10;
    const dayRadius = svgMaxRadius / (daysRange + 0.5);

    const daysOffset = 1;

    const eventsWithDaysDiff = filteredEvents.map(event => {
      const eventDate = new Date(event.type === 'تأجيل' ? event.date : event.deadline);
      const today = new Date(TODAY);
      const todayMidnight = new Date(today.getFullYear(), today.getMonth(), today.getDate());
      const eventDateMidnight = new Date(eventDate.getFullYear(), eventDate.getMonth(), eventDate.getDate());
      const timeDiff = eventDateMidnight - todayMidnight;
      const daysDiffBase = Math.floor(timeDiff / (1000 * 60 * 60 * 24));
      const daysDiff = daysDiffBase + daysOffset;
      return { ...event, daysDiff, eventDate };
    }).filter(event => Math.abs(event.daysDiff) <= daysRange);

    const eventSvgCoords = new Map();

    const glowFilter = (
      <defs key="glow-filter">
        <filter id="glow" x="-50%" y="-50%" width="200%" height="200%">
          <feGaussianBlur in="SourceGraphic" stdDeviation="1" result="blur" />
          <feColorMatrix in="blur" mode="matrix" values="1 0 0 0 0  0 1 0 0 0  0 0 1 0 0  0 0 0 18 -7" result="glow" />
          <feBlend in="SourceGraphic" in2="glow" mode="normal" />
        </filter>
      </defs>
    );

    const dotPattern = (
      <defs key="dot-pattern">
        <pattern id="dots-pattern" x="0" y="0" width="50" height="50" patternUnits="userSpaceOnUse">
          <rect x="0" y="0" width="50" height="50" fill="transparent" />
          <circle cx="10" cy="10" r="1" fill="#aaaaaa" opacity="0.4" filter="url(#glow)" />
          <circle cx="40" cy="15" r="1" fill="#aaaaaa" opacity="0.4" filter="url(#glow)" />
          <circle cx="20" cy="35" r="1" fill="#aaaaaa" opacity="0.4" filter="url(#glow)" />
          <circle cx="45" cy="40" r="1" fill="#aaaaaa" opacity="0.4" filter="url(#glow)" />
        </pattern>
      </defs>
    );

    const backgroundRect = (
      <g key="bg-group">
        <rect x="0" y="0" width={svgWidth} height={svgHeight} fill="url(#dots-pattern)" />
      </g>
    );

    const rings = [];
    for (let i = -daysRange; i <= daysRange; i++) {
      if (i === 0) continue;
      const radius = Math.abs(i) * dayRadius;
      rings.push(
        <circle
          key={`ring-${i}`}
          cx={svgCenterX} cy={svgCenterY} r={radius}
          className={styles.radarRingCircle}
        />
      );
    }

    const eventElements = [];
    eventsWithDaysDiff.forEach((event) => {
      const daysDiff = event.daysDiff;
      const radius = Math.abs(daysDiff) * dayRadius;
      const angle = event.positionAngle;
      const radian = (angle * Math.PI) / 180;

      const eventX = svgCenterX + radius * Math.cos(radian);
      const eventY = svgCenterY + radius * Math.sin(radian);
      eventSvgCoords.set(event.eventId, { x: eventX, y: eventY });

      const dotRadius = event.type === 'تأجيل' ? 8 : 6;

      eventElements.push(
        <g key={event.eventId} className={styles.eventGroup} transform={`translate(${eventX}, ${eventY})`}>
          <circle
            cx="0" cy="0" r={dotRadius} fill={event.color}
            className={styles.eventDotCircle}
            onClick={(e) => handleEventClick(event, e.target)}
          />
          <text
            x="0" y={-(dotRadius + 5)}
            className={styles.clientNameText}
          >
            {event.clientName}
          </text>
        </g>
      );
    });

    const connectionElements = [];
    eventsWithDaysDiff.forEach((event) => {
      const startCoords = eventSvgCoords.get(event.eventId);
      if (!startCoords) return;

      const drawConnection = (linkedEventId, keyPrefix) => {
        const endCoords = eventSvgCoords.get(linkedEventId);
        if (endCoords) {
          connectionElements.push(
            <line
              key={`${keyPrefix}-${event.eventId}`}
              x1={startCoords.x} y1={startCoords.y}
              x2={endCoords.x} y2={endCoords.y}
              stroke={event.color}
              className={styles.connectionLine}
            />
          );
        }
      };
      if (event.type === 'إجراء' && event.linkedDeferralId) {
        drawConnection(event.linkedDeferralId, 'conn-deferral');
      }
      if (event.type === 'إجراء' && event.linkedActionId) {
        drawConnection(event.linkedActionId, 'conn-action');
      }
    });

    return (
      <div className={styles.radarContainer} ref={containerRef} style={{ backgroundColor: 'transparent' }}>
        <svg
          ref={svgRef}
          width="100%"
          height={svgHeight}
          viewBox={viewBox}
          preserveAspectRatio="xMidYMid meet"
          className={styles.radarSvg}
        >
          {glowFilter}
          {dotPattern}
          {backgroundRect}
          <g>{connectionElements}</g>
          <g>{rings}</g>
          <g transform={`translate(${svgCenterX}, ${svgCenterY})`} className={styles.centerGroup}>
            <text x="0" y="0" dominantBaseline="middle" textAnchor="middle" className={styles.centerLabelSvg}>اليوم</text>
          </g>
          <g>{eventElements}</g>
        </svg>

        {tooltip.visible && tooltip.event && (
          <div
            ref={tooltipRef}
            className={`${styles.simpleTooltip} ${styles.visible}`}
            style={{
              position: 'absolute',
              left: `${tooltip.position.left}px`,
              top: `${tooltip.position.top}px`,
              backgroundColor: tooltip.event.color,
            }}
            onClick={handleTooltipClick}
          >
            {tooltip.event.type === 'تأجيل' ? (
              <>
                <div>{tooltip.event.clientName} ({tooltip.event.caseNumber})</div>
                <div>{tooltip.event.reasons}</div>
                <div>التاريخ: {tooltip.event.date}</div>
              </>
            ) : (
              <>
                <div>{tooltip.event.clientName} ({tooltip.event.caseNumber})</div>
                <div>{tooltip.event.description}</div>
                <div>الموعد: {tooltip.event.deadline}</div>
              </>
            )}
          </div>
        )}
      </div>
    );
  };

  return (
    <div className={styles.pageWrapper}>
      <div className={styles.mainContainer}>
        {filteredEvents.length > 0 ? (
          renderRadarViewSvg()
        ) : (
          <div className={styles.noEvents}>لا توجد أحداث لعرضها ضمن النطاق الزمني المحدد</div>
        )}
      </div>
    </div>
  );
};

export default TimelinePage;