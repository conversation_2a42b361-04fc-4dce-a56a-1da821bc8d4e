
/* استيراد المتغيرات الموحدة والثيمات */
@import './variables.css';
@import './liquid-theme.css';

html, body, #root {
  height: 100%;
  width: 100%;
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  direction: rtl;
  font-family: var(--font-family-primary);
}

body {
  background-color: var(--current-bg-secondary);
  color: var(--current-text-primary);
}

/* Animation للتحميل - تم نقلها إلى variables.css لتجنب التكرار */


/* استخدام المتغيرات الموحدة مع الحفاظ على الألوان الأصلية */
button {
  /* استخدام المتغيرات للخصائص المشتركة مع الحفاظ على الألوان الأصلية */
  padding: 12px 25px;
  margin-top: 5px;
  font-size: var(--font-size-md);
  border-radius: var(--radius-md);
  cursor: pointer;
  /* الحفاظ على الألوان الأصلية */
  background-color: var(--primary-color);
  color: rgb(255, 255, 255);
  border: none;
  transition: all var(--transition-normal);
  font-family: var(--font-family-primary);
  font-weight: var(--font-weight-light) !important;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

button:hover:not(:disabled) {
  /* تحسين بسيط للتفاعل */
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

/* زر الإرسال - Submit Button مع الحفاظ على الألوان الأصلية */
.submit-btn {
  /* استخدام المتغيرات للخصائص المشتركة */
  padding: 12px 20px;
  border: none;
  border-radius: var(--radius-md);
  cursor: pointer;
  font-size: 16px;
  width: 100%;
  transition: all var(--transition-normal);
  font-family: var(--font-family-primary);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  /* الحفاظ على الألوان الأصلية */
  background-color: var(--secondary-color);
  color: white;
  box-shadow: var(--shadow-sm);
}

.submit-btn:hover:not(:disabled) {
  background-color: var(--secondary-dark);
  box-shadow: var(--shadow-md);
  transform: translateY(-2px);
}

/* إعادة تعريف للتوافق مع الكود الموجود */
button:active {
  transform: translateY(0);
}

h2 {
  margin-bottom: 5px;
  font-size: 24px;
  color: #333;
}

p {
  margin-bottom: 5px;
  font-size: 16px;
  color: #555;
}

/* استخدام نظام النماذج الموحد - Use Unified Form System */
.case-registration-container label {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--current-text-primary);
  margin-bottom: var(--spacing-sm);
  margin-top: var(--spacing-lg);
  display: block;
}

.case-registration-container input[type="text"],
.case-registration-container input[type="date"],
.case-registration-container input[type="email"],
.case-registration-container input[type="password"],
.case-registration-container input[type="number"],
.case-registration-container input[type="tel"],
.case-registration-container textarea {
  /* استخدام فئات النماذج الموحدة */
  padding: var(--input-padding);
  border-radius: var(--input-border-radius);
  font-size: var(--input-font-size);
  font-family: var(--font-family-primary);
  min-height: var(--input-min-height);
  width: 100%;
  box-sizing: border-box;
  transition: var(--input-transition);
  background-color: var(--input-bg);
  border: var(--input-border);
  color: var(--input-color);
  margin-bottom: var(--spacing-lg);
  direction: rtl;
}

.case-registration-container input:focus,
.case-registration-container textarea:focus {
  outline: none;
  background-color: var(--input-bg-focus);
  border: var(--input-border-focus);
  box-shadow: var(--input-shadow-focus);
}

/* مجموعة الإدخال - Input Group */
.input-group {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
  margin-bottom: var(--spacing-lg);
}

/* قوائم الاختيار - Select Elements */
.input-group select,
select {
  padding: var(--input-padding);
  border-radius: var(--input-border-radius);
  font-size: var(--input-font-size);
  font-family: var(--font-family-primary);
  min-height: var(--input-min-height);
  width: 100%;
  box-sizing: border-box;
  transition: var(--input-transition);
  background-color: var(--input-bg);
  border: var(--input-border);
  color: var(--input-color);
  appearance: none;
  background-repeat: no-repeat;
  background-position: left 16px center;
  padding-left: 40px;
  direction: rtl;
}

.input-group select:focus,
select:focus {
  outline: none;
  background-color: var(--input-bg-focus);
  border: var(--input-border-focus);
  box-shadow: var(--input-shadow-focus);
}

.case-number-inputs {
  display: flex;
  align-items: center;
  gap: 10px;
}

.case-number-inputs input {
  flex: 1;
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
}

.separator {
  font-weight: bold;
}

.generated-number {
  margin-top: 5px;
  padding: 8px;
  background-color: #f5f5f5;
  border-radius: 4px;
}

label {
  display: block;
  font-weight: bold;
  margin-top: 5px;
}
/* تم تعريف أنماط الأزرار بالفعل في الأعلى */
/* تنسيقات Progress Bar */
.progress-bar-container {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  width: 100%;
  height: 4px;
  background: #e0e0e0;
  z-index: 1000;
}

.progress-bar {
  height: 100%;
  background: #4CAF50;
  width: 0%;
  transition: width 0.4s ease;
  border-radius: 0 2px 2px 0;
}

/* تعديلات للهيكل العام */
.container {
  display: flex;
  flex-direction: row;
  width: 100%;
  max-width: 1200px;
  margin: auto;
  padding: 20px;
  gap: 20px;
  box-sizing: border-box;
  position: relative;
  padding-top: 10px; /* إضافة مساحة للـ Progress Bar */
}

