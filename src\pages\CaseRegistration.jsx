import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import TopBar from "../components/topbar/TopBar";
import { caseDegrees, caseCategoriesByDegree, courtLocations, caseStatuses } from "../utils/CaseFilters";
import './CaseRegistration.css';
import { db } from '../config/firebaseConfig';
import { collection, addDoc, query, where, getDocs } from "firebase/firestore";
import { getActiveAccount, addCase, getCases, updateCase } from '../services/StorageService';
import { notifyCaseCreated } from '../utils/CacheManager';

// Google Material Icons Components
const ErrorIcon = () => (
  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
    <circle cx="12" cy="12" r="10"></circle>
    <line x1="15" y1="9" x2="9" y2="15"></line>
    <line x1="9" y1="9" x2="15" y2="15"></line>
  </svg>
);

const SuccessIcon = () => (
  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
    <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
    <polyline points="22 4 12 14.01 9 11.01"></polyline>
  </svg>
);

const SaveIcon = () => (
  <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" style={{ marginRight: '8px' }}>
    <path d="M19 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h11l5 5v11a2 2 0 0 1-2 2z"></path>
    <polyline points="17 21 17 13 7 13 7 21"></polyline>
    <polyline points="7 3 7 8 15 8"></polyline>
  </svg>
);

const CancelIcon = () => (
  <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" style={{ marginRight: '8px' }}>
    <line x1="18" y1="6" x2="6" y2="18"></line>
    <line x1="6" y1="6" x2="18" y2="18"></line>
  </svg>
);

const CaseRegistration = ({ casesList = [], setCasesList = () => {}, currentUser }) => {
  console.log('CaseRegistration component rendered');

  const currentYear = new Date().getFullYear().toString();

  const [caseData, setCaseData] = useState({
    caseNumber: '',
    caseYear: currentYear,
    clientName: '',
    caseDescription: '',
    caseCategory: '',
    opponentName: '',
    caseDegree: '',
    circleNumber: '',
    courtLocation: '',
    caseStatus: 'قيد النظر',
    reportNumber: '',
    reportLocation: '',
    caseDate: '', // تاريخ رفع الدعوى أو تاريخ المحضر
    originalCaseId: '', // معرف القضية الأصلية
    originalCaseDegree: '', // درجة القضية الأصلية
    originalCaseNumber: '' // رقم القضية الأصلية
  });

  const [fullCaseNumber, setFullCaseNumber] = useState('');
  const [isDegreeModified, setIsDegreeModified] = useState(false);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(null);
  const [isTransferCase, setIsTransferCase] = useState(false);
  const [currentStep, setCurrentStep] = useState(1);
  const [isFormValid, setIsFormValid] = useState(false);
  const navigate = useNavigate();

  // تحميل البيانات المنقولة من تحويل الدرجة
  useEffect(() => {
    const transferData = localStorage.getItem('transferData');
    if (transferData) {
      try {
        const parsedData = JSON.parse(transferData);
        setCaseData(prev => ({
          ...prev,
          clientName: parsedData.clientName || '',
          caseDescription: parsedData.caseDescription || '',
          opponentName: parsedData.opponentName || '',
          caseCategory: parsedData.caseCategory || '',
          courtLocation: parsedData.courtLocation || '',
          caseStatus: 'دعوى قضائية', // تحويل الدرجة يعني دعوى قضائية
          originalCaseId: parsedData.originalCaseId || '',
          originalCaseDegree: parsedData.originalCaseDegree || '',
          originalCaseNumber: parsedData.originalCaseNumber || '',
          originalCourtLocation: parsedData.originalCourtLocation || '',
          originalCaseDate: parsedData.originalCaseDate || '',
          // حفظ البيانات الأساسية الأصلية
          originalClientName: parsedData.originalClientName || '',
          originalOpponentName: parsedData.originalOpponentName || '',
          originalCaseDescription: parsedData.originalCaseDescription || '',
          originalCaseCategory: parsedData.originalCaseCategory || '',
          originalCircleNumber: parsedData.originalCircleNumber || ''
        }));
        setIsTransferCase(true);
        // مسح البيانات من localStorage بعد التحميل
        localStorage.removeItem('transferData');
      } catch (error) {
        console.error('خطأ في تحميل بيانات التحويل:', error);
      }
    }
  }, []);

  useEffect(() => {
    if (!isDegreeModified && caseData.courtLocation) {
      const selectedCourt = courtLocations.find(court => court.name === caseData.courtLocation);
      if (selectedCourt) {
        setCaseData(prev => ({ ...prev, caseDegree: selectedCourt.degree }));
      }
    }
  }, [caseData.courtLocation, isDegreeModified]);

  useEffect(() => {
    if (caseData.caseDegree && caseCategoriesByDegree[caseData.caseDegree]) {
      if (!caseCategoriesByDegree[caseData.caseDegree].includes(caseData.caseCategory)) {
        setCaseData(prev => ({
          ...prev,
          caseCategory: caseCategoriesByDegree[caseData.caseDegree][0]
        }));
      }
    }
  }, [caseData.caseDegree]);

  useEffect(() => {
    if (caseData.caseStatus === 'دعوى قضائية') {
      setFullCaseNumber(
        caseData.caseNumber ? `${caseData.caseNumber}/${caseData.caseYear}` : caseData.caseYear
      );
    } else if (caseData.caseStatus === 'محضر') {
      setFullCaseNumber(
        caseData.reportNumber ? `${caseData.reportNumber}/${caseData.caseYear}` : caseData.caseYear
      );
    } else {
      setFullCaseNumber('');
    }
  }, [caseData.caseNumber, caseData.caseYear, caseData.reportNumber, caseData.caseStatus]);

  const handleChange = (e) => {
    const { name, value } = e.target;
    setCaseData(prev => ({ ...prev, [name]: value }));
    if (name === 'caseDegree') setIsDegreeModified(true);
  };

  // دالة لتحديث الأرشيف الزمني الموحد للملفات المحولة
  const updateSharedTimeline = async (originalCaseId, newCaseId, newCaseDegree, caseDate, newCaseNumber) => {
    try {
      console.log('بدء تحديث الأرشيف الزمني الموحد...');
      console.log('معرف القضية الأصلية:', originalCaseId);
      console.log('معرف القضية الجديدة:', newCaseId);
      console.log('الدرجة الجديدة:', newCaseDegree);
      console.log('تاريخ القضية:', caseDate);
      console.log('رقم القضية الجديد:', newCaseNumber);

      // الحصول على جميع القضايا للعثور على الملفات المرتبطة
      const allCases = await getCases(currentUser.uid);
      console.log('إجمالي القضايا:', allCases.length);
      console.log('البحث عن القضية الأصلية بمعرف:', originalCaseId);
      console.log('قائمة معرفات جميع القضايا:', allCases.map(c => c.id));

      // البحث عن القضية الأصلية أولاً
      const originalCase = allCases.find(c => c.id === originalCaseId);

      // البحث عن جميع القضايا المرتبطة
      const relatedCases = allCases.filter(c =>
        c.originalCaseId === originalCaseId ||  // القضايا المحولة من نفس الأصل
        c.id === originalCaseId ||              // القضية الأصلية نفسها
        c.id === newCaseId                      // القضية الجديدة المحولة
      );

      console.log('القضايا المرتبطة الموجودة:', relatedCases.map(c => ({ id: c.id, degree: c.caseDegree, number: c.fullCaseNumber })));

      if (relatedCases.length === 0) {
        console.warn('لم يتم العثور على قضايا مرتبطة للتحديث');
        console.log('سيتم تحديث القضية الجديدة فقط:', newCaseId);

        // إذا لم توجد قضايا مرتبطة، نحدث القضية الجديدة فقط
        const newCase = allCases.find(c => c.id === newCaseId);
        if (newCase) {
          relatedCases.push(newCase);
        }
      }

      // تحديد نص الحدث حسب الدرجة الجديدة
      let actionText = '';
      switch (newCaseDegree) {
        case 'استئنافي':
          actionText = 'تم رفع استئناف';
          break;
        case 'نقض':
          actionText = 'تم رفع نقض';
          break;
        case 'إعادة نظر':
          actionText = 'تم رفع إعادة نظر';
          break;
        default:
          actionText = `تم تحويل الدرجة إلى ${newCaseDegree}`;
      }

      // إنشاء حدث جديد للأرشيف الزمني الموحد (استخدام history بدلاً من timeline)
      const historyEntry = {
        type: 'degree_transfer',
        timestamp: new Date().toISOString(),
        action: `${actionText} بتاريخ ${new Date(caseDate).toLocaleDateString('ar-EG')} برقم ${newCaseNumber}`,
        date: new Date(caseDate).toLocaleDateString('ar-EG'),
        transferDate: caseDate,
        newCaseNumber: newCaseNumber,
        newCaseDegree: newCaseDegree,
        relatedCaseId: newCaseId,
        isSharedHistory: true,
        // حفظ البيانات القديمة للمقارنة لاحقاً
        oldData: {
          clientName: originalCase?.clientName,
          opponentName: originalCase?.opponentName,
          caseDescription: originalCase?.caseDescription,
          caseCategory: originalCase?.caseCategory,
          courtLocation: originalCase?.courtLocation,
          caseDegree: originalCase?.caseDegree,
          fullCaseNumber: originalCase?.fullCaseNumber,
          caseDate: originalCase?.caseDate,
          circleNumber: originalCase?.circleNumber
        }
      };

      // تحديث الأرشيف الزمني لجميع القضايا المرتبطة
      console.log('بدء تحديث', relatedCases.length, 'قضايا مرتبطة...');

      const updatePromises = relatedCases.map(async (caseData) => {
        console.log('تحديث القضية:', caseData.id, '-', caseData.fullCaseNumber);

        // إنشاء أرشيف أولي إذا لم يكن موجوداً (للقضايا القديمة)
        let initialHistory = [...(caseData.history || [])];

        // إذا لم يكن هناك أرشيف، أنشئ إدخال واحد فقط حسب نوع القضية
        if (initialHistory.length === 0) {
          if (caseData.caseStatus === 'محضر' && caseData.caseDate) {
            // للمحضر: حدث واحد فقط بتاريخ المحضر
            const reportEntry = {
              type: 'report_created',
              timestamp: new Date(caseData.caseDate).toISOString(),
              action: `تم تحرير المحضر بتاريخ ${new Date(caseData.caseDate).toLocaleDateString('ar-EG')}`,
              date: new Date(caseData.caseDate).toLocaleDateString('ar-EG'),
              reportDate: caseData.caseDate
            };
            initialHistory.push(reportEntry);
          } else if (caseData.caseStatus === 'دعوى قضائية' && caseData.caseDate) {
            // للدعوى: حدث واحد فقط بتاريخ رفع الدعوى
            const lawsuitEntry = {
              type: 'lawsuit_created',
              timestamp: new Date(caseData.caseDate).toISOString(),
              action: `تم رفع دعوى قضائية بتاريخ ${new Date(caseData.caseDate).toLocaleDateString('ar-EG')}`,
              date: new Date(caseData.caseDate).toLocaleDateString('ar-EG'),
              lawsuitDate: caseData.caseDate
            };
            initialHistory.push(lawsuitEntry);
          } else {
            // لقيد النظر أو القضايا بدون تاريخ: حدث إنشاء عادي
            const creationEntry = {
              type: 'case_created',
              timestamp: caseData.createdAt || new Date().toISOString(),
              action: `تم إنشاء ${caseData.caseStatus}`,
              date: new Date(caseData.createdAt || new Date()).toLocaleDateString('ar-EG')
            };
            initialHistory.push(creationEntry);
          }
        }

        const updatedHistory = [...initialHistory, historyEntry];
        const updatedCaseData = {
          ...caseData,
          history: updatedHistory,
          updatedAt: new Date().toISOString()
        };

        console.log('الأرشيف الزمني الجديد للقضية', caseData.id, ':', updatedHistory.length, 'أحداث');
        console.log('الأحداث الأصلية:', caseData.history?.length || 0);
        console.log('الأحداث الأولية (مع الإنشاء):', initialHistory.length);
        console.log('الأحداث النهائية (مع تحويل الدرجة):', updatedHistory.length);

        return updateCase(currentUser.uid, caseData.id, updatedCaseData);
      });

      await Promise.all(updatePromises);
      console.log('تم تحديث الأرشيف الزمني الموحد لجميع الملفات المرتبطة بنجاح');
    } catch (error) {
      console.error('خطأ في تحديث الأرشيف الزمني الموحد:', error);
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setError(null);
    setSuccess(null);

    if (!currentUser) {
      alert("يجب تسجيل الدخول لحفظ القضايا.");
      navigate('/login');
      return;
    }

    if (!caseData.clientName.trim()) {
      setError('يجب إدخال اسم الموكل');
      return;
    }

    let generatedFullCaseNumber = '';
    if (caseData.caseStatus === 'قيد النظر') {
      generatedFullCaseNumber = `قيد النظر-${Date.now()}`;
    } else if (caseData.caseStatus === 'محضر') {
      if (!caseData.reportNumber.trim()) {
        setError('يجب إدخال رقم المحضر.');
        return;
      }
      if (!caseData.caseYear || !/^\d{4}$/.test(caseData.caseYear)) {
        setError('السنة يجب أن تكون مكونة من 4 أرقام.');
        return;
      }
      generatedFullCaseNumber = `${caseData.reportNumber.trim()}/${caseData.caseYear}`;
    } else if (caseData.caseStatus === 'دعوى قضائية') {
      if (!caseData.caseNumber.trim()) {
        setError('يجب إدخال رقم القضية.');
        return;
      }
      if (!caseData.caseYear || !/^\d{4}$/.test(caseData.caseYear)) {
        setError('السنة يجب أن تكون مكونة من 4 أرقام.');
        return;
      }
      generatedFullCaseNumber = `${caseData.caseNumber.trim()}/${caseData.caseYear}`;
    }

    // التحقق من تكرار رقم القضية حسب الحساب النشط
    const activeAccount = getActiveAccount();

    if (activeAccount === 'online') {
      // التحقق من تكرار رقم القضية في Firestore
      try {
        const casesQuery = query(
          collection(db, 'cases'),
          where('userId', '==', currentUser.uid),
          where('fullCaseNumber', '==', generatedFullCaseNumber)
        );
        const querySnapshot = await getDocs(casesQuery);
        if (!querySnapshot.empty) {
          setError('رقم القضية موجود بالفعل! يرجى اختيار رقم قضية مختلف.');
          return;
        }
      } catch (e) {
        setError('حدث خطأ أثناء التحقق من رقم القضية: ' + e.message);
        return;
      }
    } else {
      // التحقق من تكرار رقم القضية في التخزين المحلي
      const localCases = await getCases(currentUser.uid);
      const duplicateCase = localCases.find(c => c.fullCaseNumber === generatedFullCaseNumber);
      if (duplicateCase) {
        setError('رقم القضية موجود بالفعل في الحساب المحلي! يرجى اختيار رقم قضية مختلف.');
        return;
      }
    }

    if (caseData.caseStatus === 'دعوى قضائية') {
      if (!caseData.circleNumber.trim()) { setError('يجب إدخال رقم الدائرة.'); return; }
      if (!caseData.caseDegree) { setError('يجب اختيار درجة الدعوى.'); return; }
      if (!caseData.caseCategory) { setError('يجب اختيار نوع الدعوى.'); return; }
      if (!caseData.courtLocation) { setError('يجب اختيار مكان المحكمة.'); return; }
      if (!caseData.caseDescription.trim()) { setError('يجب إدخال الوصف.'); return; }
      if (!caseData.caseDate.trim()) { setError('يجب إدخال تاريخ رفع الدعوى.'); return; }
    }

    if (caseData.caseStatus === 'محضر') {
      if (!caseData.reportLocation.trim()) { setError('يجب إدخال مكان الجهة المختصة.'); return; }
      if (!caseData.reportNumber.trim()) { setError('يجب إدخال رقم المحضر.'); return; }
      if (!caseData.caseDescription.trim()) { setError('يجب إدخال وصف المحضر.'); return; }
      if (!caseData.caseDate.trim()) { setError('يجب إدخال تاريخ المحضر.'); return; }
    }

    if (caseData.caseStatus === 'قيد النظر') {
      if (!caseData.reportLocation.trim()) { setError('يجب إدخال مكان الجهة المختصة.'); return; }
      if (!caseData.caseDescription.trim()) { setError('يجب إدخال الوصف القضائي.'); return; }
    }

    const confirmSave = window.confirm('هل أنت متأكد من حفظ القضية؟');
    if (!confirmSave) {
      return;
    }

    // إنشاء أرشيف أولي للقضية الجديدة ونسخ بيانات المحضر
    let initialHistory = [];
    let originalReportData = {}; // لحفظ بيانات المحضر من القضية الأصلية

    // إذا كانت قضية تحويل درجة، نسخ الأحداث وبيانات المحضر من القضية الأصلية
    if (isTransferCase && caseData.originalCaseId) {
      try {
        // الحصول على القضية الأصلية
        const allCases = await getCases(currentUser.uid);
        const originalCase = allCases.find(c => c.id === caseData.originalCaseId);

        if (originalCase) {
          // نسخ بيانات المحضر إذا كانت موجودة
          if (originalCase.reportNumber || originalCase.reportLocation) {
            originalReportData = {
              reportNumber: originalCase.reportNumber,
              reportLocation: originalCase.reportLocation,
              reportYear: originalCase.caseYear // سنة المحضر
            };
            console.log('تم نسخ بيانات المحضر:', originalReportData);
          }

          if (originalCase.history && originalCase.history.length > 0) {
            // نسخ جميع الأحداث من القضية الأصلية
            initialHistory = [...originalCase.history];
            console.log('تم نسخ', initialHistory.length, 'أحداث من القضية الأصلية');
          } else {
            // إنشاء أرشيف أولي للقضية الأصلية إذا لم يكن لديها أرشيف
            if (originalCase.caseStatus === 'محضر' && originalCase.caseDate) {
              const reportEntry = {
                type: 'report_created',
                timestamp: new Date(originalCase.caseDate).toISOString(),
                action: `تم تحرير المحضر بتاريخ ${new Date(originalCase.caseDate).toLocaleDateString('ar-EG')}`,
                date: new Date(originalCase.caseDate).toLocaleDateString('ar-EG'),
                reportDate: originalCase.caseDate
              };
              initialHistory.push(reportEntry);
            } else if (originalCase.caseStatus === 'دعوى قضائية' && originalCase.caseDate) {
              const lawsuitEntry = {
                type: 'lawsuit_created',
                timestamp: new Date(originalCase.caseDate).toISOString(),
                action: `تم رفع دعوى قضائية بتاريخ ${new Date(originalCase.caseDate).toLocaleDateString('ar-EG')}`,
                date: new Date(originalCase.caseDate).toLocaleDateString('ar-EG'),
                lawsuitDate: originalCase.caseDate
              };
              initialHistory.push(lawsuitEntry);
            }
          }
        }
      } catch (error) {
        console.error('خطأ في نسخ الأحداث وبيانات المحضر من القضية الأصلية:', error);
      }
    }

    // إضافة حدث القضية الجديدة
    if (caseData.caseStatus === 'دعوى قضائية' && caseData.caseDate) {
      // للدعوى: حدث رفع الدعوى الجديدة
      const lawsuitEntry = {
        type: 'lawsuit_created',
        timestamp: new Date(caseData.caseDate).toISOString(),
        action: `تم رفع دعوى قضائية بتاريخ ${new Date(caseData.caseDate).toLocaleDateString('ar-EG')}`,
        date: new Date(caseData.caseDate).toLocaleDateString('ar-EG'),
        lawsuitDate: caseData.caseDate
      };
      initialHistory.push(lawsuitEntry);
    } else if (!isTransferCase) {
      // للقضايا الجديدة غير المحولة
      if (caseData.caseStatus === 'محضر' && caseData.caseDate) {
        const reportEntry = {
          type: 'report_created',
          timestamp: new Date(caseData.caseDate).toISOString(),
          action: `تم تحرير المحضر بتاريخ ${new Date(caseData.caseDate).toLocaleDateString('ar-EG')}`,
          date: new Date(caseData.caseDate).toLocaleDateString('ar-EG'),
          reportDate: caseData.caseDate
        };
        initialHistory.push(reportEntry);
      } else {
        const creationEntry = {
          type: 'case_created',
          timestamp: new Date().toISOString(),
          action: `تم إنشاء ${caseData.caseStatus}`,
          date: new Date().toLocaleDateString('ar-EG')
        };
        initialHistory.push(creationEntry);
      }
    }

    const caseDataToSave = {
      fullCaseNumber: generatedFullCaseNumber,
      caseNumber: caseData.caseStatus === 'دعوى قضائية' ? caseData.caseNumber.trim() : null,
      caseYear: caseData.caseStatus === 'قيد النظر' ? currentYear : caseData.caseYear,
      clientName: caseData.clientName.trim(),
      opponentName: caseData.opponentName.trim() || null,
      caseDescription: caseData.caseDescription.trim() || null,
      caseCategory: caseData.caseCategory || null,
      caseDegree: caseData.caseDegree || null,
      courtLocation: caseData.courtLocation || null,
      circleNumber: caseData.caseStatus === 'دعوى قضائية' ? caseData.circleNumber.trim() || null : null,
      caseDate: (caseData.caseStatus === 'دعوى قضائية' || caseData.caseStatus === 'محضر') ? caseData.caseDate.trim() || null : null,
      caseStatus: caseData.caseStatus || 'قيد النظر',
      reportNumber: caseData.caseStatus === 'محضر' ? caseData.reportNumber.trim() || null : originalReportData.reportNumber || null,
      reportLocation: (caseData.caseStatus === 'محضر' || caseData.caseStatus === 'قيد النظر') ? caseData.reportLocation.trim() || null : originalReportData.reportLocation || null,
      reportYear: originalReportData.reportYear || null, // إضافة سنة المحضر
      deferrals: [],
      actions: [],
      history: initialHistory,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      userId: currentUser.uid,
      // بيانات تحويل الدرجة
      originalCaseId: caseData.originalCaseId || null,
      originalCaseDegree: caseData.originalCaseDegree || null,
      originalCaseNumber: caseData.originalCaseNumber || null,
      originalCourtLocation: caseData.originalCourtLocation || null,
      originalCaseDate: caseData.originalCaseDate || null,
      // حفظ البيانات الأساسية الأصلية للمقارنة
      originalClientName: caseData.originalClientName || null,
      originalOpponentName: caseData.originalOpponentName || null,
      originalCaseDescription: caseData.originalCaseDescription || null,
      originalCaseCategory: caseData.originalCaseCategory || null,
      originalCircleNumber: caseData.originalCircleNumber || null,
      isTransferredCase: isTransferCase
    };

    setLoading(true);
    try {
      // جلب معرفات المجموعات التي ينتمي إليها المستخدم
      let groupId = null;
      try {
        const groups = await import('../services/GroupsService').then(mod => mod.getGroups(currentUser.uid));
        const resolvedGroups = await groups;
        if (Array.isArray(resolvedGroups) && resolvedGroups.length > 0) {
          // إذا كان المستخدم في أكثر من مجموعة، اختر أول مجموعة (يمكنك تعديل المنطق لاحقاً)
          groupId = resolvedGroups[0].id;
        }
      } catch (e) {
        groupId = null;
      }
      // استخدام خدمة التخزين المحلي/أونلاين لإضافة القضية مع groupId
      const savedCase = await addCase(currentUser.uid, caseDataToSave, groupId);
      console.log("Case saved with ID: ", savedCase.id);
      console.log('الأرشيف الأولي للقضية الجديدة:', initialHistory.length, 'أحداث');
      console.log('بيانات المحضر المنسوخة:', originalReportData);
      console.log('البيانات المحفوظة النهائية:', caseDataToSave);
      
      // إشعار بإنشاء قضية جديدة لتحديث جميع الصفحات
      notifyCaseCreated(currentUser.uid);

      // إذا كانت قضية تحويل درجة، تحديث الأرشيف الزمني الموحد للملفات المرتبطة
      if (isTransferCase && caseData.originalCaseId && caseData.caseDegree && caseData.caseDate) {
        console.log('شروط تحديث الأرشيف متوفرة:');
        console.log('- isTransferCase:', isTransferCase);
        console.log('- originalCaseId:', caseData.originalCaseId);
        console.log('- caseDegree:', caseData.caseDegree);
        console.log('- caseDate:', caseData.caseDate);
        console.log('- generatedFullCaseNumber:', generatedFullCaseNumber);

        await updateSharedTimeline(
          caseData.originalCaseId,
          savedCase.id,
          caseData.caseDegree,
          caseData.caseDate,
          generatedFullCaseNumber
        );
      } else {
        console.log('شروط تحديث الأرشيف غير متوفرة:');
        console.log('- isTransferCase:', isTransferCase);
        console.log('- originalCaseId:', caseData.originalCaseId);
        console.log('- caseDegree:', caseData.caseDegree);
        console.log('- caseDate:', caseData.caseDate);
      }

      setCaseData({
        caseNumber: '',
        caseYear: currentYear,
        clientName: '',
        caseDescription: '',
        caseCategory: '',
        opponentName: '',
        caseDegree: '',
        circleNumber: '',
        courtLocation: '',
        caseStatus: 'قيد النظر',
        reportNumber: '',
        reportLocation: '',
        caseDate: ''
      });
      setFullCaseNumber('');
      setIsDegreeModified(false);

      // عرض رسالة نجاح مختلفة حسب الحساب النشط ونوع العملية
      if (isTransferCase) {
        setSuccess('تم تحويل الدرجة القضائية بنجاح وتحديث الأرشيف الزمني الموحد!');
      } else if (activeAccount === 'online') {
        setSuccess('تم حفظ القضية بنجاح في الحساب الأونلاين!');
      } else {
        setSuccess('تم حفظ القضية بنجاح في الحساب المحلي!');
      }

      setTimeout(() => {
        navigate(`/case-details/${savedCase.id}`);
      }, 1500);
    } catch (e) {
      console.error("Error saving case: ", e);
      setError("حدث خطأ أثناء حفظ القضية: " + e.message);
    } finally {
      setLoading(false);
    }
  };

  const handleCancel = () => {
    navigate('/dashboard', { state: { refresh: true } }); // تمرير حالة لتحديث البيانات
  };

  // تصنيف الحقول حسب النوع للتنظيم بشكل أفضل
  const getStepTitle = (step) => {
    switch(step) {
      case 1: return 'نوع القضية';
      case 2: return 'بيانات الأطراف';
      case 3: return 'التفاصيل القانونية';
      case 4: return 'الوصف والملاحظات';
      default: return '';
    }
  };

  // التحقق من صحة البيانات حسب الخطوة الحالية
  useEffect(() => {
    let valid = false;
    switch(currentStep) {
      case 1:
        valid = !!caseData.caseStatus;
        if (caseData.caseStatus === 'دعوى قضائية') {
          valid = valid && !!caseData.caseNumber && !!caseData.caseYear;
        } else if (caseData.caseStatus === 'محضر') {
          valid = valid && !!caseData.reportNumber && !!caseData.caseYear;
        }
        break;
      case 2:
        valid = !!caseData.clientName.trim();
        break;
      case 3:
        if (caseData.caseStatus === 'دعوى قضائية') {
          valid = !!caseData.circleNumber && !!caseData.courtLocation && !!caseData.caseDegree && !!caseData.caseCategory && !!caseData.caseDate;
        } else if (caseData.caseStatus === 'محضر') {
          valid = !!caseData.reportLocation && !!caseData.caseDate;
        } else if (caseData.caseStatus === 'قيد النظر') {
          valid = !!caseData.reportLocation;
        }
        break;
      case 4:
        valid = !!caseData.caseDescription.trim();
        break;
    }
    setIsFormValid(valid);
  }, [currentStep, caseData]);

  const nextStep = () => {
    if (currentStep < 4 && isFormValid) {
      setCurrentStep(currentStep + 1);
    }
  };

  const prevStep = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  const renderStepContent = () => {
    switch(currentStep) {
      case 1:
        return (
          <div className="step-content">
            <div className="form-section">
              <h3>اختر نوع القضية</h3>
              <div className="status-cards">
                {caseStatuses.map(status => (
                  <div
                    key={status}
                    className={`status-card ${caseData.caseStatus === status ? 'active' : ''}`}
                    onClick={() => setCaseData(prev => ({ ...prev, caseStatus: status }))}
                  >
                    <div className="status-icon">
                      {status === 'دعوى قضائية' && '⚖️'}
                      {status === 'محضر' && '📋'}
                      {status === 'قيد النظر' && '⏳'}
                    </div>
                    <div className="status-title">{status}</div>
                  </div>
                ))}
              </div>
              
              {/* تعليق توضيحي لحالة قيد النظر */}
              {caseData.caseStatus === 'قيد النظر' && (
                <div className="info-note">
                  <div className="note-icon">💡</div>
                  <div className="note-content">
                    <p><strong>ملاحظة مهمة:</strong></p>
                    <p>يتم استخدام نوع "قيد النظر" ليعبر عن حالة الملف وهو كل الأعمال الغير متعلقة بالمحاضر أو الدعاوى القضائية.</p>
                    <p>في حالة تغيير ذلك العمل إلى محضر أو دعوى قضائية، يمكنك الانتقال وتغيير الحالة من خلال صفحة تفاصيل بيانات الملف.</p>
                  </div>
                </div>
              )}
            </div>

            {caseData.caseStatus === 'دعوى قضائية' && (
              <div className="form-section">
                <h4>رقم القضية</h4>
                <div className="case-number-container">
                  <div className="number-inputs">
                    <input
                      type="text"
                      name="caseNumber"
                      value={caseData.caseNumber}
                      onChange={handleChange}
                      placeholder="رقم القضية"
                      className="number-input"
                    />
                    <span className="separator">/</span>
                    <input
                      type="text"
                      name="caseYear"
                      value={caseData.caseYear}
                      onChange={handleChange}
                      placeholder="السنة"
                      className="year-input"
                    />
                  </div>
                  {fullCaseNumber && (
                    <div className="generated-display">
                      <span>رقم القضية الكامل: </span>
                      <strong>{fullCaseNumber}</strong>
                    </div>
                  )}
                </div>
              </div>
            )}

            {caseData.caseStatus === 'محضر' && (
              <div className="form-section">
                <h4>رقم المحضر</h4>
                <div className="case-number-container">
                  <div className="number-inputs">
                    <input
                      type="text"
                      name="reportNumber"
                      value={caseData.reportNumber}
                      onChange={handleChange}
                      placeholder="رقم المحضر"
                      className="number-input"
                    />
                    <span className="separator">/</span>
                    <input
                      type="text"
                      name="caseYear"
                      value={caseData.caseYear}
                      onChange={handleChange}
                      placeholder="السنة"
                      className="year-input"
                    />
                  </div>
                  {fullCaseNumber && (
                    <div className="generated-display">
                      <span>رقم المحضر الكامل: </span>
                      <strong>{fullCaseNumber}</strong>
                    </div>
                  )}
                </div>
              </div>
            )}
          </div>
        );

      case 2:
        return (
          <div className="step-content">
            <div className="form-section">
              <h3>بيانات الأطراف</h3>
              <div className="input-row">
                <div className="input-field">
                  <label>اسم الموكل <span className="required">*</span></label>
                  <input
                    type="text"
                    name="clientName"
                    value={caseData.clientName}
                    onChange={handleChange}
                    placeholder="أدخل اسم الموكل"
                  />
                </div>
                <div className="input-field">
                  <label>اسم الخصم <span className="optional">(اختياري)</span></label>
                  <input
                    type="text"
                    name="opponentName"
                    value={caseData.opponentName}
                    onChange={handleChange}
                    placeholder="أدخل اسم الخصم"
                  />
                </div>
              </div>
            </div>
          </div>
        );

      case 3:
        return (
          <div className="step-content">
            <div className="form-section">
              <h3>التفاصيل القانونية</h3>
              
              {caseData.caseStatus === 'دعوى قضائية' && (
                <>
                  <div className="input-row">
                    <div className="input-field">
                      <label>رقم الدائرة <span className="required">*</span></label>
                      <input
                        type="text"
                        name="circleNumber"
                        value={caseData.circleNumber}
                        onChange={handleChange}
                        placeholder="أدخل رقم الدائرة"
                      />
                    </div>
                    <div className="input-field">
                      <label>تاريخ رفع الدعوى <span className="required">*</span></label>
                      <input
                        type="date"
                        name="caseDate"
                        value={caseData.caseDate}
                        onChange={handleChange}
                      />
                    </div>
                  </div>
                  
                  <div className="input-row">
                    <div className="input-field">
                      <label>المحكمة <span className="required">*</span></label>
                      <select
                        name="courtLocation"
                        value={caseData.courtLocation}
                        onChange={handleChange}
                      >
                        <option value="">اختر المحكمة</option>
                        {courtLocations.map(court => (
                          <option key={court.name} value={court.name}>{court.name}</option>
                        ))}
                      </select>
                    </div>
                    <div className="input-field">
                      <label>الدرجة <span className="required">*</span></label>
                      <select
                        name="caseDegree"
                        value={caseData.caseDegree}
                        onChange={handleChange}
                      >
                        <option value="">اختر درجة الدعوى</option>
                        {caseDegrees.map(degree => (
                          <option key={degree} value={degree}>{degree}</option>
                        ))}
                      </select>
                      {caseData.courtLocation && !isDegreeModified && (
                        <small className="helper-note">سيتم تعيين الدرجة تلقائياً حسب المحكمة</small>
                      )}
                    </div>
                  </div>

                  <div className="input-row">
                    <div className="input-field full-width">
                      <label>نوع الدعوى <span className="required">*</span></label>
                      <select
                        name="caseCategory"
                        value={caseData.caseCategory}
                        onChange={handleChange}
                        disabled={!caseData.caseDegree}
                      >
                        <option value="">اختر نوع الدعوى</option>
                        {caseData.caseDegree && caseCategoriesByDegree[caseData.caseDegree]?.map(category => (
                          <option key={category} value={category}>{category}</option>
                        ))}
                      </select>
                    </div>
                  </div>
                </>
              )}

              {(caseData.caseStatus === 'محضر' || caseData.caseStatus === 'قيد النظر') && (
                <>
                  <div className="input-row">
                    <div className="input-field">
                      <label>مكان الجهة المختصة <span className="required">*</span></label>
                      <input
                        type="text"
                        name="reportLocation"
                        value={caseData.reportLocation}
                        onChange={handleChange}
                        placeholder="أدخل مكان الجهة المختصة"
                      />
                    </div>
                    {caseData.caseStatus === 'محضر' && (
                      <div className="input-field">
                        <label>تاريخ المحضر <span className="required">*</span></label>
                        <input
                          type="date"
                          name="caseDate"
                          value={caseData.caseDate}
                          onChange={handleChange}
                        />
                      </div>
                    )}
                  </div>
                </>
              )}
            </div>
          </div>
        );

      case 4:
        return (
          <div className="step-content">
            <div className="form-section">
              <h3>الوصف والملاحظات</h3>
              <div className="input-field full-width">
                <label>
                  وصف {caseData.caseStatus === 'دعوى قضائية' ? 'الدعوى' : caseData.caseStatus === 'محضر' ? 'المحضر' : 'القضية'} 
                  <span className="required">*</span>
                </label>
                <textarea
                  name="caseDescription"
                  value={caseData.caseDescription}
                  onChange={handleChange}
                  placeholder={`أدخل وصف تفصيلي ${caseData.caseStatus === 'دعوى قضائية' ? 'للدعوى القضائية' : caseData.caseStatus === 'محضر' ? 'للمحضر' : 'للقضية'}`}
                  rows="6"
                />
              </div>
            </div>
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <div className="case-registration-page">
      <TopBar currentUser={currentUser} casesList={casesList} />
      
      <div className="main-container">
        <div className="registration-card">
          {/* Header */}
          <div className="card-header">
            <div className="header-content">
              <h1>{isTransferCase ? 'تحويل درجة قضائية' : 'تسجيل قضية جديدة'}</h1>
              {isTransferCase && (
                <div className="transfer-badge">
                  <span>تحويل من: {caseData.originalCaseDegree} - رقم: {caseData.originalCaseNumber}</span>
                </div>
              )}
            </div>
          </div>

          {/* Progress Steps */}
          <div className="progress-container">
            <div className="progress-steps">
              {[1, 2, 3, 4].map(step => (
                <div key={step} className={`progress-step ${currentStep >= step ? 'active' : ''} ${currentStep === step ? 'current' : ''}`}>
                  <div className="step-number">{step}</div>
                  <div className="step-label">{getStepTitle(step)}</div>
                </div>
              ))}
            </div>
            <div className="progress-bar">
              <div className="progress-fill" style={{ width: `${(currentStep / 4) * 100}%` }}></div>
            </div>
          </div>

          {/* Messages */}
          {error && (
            <div className="message error-message">
              <div className="message-icon">⚠️</div>
              <div className="message-text">{error}</div>
            </div>
          )}
          
          {success && (
            <div className="message success-message">
              <div className="message-icon">✅</div>
              <div className="message-text">{success}</div>
            </div>
          )}
          
          {loading && (
            <div className="message loading-message">
              <div className="loading-spinner"></div>
              <div className="message-text">جاري حفظ القضية...</div>
            </div>
          )}

          {/* Form Content */}
          <form className="registration-form" onSubmit={handleSubmit}>
            <div className="form-body">
              {renderStepContent()}
            </div>

            {/* Navigation Buttons */}
            <div className="form-navigation">
              <div className="nav-buttons">
                {currentStep > 1 && (
                  <button type="button" className="nav-btn prev-btn" onClick={prevStep}>
                    <span>← السابق</span>
                  </button>
                )}
                
                {currentStep < 4 ? (
                  <button 
                    type="button" 
                    className={`nav-btn next-btn ${!isFormValid ? 'disabled' : ''}`}
                    onClick={nextStep}
                    disabled={!isFormValid}
                  >
                    <span>التالي →</span>
                  </button>
                ) : (
                  <div className="final-buttons">
                    <button 
                      type="submit" 
                      className={`submit-btn ${!isFormValid || loading ? 'disabled' : ''}`}
                      disabled={!isFormValid || loading}
                    >
                      <SaveIcon />
                      <span>{loading ? 'جاري الحفظ...' : 'حفظ القضية'}</span>
                    </button>
                    <button
                      type="button"
                      onClick={handleCancel}
                      className="cancel-btn"
                      disabled={loading}
                    >
                      <CancelIcon />
                      <span>إلغاء</span>
                    </button>
                  </div>
                )}
              </div>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};

export default CaseRegistration;