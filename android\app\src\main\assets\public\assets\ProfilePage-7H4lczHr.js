import{u as P,r as o,j as e,f as N,d as g,l as G,i as B}from"./index-Bd3HN_hN.js";import{F as I,B as J,C as L,D as V,E as q,G as S,H as O,v as z,I as K,J as Q,c as X,d as Y,k as Z}from"./index-DQWGoQ3q.js";import{T as b}from"./TopBar-DgdxUhb0.js";import"./iconBase-BmtohqY9.js";const $="_pageWrapper_1yehs_1",U="_mainContainer_1yehs_23",ee="_mainContent_1yehs_39",ne="_pageHeader_1yehs_51",se="_pageTitle_1yehs_65",ae="_buttonIcon_1yehs_91",te="_loadingContainer_1yehs_101",ie="_errorContainer_1yehs_103",le="_spinner_1yehs_119",ce="_profileContainer_1yehs_161",oe="_errorMessage_1yehs_185",re="_profileDetails_1yehs_205",de="_sectionSubtitle_1yehs_223",me="_sectionIcon_1yehs_241",he="_sectionTitle_1yehs_251",pe="_profileField_1yehs_271",ue="_fieldIcon_1yehs_321",je="_accountManagementSection_1yehs_391",xe="_accountTypeSection_1yehs_403",fe="_accountActions_1yehs_417",_e="_switchAccountButton_1yehs_431",ye="_manageMembersButton_1yehs_475",Ne="_viewOfflineButton_1yehs_477",ge="_buttonRow_1yehs_541",be="_editButton_1yehs_557",ve="_saveButton_1yehs_559",Ce="_cancelButton_1yehs_561",n={pageWrapper:$,mainContainer:U,mainContent:ee,pageHeader:ne,pageTitle:se,buttonIcon:ae,loadingContainer:te,errorContainer:ie,spinner:le,profileContainer:ce,errorMessage:oe,profileDetails:re,sectionSubtitle:de,sectionIcon:me,sectionTitle:he,profileField:pe,fieldIcon:ue,accountManagementSection:je,accountTypeSection:xe,accountActions:fe,switchAccountButton:_e,manageMembersButton:ye,viewOfflineButton:Ne,buttonRow:ge,editButton:be,saveButton:ve,cancelButton:Ce},Se=({currentUser:a})=>{P();const[s,v]=o.useState(null),[w,x]=o.useState(!0),[p,i]=o.useState(null),[d,f]=o.useState(!1),[t,_]=o.useState({name:"",email:"",phone:"",company:"",jobTitle:""}),[m,y]=o.useState("Online"),[C,u]=o.useState("اسم الشركة/المجموعة الافتراضي"),[A,j]=o.useState(!0),[M,F]=o.useState("manager");o.useEffect(()=>{(async()=>{if(!a||!a.uid){i("المستخدم غير مسجل الدخول. يرجى تسجيل الدخول مرة أخرى."),x(!1);return}x(!0),i(null);try{const r=N(g,"users",a.uid),T=await G(r);if(T.exists()){const c=T.data();v(c),_({name:c.name||"",email:c.email||"",phone:c.phone||"",company:c.company||"",jobTitle:c.jobTitle||""}),c.accountType==="Offline"?(y("Offline"),u(null),j(!1)):(y("Online"),u(c.onlineAccountCompanyName||c.company||"اسم الشركة/المجموعة الافتراضي"),j(c.isOnlineAccountOwner||!1),F(c.userRole||"manager"))}else i("لم يتم العثور على بيانات المستخدم.")}catch(r){i("خطأ في جلب بيانات المستخدم: "+r.message)}finally{x(!1)}})()},[a]);const R=()=>f(!0),E=()=>{f(!1),_({name:s.name||"",email:s.email||"",phone:s.phone||"",company:s.company||"",jobTitle:s.jobTitle||""}),i(null)},k=async()=>{if(!(a!=null&&a.uid)){i("المستخدم غير مسجل الدخول.");return}if(!t.name||!t.email){i("الاسم والبريد الإلكتروني حقلان مطلوبان.");return}try{const l=N(g,"users",a.uid);await B(l,{name:t.name,phone:t.phone,company:t.company,jobTitle:t.jobTitle}),v({...s,...t}),f(!1),i(null)}catch(l){i("خطأ في تحديث البيانات: "+l.message)}},h=l=>{_({...t,[l.target.name]:l.target.value})},W=async()=>{if(!(a!=null&&a.uid)){i("المستخدم غير مسجل الدخول.");return}const l=m==="Online"?"Offline":"Online";try{const r=N(g,"users",a.uid);await B(r,{accountType:l}),y(l),l==="Offline"?(u(null),j(!1)):(u(s.company||"اسم الشركة/المجموعة الافتراضي"),j(s.isOnlineAccountOwner||!1),F(s.userRole||"manager"))}catch(r){i("خطأ في تبديل نوع الحساب: "+r.message)}},H=()=>{alert("وظيفة إدارة أعضاء الشركة/المجموعة (تحتاج لتطوير الواجهة والمنطق الخلفي)")},D=()=>{alert("وظيفة عرض/مزامنة البيانات غير المتصلة (تحتاج لتطوير التخزين المحلي والمزامنة)")};return w?e.jsxs("div",{className:n.pageWrapper,children:[e.jsx(b,{currentUser:a}),e.jsx("div",{className:n.mainContainer,children:e.jsx("div",{className:n.mainContent,children:e.jsxs("div",{className:n.loadingContainer,children:[e.jsx("div",{className:n.spinner}),e.jsx("p",{children:"جاري تحميل بيانات المستخدم..."})]})})})]}):p&&!s?e.jsxs("div",{className:n.pageWrapper,children:[e.jsx(b,{currentUser:a}),e.jsx("div",{className:n.mainContainer,children:e.jsx("div",{className:n.mainContent,children:e.jsx("div",{className:n.errorContainer,children:p||"لم يتم العثور على بيانات المستخدم."})})})]}):e.jsxs("div",{className:n.pageWrapper,children:[e.jsx(b,{currentUser:a}),e.jsx("div",{className:n.mainContainer,children:e.jsxs("div",{className:n.mainContent,children:[e.jsx("div",{className:n.pageHeader,children:e.jsx("h2",{className:n.pageTitle,children:"الملف الشخصي"})}),e.jsxs("div",{className:n.profileContainer,children:[p&&e.jsx("div",{className:n.errorMessage,children:p}),e.jsxs("div",{className:n.profileDetails,children:[e.jsxs("h3",{className:n.sectionSubtitle,children:[e.jsx(I,{className:n.sectionIcon}),"معلومات شخصية"]}),e.jsxs("div",{className:n.profileField,children:[e.jsxs("label",{children:[e.jsx(I,{className:n.fieldIcon}),"الاسم:"]}),d?e.jsx("input",{type:"text",name:"name",value:t.name,onChange:h,className:n.inputField}):e.jsx("span",{children:(s==null?void 0:s.name)||"غير محدد"})]}),e.jsxs("div",{className:n.profileField,children:[e.jsxs("label",{children:[e.jsx(J,{className:n.fieldIcon}),"البريد الإلكتروني:"]}),d?e.jsx("input",{type:"email",name:"email",value:t.email,onChange:h,className:n.inputField,disabled:!0}):e.jsx("span",{children:(s==null?void 0:s.email)||"غير محدد"})]}),e.jsxs("div",{className:n.profileField,children:[e.jsxs("label",{children:[e.jsx(L,{className:n.fieldIcon}),"رقم الهاتف:"]}),d?e.jsx("input",{type:"tel",name:"phone",value:t.phone,onChange:h,className:n.inputField}):e.jsx("span",{children:(s==null?void 0:s.phone)||"غير محدد"})]}),e.jsxs("div",{className:n.profileField,children:[e.jsxs("label",{children:[e.jsx(V,{className:n.fieldIcon}),"الشركة/المؤسسة:"]}),d?e.jsx("input",{type:"text",name:"company",value:t.company,onChange:h,className:n.inputField}):e.jsx("span",{children:(s==null?void 0:s.company)||"غير محدد"})]}),e.jsxs("div",{className:n.profileField,children:[e.jsxs("label",{children:[e.jsx(q,{className:n.fieldIcon}),"المسمى الوظيفي:"]}),d?e.jsx("input",{type:"text",name:"jobTitle",value:t.jobTitle,onChange:h,className:n.inputField}):e.jsx("span",{children:(s==null?void 0:s.jobTitle)||"غير محدد"})]})]}),e.jsxs("div",{className:n.accountManagementSection,children:[e.jsxs("h3",{className:n.sectionTitle,children:[e.jsx(S,{className:n.sectionIcon}),"إدارة الحساب"]}),e.jsxs("div",{className:n.accountTypeSection,children:[e.jsxs("div",{className:n.profileField,children:[e.jsx("label",{children:"نوع الحساب الحالي:"}),e.jsx("span",{children:m==="Online"?e.jsxs(e.Fragment,{children:[e.jsx(S,{className:n.fieldIcon})," حساب متصل (أونلاين)"]}):e.jsxs(e.Fragment,{children:[e.jsx(O,{className:n.fieldIcon})," حساب غير متصل (أوفلاين)"]})})]}),m==="Online"&&e.jsxs(e.Fragment,{children:[C&&e.jsxs("div",{className:n.profileField,children:[e.jsx("label",{children:"الشركة/المجموعة:"}),e.jsx("span",{children:C})]}),e.jsxs("div",{className:n.profileField,children:[e.jsxs("label",{children:[e.jsx(z,{className:n.fieldIcon}),"تصنيف المستخدم:"]}),e.jsx("span",{children:M==="manager"?"مدير":"موظف"})]})]})]}),e.jsxs("div",{className:n.accountActions,children:[e.jsxs("button",{onClick:W,className:n.switchAccountButton,children:[e.jsx(K,{className:n.buttonIcon}),e.jsxs("span",{children:["تبديل إلى ",m==="Online"?"حساب غير متصل (أوفلاين)":"حساب متصل (أونلاين)"]})]}),m==="Online"&&A&&e.jsxs("button",{onClick:H,className:n.manageMembersButton,children:[e.jsx(Q,{className:n.buttonIcon}),e.jsx("span",{children:"إدارة أعضاء الشركة/المجموعة"})]}),m==="Offline"&&e.jsxs("button",{onClick:D,className:n.viewOfflineButton,children:[e.jsx(O,{className:n.buttonIcon}),e.jsx("span",{children:"عرض / مزامنة البيانات غير المتصلة"})]})]})]}),e.jsx("div",{className:n.buttonRow,children:d?e.jsxs(e.Fragment,{children:[e.jsxs("button",{onClick:k,className:n.saveButton,children:[e.jsx(X,{className:n.buttonIcon}),e.jsx("span",{children:"حفظ التغييرات"})]}),e.jsxs("button",{onClick:E,className:n.cancelButton,children:[e.jsx(Y,{className:n.buttonIcon}),e.jsx("span",{children:"إلغاء التعديل"})]})]}):e.jsxs("button",{onClick:R,className:n.editButton,children:[e.jsx(Z,{className:n.buttonIcon}),e.jsx("span",{children:"تعديل الملف الشخصي"})]})})]})]})})]})};export{Se as default};
