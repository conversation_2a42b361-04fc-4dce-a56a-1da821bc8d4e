// خدمة إدارة المجموعات والأعضاء في Firebase
import { db } from '../config/firebaseConfig';
import {
  collection,
  query,
  where,
  getDocs,
  doc,
  getDoc,
  addDoc,
  updateDoc,
  deleteDoc,
  setDoc,
  serverTimestamp,
  arrayUnion,
  arrayRemove
} from 'firebase/firestore';

// اسماء المجموعات في Firestore
const GROUPS_COLLECTION = 'groups';
const MEMBERS_COLLECTION = 'members';
const SHARED_CASES_COLLECTION = 'sharedCases';

/**
 * الحصول على جميع المجموعات للمستخدم
 * @param {string} userId - معرف المستخدم
 * @returns {Promise<Array>} - مصفوفة من المجموعات
 */
export const getGroups = async (userId) => {
  try {
    if (!userId || typeof userId !== 'string' || userId.trim() === '') {
      throw new Error('معرف المستخدم غير صالح');
    }
    const userRef = doc(db, 'users', userId);
    const userDoc = await getDoc(userRef);
    if (!userDoc.exists()) {
      throw new Error('لم يتم العثور على بيانات المستخدم');
    }

    const userEmail = userDoc.data().email;

    const groupsRef = collection(db, GROUPS_COLLECTION);
    const membersRef = collection(db, MEMBERS_COLLECTION);

    // جلب المجموعات التي أنشأها المستخدم
    const createdGroupsQuery = query(groupsRef, where('createdBy', '==', userId));
    const createdGroupsSnapshot = await getDocs(createdGroupsQuery);

    // جلب عضويات المستخدم سواء بالإيميل أو userId
    const [memberEmailSnapshot, memberIdSnapshot] = await Promise.all([
      getDocs(query(membersRef, where('email', '==', userEmail))),
      getDocs(query(membersRef, where('userId', '==', userId)))
    ]);

    const memberDocs = [...memberEmailSnapshot.docs, ...memberIdSnapshot.docs];
    const uniqueGroupIds = [...new Set(memberDocs.map(doc => doc.data().groupId))];

    // جلب تفاصيل المجموعات التي هو عضو فيها
    const memberGroups = await Promise.all(uniqueGroupIds.map(async (groupId) => {
      const groupDoc = await getDoc(doc(db, GROUPS_COLLECTION, groupId));
      if (groupDoc.exists()) {
        const memberDoc = memberDocs.find(d => d.data().groupId === groupId);
        return {
          id: groupDoc.id,
          ...groupDoc.data(),
          memberRole: memberDoc.data().role,
          membershipId: memberDoc.id,
          isMember: true
        };
      }
      return null;
    }));

    const createdGroups = createdGroupsSnapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data(),
      memberRole: 'admin',
      isCreator: true,
      isMember: true,
      created: doc.data().createdAt?.toDate()?.toISOString() || new Date().toISOString()
    }));

    // دمج المجموعات وإزالة التكرار
    const allGroups = [...createdGroups];
    memberGroups.filter(Boolean).forEach(group => {
      if (!allGroups.some(g => g.id === group.id)) {
        allGroups.push(group);
      }
    });

    return allGroups;

  } catch (error) {
    console.error('خطأ في جلب المجموعات:', error);
    throw error;
  }
};

/**
 * إنشاء مجموعة جديدة
 * @param {string} userId - معرف المستخدم
 * @param {Object} groupData - بيانات المجموعة
 * @returns {Promise<Object>} - المجموعة المنشأة
 */
export const createGroup = async (userId, groupData) => {
  try {
    const groupsRef = collection(db, GROUPS_COLLECTION);
    const nameQuery = query(groupsRef, where('name', '==', groupData.name.trim()));
    const existingGroups = await getDocs(nameQuery);

    if (!existingGroups.empty) {
      throw new Error('يوجد مجموعة بهذا الاسم بالفعل');
    }

    const newGroupData = {
      name: groupData.name.trim(),
      description: groupData.description?.trim() || '',
      createdBy: userId,
      creatorEmail: groupData.creatorEmail,
      creatorName: groupData.creatorName,
      createdAt: serverTimestamp(),
      updatedAt: serverTimestamp(),
      membersCount: 1,
      managers: { [userId]: true },
      editors: { [userId]: true }
    };

    const groupRef = await addDoc(groupsRef, newGroupData);

    const membersRef = collection(db, MEMBERS_COLLECTION);
    await addDoc(membersRef, {
      userId: userId,
      groupId: groupRef.id,
      email: groupData.creatorEmail,
      name: groupData.creatorName || groupData.creatorEmail.split('@')[0],
      role: 'admin',
      joinedAt: serverTimestamp(),
      addedBy: userId
    });

    return {
      id: groupRef.id,
      ...newGroupData,
      created: new Date().toISOString().split('T')[0]
    };

  } catch (error) {
    console.error('خطأ في إنشاء المجموعة:', error);
    throw error;
  }
};

/**
 * حذف مجموعة
 * @param {string} groupId - معرف المجموعة
 * @returns {Promise<void>}
 */
export const deleteGroup = async (groupId) => {
  try {
    await deleteDoc(doc(db, GROUPS_COLLECTION, groupId));

    const membersRef = collection(db, MEMBERS_COLLECTION);
    const q = query(membersRef, where('groupId', '==', groupId));
    const querySnapshot = await getDocs(q);

    const deletePromises = querySnapshot.docs.map(doc => deleteDoc(doc.ref));
    await Promise.all(deletePromises);
  } catch (error) {
    console.error('خطأ في حذف المجموعة:', error);
    throw error;
  }
};

/**
 * الحصول على أعضاء المجموعة
 * @param {string} groupId - معرف المجموعة (اختياري)
 * @returns {Promise<Array>} - مصفوفة من الأعضاء
 */
export const getMembers = async (groupId = null) => {
  try {
    const membersRef = collection(db, MEMBERS_COLLECTION);
    let q;

    if (groupId) {
      q = query(membersRef, where('groupId', '==', groupId));
    } else {
      q = query(membersRef);
    }

    const querySnapshot = await getDocs(q);

    return querySnapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data(),
      joinedAt: doc.data().joinedAt?.toDate?.()?.toISOString() || new Date().toISOString()
    }));
  } catch (error) {
    console.error('خطأ في جلب الأعضاء:', error);
    return [];
  }
};

/**
 * إضافة عضو جديد للمجموعة
 * @param {Object} memberData - بيانات العضو
 * @returns {Promise<Object>} - العضو المضاف
 */
export const addMember = async (memberData) => {
  try {
    const groupRef = doc(db, GROUPS_COLLECTION, memberData.groupId);
    const groupDoc = await getDoc(groupRef);

    if (!groupDoc.exists()) {
      throw new Error('المجموعة غير موجودة');
    }

    const usersRef = collection(db, 'users');
    const userQuery = query(usersRef, where('email', '==', memberData.email.trim()));
    const userSnapshot = await getDocs(userQuery);

    if (userSnapshot.empty) {
      throw new Error('لم يتم العثور على المستخدم');
    }

    const userData = userSnapshot.docs[0];
    const targetUserId = userData.id;

    const membersRef = collection(db, MEMBERS_COLLECTION);
    const existingMemberQuery = query(
      membersRef,
      where('email', '==', memberData.email.trim()),
      where('groupId', '==', memberData.groupId)
    );
    const existingMemberSnapshot = await getDocs(existingMemberQuery);

    if (!existingMemberSnapshot.empty) {
      throw new Error('المستخدم عضو في المجموعة بالفعل');
    }

    const newMemberData = {
      userId: targetUserId,
      groupId: memberData.groupId,
      email: memberData.email.trim(),
      name: userData.data().displayName || memberData.email.split('@')[0],
      role: memberData.role,
      joinedAt: serverTimestamp(),
      addedBy: groupDoc.data().createdBy,
      isActive: true
    };

    const memberDocRef = await addDoc(membersRef, newMemberData);

    // تحديث صلاحيات المجموعة تلقائياً
    let groupUpdate = {
      membersCount: (groupDoc.data().membersCount || 0) + 1,
      updatedAt: serverTimestamp()
    };
    if (memberData.role === 'admin') {
      groupUpdate.managers = { ...(groupDoc.data().managers || {}), [targetUserId]: true };
    }
    if (memberData.role === 'editor') {
      groupUpdate.editors = { ...(groupDoc.data().editors || {}), [targetUserId]: true };
    }
    await updateDoc(groupRef, groupUpdate);

    return {
      id: memberDocRef.id,
      ...newMemberData,
      joinedAt: new Date().toISOString()
    };

  } catch (error) {
    console.error('خطأ في إضافة العضو:', error);
    throw error;
  }
};

/**
 * حذف عضو من مجموعة
 * @param {string} memberId - معرف العضو
 * @param {string} groupId - معرف المجموعة
 * @returns {Promise<void>}
 */
export const deleteMember = async (memberId, groupId) => {
  try {
    await deleteDoc(doc(db, MEMBERS_COLLECTION, memberId));

    const groupRef = doc(db, GROUPS_COLLECTION, groupId);
    const groupDoc = await getDoc(groupRef);

    if (groupDoc.exists()) {
      // ✅ إصلاح: تحديث الحقل الصحيح `membersCount`
      await updateDoc(groupRef, {
        membersCount: Math.max(0, (groupDoc.data().membersCount || 1) - 1),
        updatedAt: serverTimestamp()
      });
    }
  } catch (error) {
    console.error('خطأ في حذف العضو:', error);
    throw error;
  }
};



/**
 * التحقق من صلاحيات المستخدم في المجموعة
 * @param {string} userId - معرف المستخدم
 * @param {string} groupId - معرف المجموعة
 * @returns {Promise<Object>} - معلومات العضوية والصلاحيات
 */
export const checkUserGroupPermissions = async (userId, groupId) => {
  try {
    const membersRef = collection(db, MEMBERS_COLLECTION);
    const memberQuery = query(
      membersRef,
      where('userId', '==', userId),
      where('groupId', '==', groupId)
    );
    const memberSnapshot = await getDocs(memberQuery);

    const groupRef = doc(db, GROUPS_COLLECTION, groupId);
    const groupDoc = await getDoc(groupRef);

    if (!groupDoc.exists()) {
      throw new Error('المجموعة غير موجودة');
    }

    const isAdmin = groupDoc.data().createdBy === userId;
    if (isAdmin) {
      return {
        isMember: true,
        role: 'admin',
        permissions: {
          canManageMembers: true,
          canShareCases: true,
          canViewCases: true,
          canEditCases: true
        }
      };
    }

    if (memberSnapshot.empty) {
      return {
        isMember: false,
        role: null,
        permissions: {
          canManageMembers: false,
          canShareCases: false,
          canViewCases: false,
          canEditCases: false
        }
      };
    }

    const memberData = memberSnapshot.docs[0].data();
    const role = memberData.role;

    return {
      isMember: true,
      role: role,
      permissions: {
        canManageMembers: role === 'admin' || role === 'editor',
        canShareCases: role === 'admin' || role === 'editor',
        canViewCases: true,
        canEditCases: role === 'admin' || role === 'editor'
      }
    };

  } catch (error) {
    console.error('خطأ في التحقق من الصلاحيات:', error);
    throw error;
  }
};

/**
 * الحصول على جميع القضايا المشتركة مع المستخدم
 */
export const getSharedCasesForUser = async (userId) => {
  try {
    const userRef = doc(db, 'users', userId);
    const userDoc = await getDoc(userRef);
    if (!userDoc.exists()) {
      throw new Error('لم يتم العثور على بيانات المستخدم');
    }

    const groups = await getGroups(userId);
    const groupIds = groups.map(group => group.id);

    if (groupIds.length === 0) {
      return [];
    }

    const sharedCasesRef = collection(db, SHARED_CASES_COLLECTION);
    const sharedCasesQuery = query(sharedCasesRef, where('groupId', 'in', groupIds));
    const sharedCasesSnapshot = await getDocs(sharedCasesQuery);

    const sharedCases = [];
    for (const sharedDoc of sharedCasesSnapshot.docs) {
      const sharedData = sharedDoc.data();
      const caseRef = doc(db, 'cases', sharedData.caseId);
      const caseDoc = await getDoc(caseRef);

      if (caseDoc.exists()) {
        const caseData = caseDoc.data();
        const group = groups.find(g => g.id === sharedData.groupId);

        sharedCases.push({
          id: caseDoc.id,
          ...caseData,
          sharedInfo: {
            groupId: sharedData.groupId,
            groupName: group?.name || 'مجموعة غير معروفة',
            sharedBy: sharedData.sharedBy === userId ? 'أنت' : 'مدير المجموعة',
            sharedAt: sharedData.sharedAt?.toDate() || new Date(),
            userRole: group?.memberRole || 'member'
          }
        });
      }
    }

    return sharedCases;

  } catch (error) {
    console.error('خطأ في جلب القضايا المشتركة:', error);
    return [];
  }
};

// اسم مجموعة التكليفات في Firestore
const TASK_ASSIGNMENTS_COLLECTION = 'taskAssignments';

/**
 * حفظ تكليف مهمة
 * @param {Object} assignment - بيانات التكليف
 * @returns {Promise<void>}
 */
export const saveTaskAssignment = async (assignment) => {
  try {
    // التحقق من صحة البيانات
    if (!assignment.taskId || !assignment.assignedTo || !assignment.caseId) {
      throw new Error('بيانات التكليف غير مكتملة');
    }

    console.log('حفظ التكليف:', assignment);

    // استخدام addDoc بدلاً من setDoc لتجنب مشاكل الصلاحيات
    const assignmentsRef = collection(db, TASK_ASSIGNMENTS_COLLECTION);
    const docRef = await addDoc(assignmentsRef, {
      ...assignment,
      createdAt: serverTimestamp(),
      updatedAt: serverTimestamp()
    });

    console.log('تم حفظ التكليف بنجاح:', docRef.id);
  } catch (error) {
    console.error('خطأ في حفظ التكليف:', error);
    throw error;
  }
};

/**
 * إلغاء تكليف مهمة
 * @param {string} taskId - معرف المهمة
 * @returns {Promise<void>}
 */
export const removeTaskAssignment = async (taskId) => {
  try {
    // البحث عن التكليف بناءً على taskId
    const assignmentsRef = collection(db, TASK_ASSIGNMENTS_COLLECTION);
    const q = query(assignmentsRef, where('taskId', '==', taskId));
    const querySnapshot = await getDocs(q);
    
    // حذف جميع التكليفات المرتبطة بهذه المهمة
    const deletePromises = querySnapshot.docs.map(doc => deleteDoc(doc.ref));
    await Promise.all(deletePromises);
    
    console.log('تم إلغاء التكليف بنجاح:', taskId);
  } catch (error) {
    console.error('خطأ في إلغاء التكليف:', error);
    throw error;
  }
};

/**
 * جلب تكليف مهمة محددة
 * @param {string} taskId - معرف المهمة
 * @returns {Promise<Object|null>} - بيانات التكليف أو null
 */
export const getTaskAssignment = async (taskId) => {
  try {
    const assignmentsRef = collection(db, TASK_ASSIGNMENTS_COLLECTION);
    const q = query(assignmentsRef, where('taskId', '==', taskId));
    const querySnapshot = await getDocs(q);
    
    if (!querySnapshot.empty) {
      const doc = querySnapshot.docs[0];
      return {
        id: doc.id,
        ...doc.data()
      };
    }
    
    return null;
  } catch (error) {
    console.error('خطأ في جلب التكليف:', error);
    return null;
  }
};

/**
 * جلب جميع التكليفات لمستخدم معين
 * @param {string} userId - معرف المستخدم
 * @returns {Promise<Array>} - مصفوفة من التكليفات
 */
export const getUserTaskAssignments = async (userId) => {
  try {
    const assignmentsRef = collection(db, TASK_ASSIGNMENTS_COLLECTION);
    const userAssignmentsQuery = query(
      assignmentsRef, 
      where('assignedTo.id', '==', userId)
    );
    const assignmentsSnapshot = await getDocs(userAssignmentsQuery);
    
    return assignmentsSnapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data()
    }));
  } catch (error) {
    console.error('خطأ في جلب تكليفات المستخدم:', error);
    return [];
  }
};

/**
 * جلب جميع التكليفات للمجموعات التي ينتمي إليها المستخدم
 * @param {string} userId - معرف المستخدم
 * @returns {Promise<Array>} - مصفوفة من التكليفات
 */
export const getGroupTaskAssignments = async (userId) => {
  try {
    const groups = await getGroups(userId);
    const groupIds = groups.map(group => group.id);

    if (groupIds.length === 0) {
      return [];
    }

    const assignmentsRef = collection(db, TASK_ASSIGNMENTS_COLLECTION);
    const groupAssignmentsQuery = query(
      assignmentsRef,
      where('assignedTo.groupId', 'in', groupIds)
    );
    const assignmentsSnapshot = await getDocs(groupAssignmentsQuery);

    return assignmentsSnapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data()
    }));
  } catch (error) {
    console.error('خطأ في جلب تكليفات المجموعة:', error);
    return [];
  }
};

/**
 * جلب جميع التكليفات لمستخدم معين مع تشخيص مفصل
 * @param {string} userId - معرف المستخدم
 * @returns {Promise<Array>} - مصفوفة من التكليفات
 */
export const getUserTaskAssignmentsDebug = async (userId) => {
  try {
    console.log('البحث عن تكليفات للمستخدم:', userId);
    
    // أولاً، جلب جميع التكليفات للتشخيص
    const assignmentsRef = collection(db, TASK_ASSIGNMENTS_COLLECTION);
    const allAssignmentsSnapshot = await getDocs(assignmentsRef);
    
    console.log('إجمالي التكليفات في قاعدة البيانات:', allAssignmentsSnapshot.docs.length);
    
    allAssignmentsSnapshot.docs.forEach(doc => {
      const data = doc.data();
      console.log('تكليف موجود:', {
        id: doc.id,
        assignedToId: data.assignedTo?.id,
        assignedToName: data.assignedTo?.name,
        taskType: data.taskType
      });
    });
    
    // ثم البحث عن التكليفات الخاصة بالمستخدم
    const userAssignmentsQuery = query(
      assignmentsRef, 
      where('assignedTo.id', '==', userId)
    );
    const userAssignmentsSnapshot = await getDocs(userAssignmentsQuery);

    console.log('عدد التكليفات للمستخدم:', userAssignmentsSnapshot.docs.length);
    
    const assignments = userAssignmentsSnapshot.docs.map(doc => {
      const data = doc.data();
      console.log('تكليف للمستخدم:', data);
      return {
        id: doc.id,
        ...data
      };
    });

    return assignments;
  } catch (error) {
    console.error('خطأ في جلب تكليفات المستخدم:', error);
    return [];
  }
};

/**
 * إصلاح التكليفات الموجودة - تحويل معرف العضوية إلى معرف المستخدم
 * @param {string} userId - معرف المستخدم الحالي
 * @returns {Promise<void>}
 */
export const fixExistingAssignments = async (userId) => {
  try {
    console.log('إصلاح التكليفات الموجودة للمستخدم:', userId);
    
    // جلب معلومات المستخدم
    const userRef = doc(db, 'users', userId);
    const userDoc = await getDoc(userRef);
    if (!userDoc.exists()) {
      console.log('المستخدم غير موجود');
      return;
    }
    
    const userEmail = userDoc.data().email;
    console.log('إيميل المستخدم:', userEmail);
    
    // البحث عن التكليفات التي تحتوي على إيميل المستخدم
    const assignmentsRef = collection(db, TASK_ASSIGNMENTS_COLLECTION);
    const emailAssignmentsQuery = query(
      assignmentsRef,
      where('assignedTo.email', '==', userEmail)
    );
    const emailAssignmentsSnapshot = await getDocs(emailAssignmentsQuery);
    
    console.log('عدد التكليفات الموجودة بالإيميل:', emailAssignmentsSnapshot.docs.length);
    
    // تحديث التكليفات
    const updatePromises = emailAssignmentsSnapshot.docs.map(async (docSnapshot) => {
      const data = docSnapshot.data();
      
      // إذا كان assignedTo.id مختلف عن userId، قم بالتحديث
      if (data.assignedTo?.id !== userId) {
        console.log('تحديث التكليف:', docSnapshot.id);
        
        const updatedAssignedTo = {
          ...data.assignedTo,
          id: userId, // تحديث المعرف إلى معرف المستخدم الصحيح
          userId: userId // إضافة userId أيضاً
        };
        
        await updateDoc(docSnapshot.ref, {
          assignedTo: updatedAssignedTo,
          updatedAt: serverTimestamp()
        });
        
        console.log('تم تحديث التكليف بنجاح');
      }
    });
    
    await Promise.all(updatePromises);
    console.log('تم إصلاح جميع التكليفات');
    
  } catch (error) {
    console.error('خطأ في إصلاح التكليفات:', error);
  }
};