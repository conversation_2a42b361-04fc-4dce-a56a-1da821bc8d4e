// Custom Hook for Case Registration Form Management
import { useState, useEffect, useCallback } from 'react';
import { validateCaseForm, getFormProgress } from '../utils/formValidation';

export const useCaseForm = (initialData = {}) => {
  const currentYear = new Date().getFullYear().toString();

  const [caseData, setCaseData] = useState({
    caseNumber: '',
    caseYear: currentYear,
    clientName: '',
    caseDescription: '',
    caseCategory: '',
    opponentName: '',
    caseDegree: '',
    circleNumber: '',
    courtLocation: '',
    caseStatus: 'قيد النظر',
    reportNumber: '',
    reportLocation: '',
    caseDate: '',
    originalCaseId: '',
    originalCaseDegree: '',
    originalCaseNumber: '',
    ...initialData
  });

  const [currentStep, setCurrentStep] = useState(1);
  const [validationErrors, setValidationErrors] = useState({});
  const [isStepValid, setIsStepValid] = useState(false);
  const [formProgress, setFormProgress] = useState(0);
  const [touchedFields, setTouchedFields] = useState({});

  // Update form progress whenever step or data changes
  useEffect(() => {
    const progress = getFormProgress(currentStep, caseData);
    setFormProgress(progress);
  }, [currentStep, caseData]);

  // Validate current step whenever data changes
  useEffect(() => {
    const validation = validateCaseForm(caseData, currentStep);
    setValidationErrors(validation.errors);
    setIsStepValid(validation.isValid);
  }, [caseData, currentStep]);

  // Handle field changes with validation
  const handleFieldChange = useCallback((name, value) => {
    setCaseData(prev => ({
      ...prev,
      [name]: value
    }));
    
    // Mark field as touched
    setTouchedFields(prev => ({
      ...prev,
      [name]: true
    }));
  }, []);

  // Handle field blur events
  const handleFieldBlur = useCallback((name) => {
    setTouchedFields(prev => ({
      ...prev,
      [name]: true
    }));
  }, []);

  // Navigate to next step
  const nextStep = useCallback(() => {
    if (isStepValid && currentStep < 4) {
      setCurrentStep(prev => prev + 1);
    }
  }, [isStepValid, currentStep]);

  // Navigate to previous step
  const prevStep = useCallback(() => {
    if (currentStep > 1) {
      setCurrentStep(prev => prev - 1);
    }
  }, [currentStep]);

  // Go to specific step
  const goToStep = useCallback((step) => {
    if (step >= 1 && step <= 4) {
      setCurrentStep(step);
    }
  }, []);

  // Reset form to initial state
  const resetForm = useCallback(() => {
    setCaseData({
      caseNumber: '',
      caseYear: currentYear,
      clientName: '',
      caseDescription: '',
      caseCategory: '',
      opponentName: '',
      caseDegree: '',
      circleNumber: '',
      courtLocation: '',
      caseStatus: 'قيد النظر',
      reportNumber: '',
      reportLocation: '',
      caseDate: '',
      originalCaseId: '',
      originalCaseDegree: '',
      originalCaseNumber: '',
      ...initialData
    });
    setCurrentStep(1);
    setValidationErrors({});
    setTouchedFields({});
  }, [initialData]);

  // Check if field has error and has been touched
  const getFieldError = useCallback((fieldName) => {
    return touchedFields[fieldName] ? validationErrors[fieldName] : null;
  }, [touchedFields, validationErrors]);

  // Check if field is valid
  const isFieldValid = useCallback((fieldName) => {
    return !validationErrors[fieldName];
  }, [validationErrors]);

  // Get step title
  const getStepTitle = useCallback((step = currentStep) => {
    switch(step) {
      case 1: return 'نوع القضية والرقم';
      case 2: return 'أطراف القضية';
      case 3: return 'التفاصيل القانونية';
      case 4: return 'الوصف والملاحظات';
      default: return '';
    }
  }, [currentStep]);

  // Check if all steps are valid for submission
  const isFormReady = useCallback(() => {
    for (let step = 1; step <= 4; step++) {
      const validation = validateCaseForm(caseData, step);
      if (!validation.isValid) {
        return false;
      }
    }
    return true;
  }, [caseData]);

  // Get completion status for each step
  const getStepStatus = useCallback((step) => {
    const validation = validateCaseForm(caseData, step);
    if (step === currentStep) return 'current';
    if (validation.isValid) return 'completed';
    if (step < currentStep) return 'error';
    return 'pending';
  }, [caseData, currentStep]);

  // Get required fields for current step
  const getRequiredFields = useCallback((step = currentStep) => {
    const requiredFields = {
      1: ['caseStatus'],
      2: ['clientName'],
      3: [],
      4: ['caseDescription']
    };

    // Add conditional required fields based on case status
    if (step === 1) {
      if (caseData.caseStatus === 'دعوى قضائية') {
        requiredFields[1].push('caseNumber', 'caseYear');
      } else if (caseData.caseStatus === 'محضر') {
        requiredFields[1].push('reportNumber', 'caseYear');
      }
    }

    if (step === 3) {
      if (caseData.caseStatus === 'دعوى قضائية') {
        requiredFields[3].push('circleNumber', 'courtLocation', 'caseDegree', 'caseCategory', 'caseDate');
      } else if (caseData.caseStatus === 'محضر') {
        requiredFields[3].push('reportLocation', 'caseDate');
      } else if (caseData.caseStatus === 'قيد النظر') {
        requiredFields[3].push('reportLocation');
      }
    }

    return requiredFields[step] || [];
  }, [currentStep, caseData.caseStatus]);

  // Check if field is required for current step
  const isFieldRequired = useCallback((fieldName, step = currentStep) => {
    const requiredFields = getRequiredFields(step);
    return requiredFields.includes(fieldName);
  }, [getRequiredFields, currentStep]);

  // Get field validation state for UI
  const getFieldState = useCallback((fieldName) => {
    const hasError = !isFieldValid(fieldName);
    const isTouched = touchedFields[fieldName];
    const isRequired = isFieldRequired(fieldName);
    const error = getFieldError(fieldName);

    return {
      hasError: hasError && isTouched,
      isValid: isFieldValid(fieldName) && isTouched && caseData[fieldName],
      isRequired,
      error,
      className: hasError && isTouched ? 'error' : (isFieldValid(fieldName) && isTouched && caseData[fieldName] ? 'success' : '')
    };
  }, [isFieldValid, touchedFields, isFieldRequired, getFieldError, caseData]);

  return {
    // Data
    caseData,
    
    // Navigation
    currentStep,
    nextStep,
    prevStep,
    goToStep,
    
    // Validation
    validationErrors,
    isStepValid,
    isFormReady: isFormReady(),
    getFieldError,
    isFieldValid,
    getFieldState,
    isFieldRequired,
    
    // Progress
    formProgress,
    
    // Field management
    handleFieldChange,
    handleFieldBlur,
    touchedFields,
    
    // Utilities
    getStepTitle,
    getStepStatus,
    getRequiredFields,
    resetForm,
    
    // Step info
    totalSteps: 4,
    isFirstStep: currentStep === 1,
    isLastStep: currentStep === 4,
  };
};