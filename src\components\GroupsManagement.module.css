@import '../styles/variables.css';

.container {
  padding-top: 0;
  min-height: 100vh;
  background-color: var(--neutral-100);
}

.content {
  max-width: 1400px;
  margin: 0 auto;
  padding: 24px;
  color: var(--neutral-800);
}

.title {
  display: flex;
  align-items: center;
  gap: 12px;
  color: var(--neutral-900);
  margin-bottom: 32px;
  font-size: 2rem;
}

.title .icon {
  color: var(--primary-color);
}

/* تخطيط لوحة التحكم */
.dashboard {
  display: flex;
  gap: 24px;
  min-height: 70vh;
}

/* الشريط الجانبي */
.sidebar {
  width: 280px;
  flex-shrink: 0;
  display: flex;
  flex-direction: column;
  gap: 24px;
}

/* المحتوى الرئيسي */
.mainContent {
  flex: 1;
  background-color: white;
  border-radius: var(--radius-lg);
  padding: 24px;
  box-shadow: var(--shadow-md);
  border: 1px solid var(--neutral-200);
}

/* التبويبات العمودية */
.tabs {
  display: flex;
  flex-direction: column;
  background-color: white;
  border-radius: var(--radius-lg);
  overflow: hidden;
  box-shadow: var(--shadow-md);
  border: 1px solid var(--neutral-200);
}

.tabButton {
  padding: 16px;
  background: none;
  border: none;
  border-right: 3px solid transparent;
  cursor: pointer;
  font-size: 16px;
  color: var(--neutral-600);
  transition: all var(--transition-normal);
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 12px;
  text-align: right;
}

.tabButton:hover {
  background-color: var(--neutral-100);
  color: var(--primary-color);
}

.activeTab {
  color: var(--primary-color);
  background-color: var(--neutral-50);
  border-right-color: var(--primary-color);
  font-weight: 600;
}

.tabIcon {
  font-size: 20px;
}

/* بطاقات الإحصائيات */
.statsContainer {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.statCard {
  background-color: white;
  border-radius: var(--radius-lg);
  padding: 16px;
  display: flex;
  align-items: center;
  gap: 16px;
  box-shadow: var(--shadow-md);
  border: 1px solid var(--neutral-200);
  transition: all var(--transition-normal);
}

.statCard:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-lg);
}

.statIcon {
  width: 48px;
  height: 48px;
  border-radius: var(--radius-full);
  background-color: var(--primary-light);
  color: var(--primary-color);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
}

.statInfo {
  display: flex;
  flex-direction: column;
}

.statValue {
  font-size: 24px;
  font-weight: 700;
  color: var(--neutral-900);
}

.statLabel {
  font-size: 14px;
  color: var(--neutral-600);
}

/* رسالة التنبيه للوضع المحلي */
.offlineAlert {
  background-color: white;
  border-radius: var(--radius-lg);
  padding: 24px;
  box-shadow: var(--shadow-md);
  border: 1px solid var(--neutral-200);
  margin-bottom: 24px;
  display: flex;
  gap: 16px;
  align-items: flex-start;
}

.alertIcon {
  color: var(--warning);
  font-size: 24px;
  margin-top: 4px;
}

.alertContent {
  flex: 1;
}

.alertContent h3 {
  color: var(--neutral-900);
  margin-top: 0;
  margin-bottom: 12px;
  font-size: 1.25rem;
}

.alertContent p {
  color: var(--neutral-700);
  margin-bottom: 16px;
  line-height: 1.5;
}

.accountIndicator {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  background-color: var(--neutral-100);
  padding: 8px 16px;
  border-radius: var(--radius-md);
  border: 1px solid var(--neutral-200);
  color: var(--neutral-700);
  font-size: 0.9rem;
}

.localIcon {
  color: var(--success);
}

.section {
  margin-bottom: 32px;
}

.sectionHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid var(--neutral-200);
}

.sectionHeader h2 {
  color: var(--neutral-900);
  font-size: 1.5rem;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 12px;
}

.headerIcon {
  color: var(--primary-color);
  font-size: 24px;
}

/* الأقسام الفرعية */
.subsection {
  margin-bottom: 40px;
  background-color: white;
  border-radius: var(--radius-md);
  padding: 24px;
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--neutral-200);
}

.subsection:last-child {
  margin-bottom: 0;
}

.subsectionTitle {
  color: var(--neutral-800);
  font-size: 1.25rem;
  margin: 0 0 20px 0;
  padding-bottom: 12px;
  border-bottom: 1px solid var(--neutral-200);
  display: flex;
  align-items: center;
  gap: 10px;
}

.subsectionIcon {
  color: var(--primary-color);
  font-size: 20px;
}

.subsectionHeader {
  display: flex;
  justify-content: flex-end;
  margin-bottom: 20px;
}

/* مؤشر التحميل */
.loadingContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px;
  text-align: center;
}

.loadingSpinner {
  width: 50px;
  height: 50px;
  border: 5px solid var(--neutral-200);
  border-radius: 50%;
  border-top-color: var(--primary-color);
  animation: var(--animation-spin);
  margin-bottom: 16px;
}

/* استخدام أنيميشن spin من variables.css */

.addButton {
  display: flex;
  align-items: center;
  gap: 8px;
  background-color: #141414;
  color: white;
  border: none;
  padding: 12px 20px;
  border-radius: var(--radius-md);
  cursor: pointer;
  transition: all var(--transition-normal);
  font-weight: 500;
  box-shadow: var(--shadow-sm);
}

.addButton:hover {
  background-color: var(--primary-dark);
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.addButton:disabled {
  background-color: var(--neutral-400);
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.groupsList {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  gap: 24px;
}

.groupCard {
  background-color: white;
  border-radius: var(--radius-lg);
  padding: 24px;
  transition: all var(--transition-normal);
  border: 1px solid var(--neutral-200);
  box-shadow: var(--shadow-sm);
  position: relative;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.groupCard::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 4px;
  background: linear-gradient(to right, var(--primary-color), var(--secondary-color));
}

.groupCard:hover {
  box-shadow: var(--shadow-md);
  transform: translateY(-5px);
  border-color: var(--primary-light);
}

.groupHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.groupHeader h3 {
  margin: 0;
  color: var(--neutral-900);
  font-size: 1.25rem;
  font-weight: 600;
}

.groupBadge {
  background-color: var(--primary-light);
  color: var(--primary-dark);
  padding: 4px 12px;
  border-radius: var(--radius-full);
  font-size: 0.85rem;
  font-weight: 500;
}

.groupDescription {
  color: var(--neutral-600);
  font-size: 0.95rem;
  margin-bottom: 16px;
  line-height: 1.5;
  flex-grow: 1;
}

.groupMeta {
  color: var(--neutral-500);
  font-size: 0.85rem;
  margin-bottom: 20px;
  padding-bottom: 16px;
  border-bottom: 1px solid var(--neutral-200);
}

.groupActions {
  display: flex;
  gap: 12px;
  margin-top: auto;
  flex-wrap: wrap;
}

.actionButton {
  display: flex;
  align-items: center;
  gap: 6px;
  background-color: var(--neutral-100);
  border: 1px solid var(--neutral-200);
  padding: 8px 12px;
  border-radius: var(--radius-md);
  cursor: pointer;
  transition: all var(--transition-normal);
  color: var(--neutral-700);
  font-weight: 500;
  font-size: 0.9rem;
}

.actionButton:hover {
  background-color: var(--neutral-200);
  transform: translateY(-2px);
  box-shadow: var(--shadow-sm);
}

.deleteButton {
  display: flex;
  align-items: center;
  gap: 6px;
  background-color: var(--error);
  color: rgb(234, 196, 196);
  border: none;
  padding: 8px 12px;
  border-radius: var(--radius-md);
  cursor: pointer;
  transition: all var(--transition-normal);
  font-weight: 500;
  font-size: 0.9rem;
}

.deleteButton:hover {
  background-color: #dc2626;
  transform: translateY(-2px);
  box-shadow: var(--shadow-sm);
}

.membersContainer {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.filterBar {
  background-color: var(--neutral-50);
  padding: 16px;
  border-radius: var(--radius-md);
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

.filterGroup {
  display: flex;
  align-items: center;
  gap: 12px;
}

.filterGroup label {
  color: var(--neutral-700);
  font-weight: 500;
}

.filterSelect {
  padding: 8px 16px;
  border-radius: var(--radius-md);
  border: 1px solid var(--neutral-300);
  background-color: white;
  color: var(--neutral-800);
  min-width: 200px;
}

.membersTable {
  width: 100%;
  border-collapse: collapse;
  color: var(--neutral-800);
  background-color: white;
  border-radius: var(--radius-md);
  overflow: hidden;
  box-shadow: var(--shadow-sm);
}

.membersTable th, .membersTable td {
  padding: 14px 16px;
  text-align: right;
  border-bottom: 1px solid var(--neutral-200);
}

.membersTable th {
  background-color: var(--neutral-100);
  font-weight: 600;
  color: var(--neutral-800);
  font-size: 0.95rem;
}

.membersTable tr:hover {
  background-color: var(--neutral-50);
}

.membersTable tr:last-child td {
  border-bottom: none;
}

.memberActions {
  display: flex;
  gap: 12px;
}

.roleBadge {
  display: inline-block;
  padding: 4px 12px;
  border-radius: var(--radius-full);
  font-size: 0.85rem;
  font-weight: 500;
}

.adminBadge {
  background-color: rgba(239, 68, 68, 0.1);
  color: var(--error);
}

.editorBadge {
  background-color: rgba(245, 158, 11, 0.1);
  color: var(--warning);
}

.memberBadge {
  background-color: rgba(16, 185, 129, 0.1);
  color: var(--success);
}

.emptyState {
  text-align: center;
  padding: 60px 24px;
  background-color: white;
  border-radius: var(--radius-lg);
  border: 1px solid var(--neutral-200);
  margin: 24px 0;
  box-shadow: var(--shadow-sm);
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
}

.emptyStateIcon {
  width: 80px;
  height: 80px;
  border-radius: var(--radius-full);
  background-color: var(--neutral-100);
  color: var(--primary-color);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32px;
  margin-bottom: 16px;
}

.emptyState h3 {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--neutral-900);
  margin: 0;
}

.emptyState p {
  margin: 0;
  color: var(--neutral-600);
  line-height: 1.5;
  max-width: 500px;
}

.emptyStateButton {
  margin-top: 16px;
  display: flex;
  align-items: center;
  gap: 8px;
  background-color: var(--primary-color);
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: var(--radius-md);
  cursor: pointer;
  transition: all var(--transition-normal);
  font-weight: 500;
}

.modalOverlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.6);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  backdrop-filter: blur(4px);
}

.modal {
  background-color: white;
  border-radius: var(--radius-lg);
  width: 100%;
  max-width: 550px;
  box-shadow: var(--shadow-lg);
  border: 1px solid var(--neutral-200);
  overflow: hidden;
  animation: modalFadeIn 0.3s ease;
}

@keyframes modalFadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.modalHeader {
  background-color: var(--neutral-50);
  padding: 20px 28px;
  border-bottom: 1px solid var(--neutral-200);
}

.modalHeader h3 {
  margin: 0;
  color: var(--neutral-900);
  font-size: 1.5rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 12px;
}

.modalIcon {
  color: var(--primary-color);
  font-size: 24px;
}

.formError {
  background-color: rgba(239, 68, 68, 0.1);
  color: var(--error);
  padding: 12px 16px;
  border-radius: var(--radius-md);
  margin: 16px 28px;
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 0.95rem;
}

.formGroup {
  margin-bottom: 24px;
  padding: 0 28px;
}

.formGroup:first-of-type {
  margin-top: 28px;
}

.formGroup label {
  display: block;
  margin-bottom: 10px;
  color: var(--neutral-700);
  font-weight: 500;
}

.formGroup input,
.formGroup select,
.formGroup textarea {
  width: 100%;
  padding: 14px 16px;
  background-color: white;
  border: 1px solid var(--neutral-300);
  border-radius: var(--radius-md);
  font-size: 16px;
  color: var(--neutral-800);
  transition: all var(--transition-fast);
}

.formGroup input:focus,
.formGroup select:focus,
.formGroup textarea:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.formGroup textarea {
  min-height: 120px;
  resize: vertical;
}

.roleDescription {
  margin-top: 8px;
  font-size: 0.9rem;
  color: var(--neutral-600);
  line-height: 1.5;
}

.infoBox {
  background-color: rgba(59, 130, 246, 0.1);
  color: var(--primary-color);
  padding: 16px;
  border-radius: var(--radius-md);
  margin: 0 28px 24px;
  font-size: 0.95rem;
}

.modalButtons {
  display: flex;
  justify-content: flex-end;
  gap: 16px;
  padding: 20px 28px;
  background-color: var(--neutral-50);
  border-top: 1px solid var(--neutral-200);
}

.cancelButton {
  padding: 12px 20px;
  background-color: var(--neutral-100);
  color: var(--neutral-700);
  border: 1px solid var(--neutral-300);
  border-radius: var(--radius-md);
  cursor: pointer;
  transition: all var(--transition-normal);
  font-weight: 500;
  font-size: 16px;
}

.cancelButton:hover {
  background-color: var(--neutral-200);
  transform: translateY(-2px);
  box-shadow: var(--shadow-sm);
}

.submitButton {
  padding: 12px 24px;
  background-color: var(--primary-color);
  color: white;
  border: none;
  border-radius: var(--radius-md);
  cursor: pointer;
  transition: all var(--transition-normal);
  font-weight: 500;
  font-size: 16px;
  box-shadow: var(--shadow-sm);
}

.submitButton:hover {
  background-color: var(--primary-dark);
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

/* صفحة الصلاحيات */
.permissionsContainer {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 24px;
  margin-top: 16px;
}

.permissionCard {
  background-color: white;
  border-radius: var(--radius-lg);
  border: 1px solid var(--neutral-200);
  box-shadow: var(--shadow-sm);
  overflow: hidden;
  transition: all var(--transition-normal);
}

.permissionCard:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-md);
}

.permissionHeader {
  background-color: var(--neutral-50);
  padding: 16px 20px;
  border-bottom: 1px solid var(--neutral-200);
}

.permissionHeader h3 {
  margin: 0;
  color: var(--neutral-900);
  font-size: 1.25rem;
  font-weight: 600;
}

.permissionList {
  padding: 20px;
  list-style-type: none;
  margin: 0;
}

.permissionList li {
  padding: 8px 0;
  border-bottom: 1px solid var(--neutral-100);
  color: var(--neutral-700);
  position: relative;
  padding-right: 24px;
}

.permissionList li:last-child {
  border-bottom: none;
}

.permissionList li::before {
  content: '✓';
  position: absolute;
  right: 0;
  color: var(--success);
  font-weight: bold;
}

/* تصميم متجاوب */
@media (max-width: 1024px) {
  .dashboard {
    flex-direction: column;
  }

  .sidebar {
    width: 100%;
  }

  .tabs {
    flex-direction: row;
    overflow-x: auto;
  }

  .tabButton {
    border-right: none;
    border-bottom: 3px solid transparent;
  }

  .activeTab {
    border-right-color: transparent;
    border-bottom-color: var(--primary-color);
  }

  .statsContainer {
    flex-direction: row;
  }

  .statCard {
    flex: 1;
  }
}

@media (max-width: 768px) {
  .permissionsContainer {
    grid-template-columns: 1fr;
  }

  .groupsList {
    grid-template-columns: 1fr;
  }

  .membersTable {
    display: block;
    overflow-x: auto;
  }
}
.groupCard {
  border-bottom: 1px solid #e0e7ef;
  margin-bottom: 18px;
  padding-bottom: 18px;
}

.groupsList {
  border-top: 1px solid #e0e7ef;
}

.membersTable tr {
  border-bottom: 1px solid #e0e7ef;
}

/* أنماط شارات نوع المهمة */
.taskTypeBadge {
  display: inline-block;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 500;
  text-align: center;
  min-width: 60px;
}

.deferralBadge {
  background-color: #fff3cd;
  color: #856404;
  border: 1px solid #ffeaa7;
}

.actionBadge {
  background-color: #d1ecf1;
  color: #0c5460;
  border: 1px solid #bee5eb;
}

/* أنماط الصلاحيات الجديدة */
.permissionHeader {
  background-color: var(--neutral-50);
  padding: 16px 20px;
  border-bottom: 1px solid var(--neutral-200);
  display: flex;
  align-items: center;
  gap: 12px;
}

.roleIcon {
  font-size: 20px;
  color: var(--primary-color);
}

.permissionItem {
  margin-bottom: 12px;
  padding: 8px 0;
}

.permissionItem:last-child {
  margin-bottom: 0;
}

.permissionItem label {
  display: flex;
  align-items: center;
  gap: 12px;
  cursor: pointer;
  font-size: 14px;
  color: var(--neutral-800);
  line-height: 1.4;
  position: relative;
  user-select: none;
}

.permissionItem input[type="checkbox"] {
  opacity: 0;
  position: absolute;
  cursor: pointer;
}

.checkmark {
  height: 18px;
  width: 18px;
  background-color: var(--neutral-100);
  border: 2px solid var(--neutral-300);
  border-radius: 4px;
  position: relative;
  transition: all 0.2s ease;
  flex-shrink: 0;
}

.permissionItem input[type="checkbox"]:checked ~ .checkmark {
  background-color: var(--primary-color);
  border-color: var(--primary-color);
}

.checkmark:after {
  content: "";
  position: absolute;
  display: none;
  left: 5px;
  top: 2px;
  width: 6px;
  height: 10px;
  border: solid white;
  border-width: 0 2px 2px 0;
  transform: rotate(45deg);
}

.permissionItem input[type="checkbox"]:checked ~ .checkmark:after {
  display: block;
}

.permissionItem label:hover .checkmark {
  border-color: var(--primary-color);
  background-color: var(--primary-light);
}

.permissionsNote {
  margin-top: 24px;
  padding: 16px;
  background-color: var(--neutral-50);
  border-radius: var(--radius-md);
  border: 1px solid var(--neutral-200);
  display: flex;
  align-items: center;
  gap: 12px;
}

.noteIcon {
  color: var(--primary-color);
  font-size: 18px;
  flex-shrink: 0;
}

.permissionsNote p {
  margin: 0;
  color: var(--neutral-700);
  font-size: 14px;
  line-height: 1.4;
}

/* معلومات المستخدم الحالي */
.currentUserInfo {
  background-color: #f8f9fa;
  border: 2px solid #e9ecef;
  border-radius: var(--radius-md);
  padding: 20px;
  margin-bottom: 24px;
}

.currentUserInfo h3 {
  margin: 0 0 12px 0;
  color: var(--neutral-900);
  font-size: 1.1rem;
}

.currentUserInfo p {
  margin: 8px 0;
  color: var(--neutral-700);
  font-size: 14px;
}

.roleChangeButtons {
  display: flex;
  gap: 12px;
  margin-top: 16px;
  flex-wrap: wrap;
}

.debugButton {
  padding: 8px 16px;
  background-color: #007bff;
  color: white;
  border: none;
  border-radius: var(--radius-md);
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.2s ease;
}

.debugButton:hover {
  background-color: #0056b3;
  transform: translateY(-2px);
}

/* أنماط عدم وجود صلاحية إدارة الأعضاء */
.noPermissionButton {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 10px 16px;
  background: var(--neutral-100);
  color: var(--neutral-500);
  border: 2px dashed var(--neutral-300);
  border-radius: 8px;
  font-size: 12px;
  font-style: italic;
  text-align: center;
  min-width: 140px;
}

.noPermissionText {
  color: var(--neutral-500);
  font-size: 12px;
  font-style: italic;
  text-align: center;
  padding: 8px;
  white-space: nowrap;
}

/* تحسين عرض الأزرار مع الصلاحيات */
.groupActions {
  display: flex;
  gap: 12px;
  align-items: center;
  flex-wrap: wrap;
}

.memberActions {
  display: flex;
  justify-content: center;
  align-items: center;
}

/* رسالة تنبيه الصلاحيات المحدودة - داخل تبويب الأعضاء */
.permissionAlert {
  background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
  border: 1px solid #ffa500;
  border-radius: var(--radius-md);
  padding: 12px 16px;
  margin: 16px 0 20px 0;
  display: flex;
  align-items: flex-start;
  gap: 10px;
  box-shadow: 0 2px 6px rgba(255, 165, 0, 0.12);
}

.permissionAlert .alertIcon {
  color: #856404;
  font-size: 18px;
  flex-shrink: 0;
  margin-top: 2px;
}

.permissionAlert .alertContent h3 {
  margin: 0 0 6px 0;
  color: #856404;
  font-size: 15px;
  font-weight: 600;
}

.permissionAlert .alertContent p {
  margin: 0;
  color: #856404;
  font-size: 13px;
  line-height: 1.4;
}

