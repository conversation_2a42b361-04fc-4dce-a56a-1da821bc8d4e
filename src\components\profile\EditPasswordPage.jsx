import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { FaSave, FaTimes, FaArrowRight, FaLock, FaEye, FaEyeSlash, FaShieldAlt, FaInfoCircle, FaUser } from 'react-icons/fa';
import TopBar from '../topbar/TopBar';
import styles from './ProfilePage.module.css';

import { updatePassword, reauthenticateWithCredential, EmailAuthProvider } from 'firebase/auth';

const EditPasswordPage = ({ currentUser }) => {
  const navigate = useNavigate();
  const [currentPassword, setCurrentPassword] = useState('');
  const [newPassword, setNewPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [showCurrentPassword, setShowCurrentPassword] = useState(false);
  const [showNewPassword, setShowNewPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);

  const handleSave = async () => {
    if (!currentUser) {
      setError('المستخدم غير مسجل الدخول.');
      return;
    }

    // التحقق من صحة البيانات
    if (!currentPassword) {
      setError('كلمة المرور الحالية مطلوبة.');
      return;
    }

    if (!newPassword) {
      setError('كلمة المرور الجديدة مطلوبة.');
      return;
    }

    if (newPassword.length < 6) {
      setError('يجب أن تكون كلمة المرور الجديدة 6 أحرف على الأقل.');
      return;
    }

    if (newPassword !== confirmPassword) {
      setError('كلمة المرور الجديدة وتأكيدها غير متطابقين.');
      return;
    }

    try {
      setLoading(true);
      setError(null);

      // إعادة المصادقة قبل تغيير كلمة المرور
      const credential = EmailAuthProvider.credential(
        currentUser.email,
        currentPassword
      );
      
      await reauthenticateWithCredential(currentUser, credential);
      
      // تغيير كلمة المرور
      await updatePassword(currentUser, newPassword);
      
      alert('تم تغيير كلمة المرور بنجاح.');
      navigate('/profile');
    } catch (e) {
      console.error('خطأ في تغيير كلمة المرور:', e);
      
      if (e.code === 'auth/wrong-password') {
        setError('كلمة المرور الحالية غير صحيحة.');
      } else if (e.code === 'auth/too-many-requests') {
        setError('تم تجاوز عدد المحاولات المسموح بها. يرجى المحاولة لاحقاً.');
      } else {
        setError('حدث خطأ أثناء تغيير كلمة المرور. يرجى المحاولة مرة أخرى.');
      }
    } finally {
      setLoading(false);
    }
  };

  const handleCancel = () => {
    navigate('/profile');
  };

  return (
    <div className={styles.pageWrapper}>
      <TopBar currentUser={currentUser} />
      <div className={styles.mainContainer}>
        <div className={styles.accountManagementSection}>
          <div className={styles.sidebarNavItem} style={{ visibility: 'hidden' }}>
            <div className={styles.sidebarNavIcon}><FaUser /></div>
            <span>المعلومات الشخصية</span>
          </div>
        </div>
        <div className={styles.mainContentArea}>
          <div className={styles.pageHeader}>
            <h2 className={styles.pageTitle}>
              تعديل كلمة المرور
            </h2>
          </div>
          
          {error && <div className={styles.errorMessage}>{error}</div>}
          
          <div className={styles.personalInfoSection}>
            <div className={styles.editFieldForm}>
              <h3 className={styles.sectionTitle}>تغيير كلمة المرور</h3>
              
              <div style={{ 
                color: '#5f6368', 
                fontSize: '14px', 
                marginBottom: '15px',
                lineHeight: '1.5',
                display: 'flex',
                alignItems: 'flex-start',
                backgroundColor: '#f8f9fa',
                padding: '12px 15px',
                borderRadius: '8px'
              }}>
                <div className="field-description">
                  <FaShieldAlt style={{ color: '#1a73e8', marginLeft: '8px', flexShrink: 0 }} />
                  <span>كلمة المرور القوية تساعد في حماية حسابك من الاختراق. استخدم مزيجًا من الأحرف والأرقام والرموز لزيادة مستوى الأمان.</span>
                </div>
              </div>
              
              <div className={styles.formGroup}>
                <div style={{ position: 'relative', marginBottom: '15px' }}>
                  <input 
                    type={showCurrentPassword ? "text" : "password"} 
                    value={currentPassword} 
                    onChange={(e) => setCurrentPassword(e.target.value)} 
                    placeholder="كلمة المرور الحالية"
                    className={styles.formInput}
                    style={{ paddingRight: '40px' }}
                  />
                  <button 
                    type="button"
                    onClick={() => setShowCurrentPassword(!showCurrentPassword)}
                    style={{
                      position: 'absolute',
                      right: '10px',
                      top: '50%',
                      transform: 'translateY(-50%)',
                      background: 'none',
                      border: 'none',
                      cursor: 'pointer',
                      color: '#5f6368'
                    }}
                  >
                    {showCurrentPassword ? <FaEyeSlash /> : <FaEye />}
                  </button>
                </div>
                
                <div style={{ position: 'relative', marginBottom: '15px' }}>
                  <input 
                    type={showNewPassword ? "text" : "password"} 
                    value={newPassword} 
                    onChange={(e) => setNewPassword(e.target.value)} 
                    placeholder="كلمة المرور الجديدة"
                    className={styles.formInput}
                    style={{ paddingRight: '40px' }}
                  />
                  <button 
                    type="button"
                    onClick={() => setShowNewPassword(!showNewPassword)}
                    style={{
                      position: 'absolute',
                      right: '10px',
                      top: '50%',
                      transform: 'translateY(-50%)',
                      background: 'none',
                      border: 'none',
                      cursor: 'pointer',
                      color: '#5f6368'
                    }}
                  >
                    {showNewPassword ? <FaEyeSlash /> : <FaEye />}
                  </button>
                </div>
                
                <div style={{ position: 'relative', marginBottom: '15px' }}>
                  <input 
                    type={showConfirmPassword ? "text" : "password"} 
                    value={confirmPassword} 
                    onChange={(e) => setConfirmPassword(e.target.value)} 
                    placeholder="تأكيد كلمة المرور الجديدة"
                    className={styles.formInput}
                    style={{ paddingRight: '40px' }}
                  />
                  <button 
                    type="button"
                    onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                    style={{
                      position: 'absolute',
                      right: '10px',
                      top: '50%',
                      transform: 'translateY(-50%)',
                      background: 'none',
                      border: 'none',
                      cursor: 'pointer',
                      color: '#5f6368'
                    }}
                  >
                    {showConfirmPassword ? <FaEyeSlash /> : <FaEye />}
                  </button>
                </div>
              </div>
              
              <div style={{ 
                color: '#5f6368', 
                fontSize: '12px', 
                marginBottom: '20px',
                lineHeight: '1.5',
                display: 'flex',
                alignItems: 'flex-start',
                backgroundColor: '#f1f3f4',
                padding: '10px 12px',
                borderRadius: '8px'
              }}>
                <div className="field-footer">
                  <FaInfoCircle style={{ color: '#5f6368', marginLeft: '8px', flexShrink: 0 }} />
                  <span>استخدم 8 أحرف على الأقل مع مزيج من الأحرف الكبيرة والصغيرة والأرقام والرموز لإنشاء كلمة مرور قوية. تجنب استخدام معلومات شخصية يمكن تخمينها بسهولة.</span>
                </div>
              </div>
              
              <div className={styles.buttonRow}>
                <button 
                  onClick={handleSave} 
                  style={{
                    background: '#1a73e8',
                    color: '#fff',
                    border: 'none',
                    borderRadius: '20px',
                    padding: '8px 16px',
                    fontSize: '14px',
                    cursor: 'pointer',
                    display: 'flex',
                    alignItems: 'center',
                    gap: '5px'
                  }}
                  disabled={loading}
                >
                  <FaSave style={{ fontSize: '12px' }} /> حفظ
                </button>
                <button 
                  onClick={handleCancel} 
                  style={{
                    background: '#f1f3f4',
                    color: '#5f6368',
                    border: '1px solid #dadce0',
                    borderRadius: '20px',
                    padding: '8px 16px',
                    fontSize: '14px',
                    cursor: 'pointer',
                    display: 'flex',
                    alignItems: 'center',
                    gap: '5px'
                  }}
                >
                  <FaTimes style={{ fontSize: '12px' }} /> إلغاء
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
      <style jsx>{`
        .field-description {
          display: flex;
          align-items: flex-start;
          gap: 8px;
          width: 100%;
        }
        .field-description span {
          flex: 1;
        }
        .field-footer {
          display: flex;
          align-items: flex-start;
          gap: 8px;
          width: 100%;
        }
        .field-footer span {
          flex: 1;
        }
      `}</style>
    </div>
  );
};

export default EditPasswordPage;