const functions = require('firebase-functions');
const admin = require('firebase-admin');
admin.initializeApp();

exports.updateCasesStats = functions.firestore
  .document('cases/{caseId}')
  .onWrite(async (change, context) => {
    const db = admin.firestore();
    const casesRef = db.collection('cases');
    
    // حساب عدد القضايا المؤجلة
    const delayedSnapshot = await casesRef.where('caseStatus', '==', 'مؤجلة').get();
    const delayedCount = delayedSnapshot.size;

    // حساب إجمالي عدد القضايا
    const totalSnapshot = await casesRef.get();
    const totalCount = totalSnapshot.size;

    await db.collection('stats').doc('global').set({
      delayedCasesCount: delayedCount,
      totalCasesCount: totalCount,
      updatedAt: new Date().toISOString(),
    }, { merge: true });
  });