/* 
 * تأثيرات خاصة للثيم الشفاف (Liquid Mode)
 * Special effects for Liquid/Transparent Theme
 */

/* تأثيرات عامة للثيم الشفاف */
body.theme-liquid {
  position: relative;
  overflow-x: hidden;
}

/* خلفية متحركة للثيم الشفاف */
body.theme-liquid::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: 
    radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),
    radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.3) 0%, transparent 50%),
    linear-gradient(135deg, rgba(99, 102, 241, 0.1) 0%, rgba(168, 85, 247, 0.1) 100%);
  z-index: -2;
  pointer-events: none;
  animation: liquidBackground 20s ease-in-out infinite;
}

/* تأثير الفقاعات المتحركة */
body.theme-liquid::after {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: 
    radial-gradient(circle at 10% 20%, rgba(255, 255, 255, 0.1) 0%, transparent 20%),
    radial-gradient(circle at 90% 80%, rgba(255, 255, 255, 0.1) 0%, transparent 20%),
    radial-gradient(circle at 30% 70%, rgba(255, 255, 255, 0.05) 0%, transparent 15%),
    radial-gradient(circle at 70% 30%, rgba(255, 255, 255, 0.05) 0%, transparent 15%);
  z-index: -1;
  pointer-events: none;
  animation: bubbleFloat 15s ease-in-out infinite;
}

/* تحسينات للعناصر في الثيم الشفاف */
body.theme-liquid .topBar,
body.theme-liquid .card,
body.theme-liquid .statCard,
body.theme-liquid .login-box,
body.theme-liquid .mainContentArea,
body.theme-liquid .tabs {
  backdrop-filter: blur(20px) saturate(180%);
  -webkit-backdrop-filter: blur(20px) saturate(180%);
  background: rgba(255, 255, 255, 0.85);
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 
    0 8px 32px rgba(31, 38, 135, 0.37),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

/* تأثيرات خاصة للأزرار في الثيم الشفاف */
body.theme-liquid .iconButton,
body.theme-liquid .profileButton,
body.theme-liquid .dropdownItem {
  backdrop-filter: blur(15px) saturate(150%);
  -webkit-backdrop-filter: blur(15px) saturate(150%);
  background: rgba(255, 255, 255, 0.7);
  border: 1px solid rgba(255, 255, 255, 0.3);
}

body.theme-liquid .iconButton:hover,
body.theme-liquid .profileButton:hover,
body.theme-liquid .dropdownItem:hover {
  background: rgba(255, 255, 255, 0.9);
  transform: translateY(-2px) scale(1.02);
  box-shadow: 
    0 12px 24px rgba(31, 38, 135, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.4);
}

/* تأثيرات للحقول في الثيم الشفاف */
body.theme-liquid input,
body.theme-liquid .searchInput {
  backdrop-filter: blur(10px) saturate(120%);
  -webkit-backdrop-filter: blur(10px) saturate(120%);
  background: rgba(255, 255, 255, 0.6);
  border: 1px solid rgba(255, 255, 255, 0.3);
}

body.theme-liquid input:focus,
body.theme-liquid .searchInput:focus {
  background: rgba(255, 255, 255, 0.8);
  box-shadow: 
    0 0 0 3px rgba(99, 102, 241, 0.2),
    0 8px 16px rgba(31, 38, 135, 0.2);
}

/* تأثيرات للنصوص في الثيم الشفاف */
body.theme-liquid h1,
body.theme-liquid h2,
body.theme-liquid h3,
body.theme-liquid h4,
body.theme-liquid h5,
body.theme-liquid h6 {
  text-shadow: 0 1px 2px rgba(255, 255, 255, 0.8);
}

/* تأثيرات للظلال في الثيم الشفاف */
body.theme-liquid .card:hover,
body.theme-liquid .statCard:hover {
  transform: translateY(-6px) scale(1.02);
  box-shadow: 
    0 20px 40px rgba(31, 38, 135, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.4);
}

/* Animations للثيم الشفاف */
@keyframes liquidBackground {
  0%, 100% {
    transform: scale(1) rotate(0deg);
    opacity: 0.8;
  }
  25% {
    transform: scale(1.1) rotate(90deg);
    opacity: 0.6;
  }
  50% {
    transform: scale(0.9) rotate(180deg);
    opacity: 0.9;
  }
  75% {
    transform: scale(1.05) rotate(270deg);
    opacity: 0.7;
  }
}

@keyframes bubbleFloat {
  0%, 100% {
    transform: translateY(0px) scale(1);
    opacity: 0.7;
  }
  33% {
    transform: translateY(-20px) scale(1.1);
    opacity: 0.4;
  }
  66% {
    transform: translateY(-10px) scale(0.9);
    opacity: 0.8;
  }
}

/* تأثيرات للانتقالات في الثيم الشفاف */
body.theme-liquid * {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* تحسينات للشفافية على الشاشات الصغيرة */
@media (max-width: 768px) {
  body.theme-liquid .topBar,
  body.theme-liquid .card,
  body.theme-liquid .statCard,
  body.theme-liquid .mainContentArea {
    backdrop-filter: blur(15px) saturate(150%);
    -webkit-backdrop-filter: blur(15px) saturate(150%);
    background: rgba(255, 255, 255, 0.9);
  }
}

/* تأثيرات للتمرير في الثيم الشفاف */
body.theme-liquid::-webkit-scrollbar {
  width: 8px;
}

body.theme-liquid::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
}

body.theme-liquid::-webkit-scrollbar-thumb {
  background: rgba(99, 102, 241, 0.3);
  border-radius: 4px;
  backdrop-filter: blur(10px);
}

body.theme-liquid::-webkit-scrollbar-thumb:hover {
  background: rgba(99, 102, 241, 0.5);
}

/* تأثيرات للتحديد في الثيم الشفاف */
body.theme-liquid ::selection {
  background: rgba(99, 102, 241, 0.3);
  color: var(--current-text-primary);
}

body.theme-liquid ::-moz-selection {
  background: rgba(99, 102, 241, 0.3);
  color: var(--current-text-primary);
}
