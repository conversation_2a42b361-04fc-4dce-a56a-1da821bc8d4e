import React, { createContext, useContext, useEffect, useState } from 'react';

// إنشاء Context للثيمات
const ThemeContext = createContext();

// Hook لاستخدام الثيم
export const useTheme = () => {
  const context = useContext(ThemeContext);
  if (!context) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  return context;
};

// مزود الثيمات
export const ThemeProvider = ({ children }) => {
  const [theme, setTheme] = useState('light');
  const [isLoading, setIsLoading] = useState(true);

  // تحميل الثيم المحفوظ عند بدء التطبيق
  useEffect(() => {
    const initializeTheme = () => {
      try {
        const savedTheme = localStorage.getItem('app-theme');
        if (savedTheme && ['light', 'dark', 'liquid'].includes(savedTheme)) {
          setTheme(savedTheme);
          applyTheme(savedTheme);
        } else {
          // تطبيق الثيم الافتراضي
          applyTheme('light');
        }
      } catch (error) {
        console.error('Error loading theme:', error);
        applyTheme('light');
      } finally {
        setIsLoading(false);
      }
    };

    initializeTheme();
  }, []);

  // تطبيق الثيم على الصفحة
  const applyTheme = (newTheme) => {
    try {
      // إزالة جميع كلاسات الثيمات السابقة
      document.body.classList.remove('theme-light', 'theme-dark', 'theme-liquid');
      
      // إضافة كلاس الثيم الجديد
      document.body.classList.add(`theme-${newTheme}`);
      
      // تحديث متغير CSS للثيم الحالي
      document.documentElement.style.setProperty('--current-theme', `'${newTheme}'`);
      
      // إضافة تأثير انتقال سلس
      document.body.style.transition = 'all 0.3s ease';
      setTimeout(() => {
        document.body.style.transition = '';
      }, 300);
    } catch (error) {
      console.error('Error applying theme:', error);
    }
  };

  // تغيير الثيم
  const changeTheme = (newTheme) => {
    if (!['light', 'dark', 'liquid'].includes(newTheme)) {
      console.warn(`Invalid theme: ${newTheme}. Using 'light' instead.`);
      newTheme = 'light';
    }
    
    try {
      setTheme(newTheme);
      applyTheme(newTheme);
      
      // حفظ الثيم في localStorage
      localStorage.setItem('app-theme', newTheme);
      
      // إرسال حدث مخصص لإعلام المكونات الأخرى بتغيير الثيم
      window.dispatchEvent(new CustomEvent('themeChanged', { 
        detail: { theme: newTheme } 
      }));
    } catch (error) {
      console.error('Error changing theme:', error);
    }
  };

  // التبديل بين الثيمات
  const toggleTheme = () => {
    const themes = ['light', 'dark', 'liquid'];
    const currentIndex = themes.indexOf(theme);
    const nextIndex = (currentIndex + 1) % themes.length;
    changeTheme(themes[nextIndex]);
  };

  // التحقق من الثيم الحالي
  const isLight = theme === 'light';
  const isDark = theme === 'dark';
  const isLiquid = theme === 'liquid';

  // الحصول على معلومات الثيم
  const getThemeInfo = () => {
    const themeMap = {
      light: {
        name: 'الوضع المضيء',
        description: 'الثيم الكلاسيكي المضيء',
        icon: '☀️'
      },
      dark: {
        name: 'الوضع المظلم',
        description: 'ثيم مظلم مريح للعينين',
        icon: '🌙'
      },
      liquid: {
        name: 'الوضع الشفاف',
        description: 'ثيم شفاف مع تأثيرات زجاجية',
        icon: '💧'
      }
    };
    
    return themeMap[theme] || themeMap.light;
  };

  // قيم السياق
  const contextValue = {
    theme,
    changeTheme,
    toggleTheme,
    isLight,
    isDark,
    isLiquid,
    getThemeInfo,
    isLoading
  };

  // عرض مؤشر التحميل أثناء تحميل الثيم
  if (isLoading) {
    return (
      <div style={{
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        height: '100vh',
        background: 'var(--neutral-100)',
        color: 'var(--neutral-800)'
      }}>
        <div style={{
          width: '40px',
          height: '40px',
          border: '4px solid var(--neutral-200)',
          borderTop: '4px solid var(--primary-color)',
          borderRadius: '50%',
          animation: 'spin 1s linear infinite'
        }}></div>
      </div>
    );
  }

  return (
    <ThemeContext.Provider value={contextValue}>
      {children}
    </ThemeContext.Provider>
  );
};

export default ThemeProvider;
