import React, { useState } from 'react';
import { FaExchangeAlt, FaChevronDown, FaChevronUp, FaGavel, FaInfoCircle } from 'react-icons/fa';
import styles from './TransferDegreeBox.module.css';

const TransferDegreeBox = ({ caseData, onTransferDegree }) => {
  const [isExpanded, setIsExpanded] = useState(false);

  // التحقق من إمكانية تحويل الدرجة
  // يمكن تحويل الدرجة فقط للقضايا القضائية وليس المحالة للخبراء
  const canTransfer = caseData?.caseStatus === 'دعوى قضائية' && 
                     caseData?.status !== 'referred_to_experts';

  // إذا لم يكن بإمكان التحويل، لا نعرض الصندوق
  if (!canTransfer) {
    return null;
  }

  const toggleExpanded = () => {
    setIsExpanded(!isExpanded);
  };

  const handleTransferClick = () => {
    if (onTransferDegree) {
      onTransferDegree();
    }
  };

  return (
    <div className={styles.transferDegreeBox}>
      {/* العنوان القابل للنقر */}
      <div className={styles.header} onClick={toggleExpanded}>
        <div className={styles.titleSection}>
          <FaExchangeAlt className={styles.icon} />
          <span className={styles.title}>تحويل الدرجة</span>
        </div>
        <div className={styles.expandIcon}>
          {isExpanded ? <FaChevronUp /> : <FaChevronDown />}
        </div>
      </div>

      {/* المحتوى المفصل */}
      {isExpanded && (
        <div className={styles.content}>
          {/* معلومات الدرجة الحالية */}
          <div className={styles.infoRow}>
            <FaGavel className={styles.rowIcon} />
            <span className={styles.label}>الدرجة الحالية:</span>
            <span className={styles.value}>{caseData.caseDegree || '—'}</span>
          </div>

          <div className={styles.infoRow}>
            <FaInfoCircle className={styles.rowIcon} />
            <span className={styles.label}>رقم القضية:</span>
            <span className={styles.value}>{caseData.fullCaseNumber || '—'}</span>
          </div>

          <div className={styles.infoRow}>
            <FaInfoCircle className={styles.rowIcon} />
            <span className={styles.label}>المحكمة:</span>
            <span className={styles.value}>{caseData.courtLocation || '—'}</span>
          </div>

          {/* معلومات التحويل */}
          <div className={styles.transferInfo}>
            <p className={styles.transferDescription}>
              يمكنك تحويل هذه القضية إلى درجة قضائية جديدة (استئناف، نقض، إلخ).
              سيتم الاحتفاظ بجميع البيانات والتاريخ الخاص بالقضية الحالية.
            </p>
          </div>

          {/* زر التحويل */}
          <div className={styles.actionRow}>
            <button 
              className={styles.transferButton}
              onClick={handleTransferClick}
              title="تحويل إلى درجة قضائية جديدة"
            >
              <FaExchangeAlt />
              تحويل الدرجة
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

export default TransferDegreeBox;
