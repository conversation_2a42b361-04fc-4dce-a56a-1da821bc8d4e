import React, { useMemo, useState, useEffect } from 'react';
import { createPortal } from 'react-dom';
import { FaHistory, FaEye } from 'react-icons/fa';
import styles from './CaseDetails.module.css';
import { cleanupHistoryEntries } from './ReportDetailsLogic';

function buildTimelineEvents(caseData) {
  const timelineEvents = [];
  if (!caseData.history || caseData.history.length === 0) {
    if (caseData.caseDate) {
      const caseDate = new Date(caseData.caseDate).toLocaleDateString('ar-EG');
      let eventDescription = '';
      if (caseData.caseStatus === 'دعوى قضائية') {
        eventDescription = `تم رفع دعوى قضائية بتاريخ ${caseDate}`;
      } else if (caseData.caseStatus === 'محضر') {
        eventDescription = `تم تحرير المحضر بتاريخ ${caseDate}`;
      } else {
        eventDescription = `تم إنشاء ${caseData.caseStatus} بتاريخ ${caseDate}`;
      }
      timelineEvents.push({ date: caseDate, event: eventDescription });
    } else if (caseData.createdAt) {
      const createdDate = new Date(caseData.createdAt).toLocaleDateString('ar-EG');
      timelineEvents.push({
        date: createdDate,
        event: `تم إنشاء ${caseData.caseStatus || 'الملف'}`
      });
    }
  }
  if (caseData.history && caseData.history.length > 0) {
    const cleanedHistory = cleanupHistoryEntries(caseData.history);
    cleanedHistory.forEach(historyItem => {
      if (historyItem.type === 'completed_action' && historyItem.completedAt) {
        const completedDate = new Date(historyItem.completedAt).toLocaleDateString('ar-EG');
        timelineEvents.push({ date: completedDate, event: `تم تنفيذ إجراء: ${historyItem.description || 'غير محدد'}` });
      } else if (historyItem.type === 'completed_deferral' && historyItem.completedAt) {
        const sessionDate = new Date(historyItem.completedAt).toLocaleDateString('ar-EG');
        let eventText = `تم حضور جلسة: ${historyItem.description || 'غير محدد'}`;
        if (historyItem.deferralDescription && historyItem.deferralDescription.trim()) {
          eventText += ` - ${historyItem.deferralDescription}`;
        }
        timelineEvents.push({ date: sessionDate, event: eventText });
      } else if (historyItem.type === 'case_created') {
        const createdDate = new Date(historyItem.timestamp || historyItem.createdAt).toLocaleDateString('ar-EG');
        timelineEvents.push({ date: createdDate, event: historyItem.action || 'تم إنشاء القضية' });
      } else if (historyItem.type === 'status_transfer' && historyItem.timestamp) {
        const transferDate = new Date(historyItem.timestamp).toLocaleDateString('ar-EG');
        timelineEvents.push({
          date: transferDate,
          event: historyItem.action || `تم تحويل حالة الملف من "${historyItem.oldStatus}" إلى "${historyItem.newStatus}"`
        });
      } else if (historyItem.type === 'degree_transfer' && historyItem.timestamp) {
        const transferDate = new Date(historyItem.timestamp).toLocaleDateString('ar-EG');
        timelineEvents.push({
          date: transferDate,
          event: historyItem.action || `تم تحويل الدرجة إلى ${historyItem.newCaseDegree}`
        });
      } else if (historyItem.type === 'report_created' && historyItem.timestamp) {
        const reportDate = new Date(historyItem.timestamp).toLocaleDateString('ar-EG');
        timelineEvents.push({
          date: reportDate,
          event: historyItem.action || 'تم كتابة محضر'
        });
      } else if (historyItem.type === 'lawsuit_created' && historyItem.timestamp) {
        const lawsuitDate = new Date(historyItem.timestamp).toLocaleDateString('ar-EG');
        timelineEvents.push({
          date: lawsuitDate,
          event: historyItem.action || 'تم رفع دعوى قضائية'
        });
      }
    });
  }
  if (caseData.timeline && caseData.timeline.length > 0) {
    caseData.timeline.forEach(event => {
      timelineEvents.push({ date: event.date, event: event.description });
    });
  }
  timelineEvents.sort((a, b) => {
    try {
      const dateA = new Date(a.date.split('/').reverse().join('-'));
      const dateB = new Date(b.date.split('/').reverse().join('-'));
      if (isNaN(dateA) || isNaN(dateB)) return 0;
      return dateB - dateA;
    } catch (e) {
      return 0;
    }
  });
  return timelineEvents;
}

const CaseArchiveView = React.memo(({ caseData, cardClassName }) => {
  const timelineEvents = useMemo(() => buildTimelineEvents(caseData), [caseData]);
  const [showFullModal, setShowFullModal] = useState(false);
  
  if (!caseData) return null;
  
  // عرض آخر 3 أحداث فقط في الكارت
  const limitedEvents = timelineEvents.slice(-3);
  
  const handleShowFullReport = () => {
    setShowFullModal(true);
    // منع التمرير في الخلفية
    document.body.classList.add('modal-open');
  };
  
  const handleCloseModal = () => {
    setShowFullModal(false);
    // إعادة تمكين التمرير
    document.body.classList.remove('modal-open');
  };
  
  // التعامل مع مفتاح Escape
  useEffect(() => {
    const handleEscapeKey = (event) => {
      if (event.key === 'Escape' && showFullModal) {
        handleCloseModal();
      }
    };

    if (showFullModal) {
      document.addEventListener('keydown', handleEscapeKey);
    }

    return () => {
      document.removeEventListener('keydown', handleEscapeKey);
      document.body.classList.remove('modal-open');
    };
  }, [showFullModal]);
  
  return (
    <>
      {cardClassName ? (
        <>
          {/* إضافة الزر في الحالة الأولى أيضاً */}
          <div className={styles.cardHeaderInSimple}>
            <div className={styles.cardHeaderContent}>
              <div className={styles.cardTitle}>التقرير</div>
              <button 
                className={styles.fullReportButton}
                onClick={handleShowFullReport}
                title="عرض التقرير كامل"
              >
                <FaEye />
                عرض التقرير كامل
              </button>
            </div>
          </div>
          <div className={styles.cardPrimaryContent}>
            {limitedEvents.length > 0 ? (
              <div className={styles.timelineScrollContainer}>
                {limitedEvents.map((event, index) => (
                  <div key={index} className={styles.timelineEntry}>
                    <span className={styles.timelineDate}>{event.date}</span>
                    <span className={styles.timelineDescription}>{event.event}</span>
                  </div>
                ))}
              </div>
            ) : (
              <div className={styles.noTimeline}>لا توجد أحداث زمنية مسجلة</div>
            )}
          </div>
        </>
      ) : (
        <div className={`${styles.coloredCard} ${styles.timelineCard}`}>
          <div className={styles.cardHeader}>
            <div className={styles.cardHeaderContent}>
              <div className={styles.cardTitle}>التقرير</div>
              <button 
                className={styles.fullReportButton}
                onClick={handleShowFullReport}
                title="عرض التقرير كامل"
              >
                <FaEye />
                عرض التقرير كامل
              </button>
            </div>
          </div>
          <div className={styles.cardPrimaryContent}>
            {limitedEvents.length > 0 ? (
              <div className={styles.timelineScrollContainer}>
                {limitedEvents.map((event, index) => (
                  <div key={index} className={styles.timelineEntry}>
                    <span className={styles.timelineDate}>{event.date}</span>
                    <span className={styles.timelineDescription}>{event.event}</span>
                  </div>
                ))}
              </div>
            ) : (
              <div className={styles.noTimeline}>لا توجد أحداث زمنية مسجلة</div>
            )}
          </div>
        </div>
      )}
      
      {/* نافذة منبثقة لعرض التقرير كامل باستخدام Portal */}
      {showFullModal && createPortal(
        <div 
          className={styles.fullReportModalOverlay} 
          onClick={handleCloseModal}
          style={{
            position: 'fixed',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            width: '100vw',
            height: '100vh',
            zIndex: 2147483647,
            background: 'rgba(0, 0, 0, 0.8)',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            padding: '20px',
            margin: 0,
            boxSizing: 'border-box'
          }}
        >
          <div 
            className={styles.fullReportModal} 
            onClick={(e) => e.stopPropagation()}
            style={{
              background: 'white',
              borderRadius: '20px',
              maxWidth: '800px',
              width: '90%',
              maxHeight: '85vh',
              display: 'flex',
              flexDirection: 'column',
              boxShadow: '0 25px 80px rgba(0, 0, 0, 0.5)',
              position: 'relative',
              margin: 'auto',
              zIndex: 2147483647,
              overflow: 'hidden'
            }}
          >
            <div className={styles.fullReportModalHeader}>
              <h3>التقرير الكامل</h3>
              <button 
                className={styles.closeModalButton}
                onClick={handleCloseModal}
              >
                ×
              </button>
            </div>
            <div className={styles.fullReportModalContent}>
              {timelineEvents.length > 0 ? (
                <div className={styles.timelineScrollContainer}>
                  {timelineEvents.map((event, index) => (
                    <div key={index} className={styles.timelineEntry}>
                      <span className={styles.timelineDate}>{event.date}</span>
                      <span className={styles.timelineDescription}>{event.event}</span>
                    </div>
                  ))}
                </div>
              ) : (
                <div className={styles.noTimeline}>لا توجد أحداث زمنية مسجلة</div>
              )}
            </div>
          </div>
        </div>,
        document.body
      )}
    </>
  );
});

export default CaseArchiveView;
