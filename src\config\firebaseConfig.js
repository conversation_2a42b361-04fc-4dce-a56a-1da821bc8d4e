import { initializeApp } from 'firebase/app';
import { getAuth } from 'firebase/auth';
import { getFirestore, initializeFirestore, CACHE_SIZE_UNLIMITED } from 'firebase/firestore';

// استبدل القيم دي بإعدادات مشروعك من Firebase Console
const firebaseConfig = {
  apiKey: "AIzaSyD_lyFiT_DQhgkAJhpcR3884lERl_sL8nk",
  authDomain: "formflow-g67xu.firebaseapp.com",
  projectId: "formflow-g67xu",
  storageBucket: "formflow-g67xu.firebasestorage.app",
  messagingSenderId: "162150896311",
  appId: "1:162150896311:web:e6f39809e9b4f2e86b7332"
};

// تهيئة Firebase
// تهيئة Firebase
const app = initializeApp(firebaseConfig);

// إعداد Auth
export const auth = getAuth(app);

// إعداد Firestore مع التخزين المؤقت (Caching) والإعدادات المحسنة
export const db = initializeFirestore(app, {
  cache: {
    type: 'persistent', // استخدم 'persistent' للتخزين المؤقت على القرص
    cacheSizeBytes: CACHE_SIZE_UNLIMITED, // حجم التخزين غير محدود
  },
  // إعدادات إضافية لتحسين الأداء
  experimentalForceLongPolling: false, // تعطيل Long Polling لتحسين الأداء
  ignoreUndefinedProperties: true, // تجاهل الخصائص غير المعرفة
});