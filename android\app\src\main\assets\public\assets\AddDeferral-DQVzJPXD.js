import{r,j as s,c as b,d as R,q as T,w as D,g as w}from"./index-Bd3HN_hN.js";import{c as B,d as E}from"./index-DQWGoQ3q.js";import{s as t}from"./ReportDetails.module-BodVG5Gz.js";import"./iconBase-BmtohqY9.js";const G=({currentUser:l,caseItem:I,deferrals:q,setDeferrals:F,history:A,setHistory:C,onSave:p,onCancel:x,onAddActions:k,isUnderConsideration:j})=>{const[i,f]=r.useState(""),[o,g]=r.useState([]),[m,c]=r.useState(null),[S,v]=r.useState([]);r.useEffect(()=>{(async()=>{if(!l||!l.uid){c("لا يوجد مستخدم مسجل الدخول. يرجى تسجيل الدخول لعرض القوالب.");return}try{const n=b(R,"deferralTemplates"),d=T(n,D("userId","==",l.uid)),u=(await w(d)).docs.map(h=>({id:h.id,...h.data()}));v(u),console.log("Loaded Templates:",u)}catch(n){console.error("Error fetching templates:",n),c("خطأ في جلب القوالب: "+(n.message||"غير معروف"))}})()},[l]);const y=a=>{g(n=>n.includes(a)?n.filter(d=>d!==a):[...n,a])},N=async()=>{try{console.log("Saving deferral with reportDate:",i,"and reasons:",o),await p(i,o,c)}catch(a){console.error("Error in handleSave:",a),c("خطأ أثناء حفظ التأجيل: "+(e.message||"غير معروف"))}};return j?s.jsx("div",{style:{color:"red",textAlign:"center",padding:"20px"},children:'لا يمكن إضافة تأجيلة لأن حالة القضية "قيد النظر".'}):s.jsxs("div",{className:t.addReportForm,children:[m&&s.jsx("div",{style:{color:"red",marginBottom:"15px"},children:m}),s.jsxs("div",{className:t.dateReasonSection,children:[s.jsxs("div",{className:t.dateField,children:[s.jsx("label",{children:"تاريخ التأجيلة:"}),s.jsx("input",{type:"date",value:i,onChange:a=>f(a.target.value),className:t.dateInput,min:new Date().toISOString().split("T")[0]})]}),s.jsxs("div",{className:t.reasonSection,children:[s.jsx("label",{children:"أسباب التأجيلة:"}),s.jsx("div",{className:t.selectedReasons,children:o.length>0?o.join("، "):s.jsx("span",{className:t.noSelection,children:"لم يتم اختيار أسباب"})}),s.jsx("div",{className:t.reasonButtons,children:S.map(a=>s.jsx("button",{className:`${t.reasonButton} ${o.includes(a.reason)?t.selected:""}`,onClick:()=>y(a.reason),type:"button",children:a.reason},a.reason))})]})]}),s.jsxs("div",{className:t.reportFormButtons,children:[s.jsxs("button",{onClick:N,className:t.saveButton,children:[s.jsx(B,{className:t.buttonIcon}),s.jsx("span",{children:"حفظ الإضافة"})]}),s.jsxs("button",{onClick:x,className:t.cancelButton,children:[s.jsx(E,{className:t.buttonIcon}),s.jsx("span",{children:"إلغاء"})]})]})]})};export{G as default};
