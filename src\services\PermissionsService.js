// خدمة إدارة الصلاحيات
class PermissionsService {
  constructor() {
    this.storageKey = 'user_permissions';
  }

  // حفظ الصلاحيات في localStorage
  savePermissions(permissions) {
    try {
      localStorage.setItem(this.storageKey, JSON.stringify(permissions));
    } catch (error) {
      console.error('خطأ في حفظ الصلاحيات:', error);
    }
  }

  // جلب الصلاحيات من localStorage
  getPermissions() {
    try {
      const saved = localStorage.getItem(this.storageKey);
      if (saved) {
        return JSON.parse(saved);
      }
    } catch (error) {
      console.error('خطأ في جلب الصلاحيات:', error);
    }

    // إرجاع الصلاحيات الافتراضية
    return {
      admin: {
        deleteData: true,
        addData: true,
        addCases: true,
        viewNotifications: true,
        manageMembers: true,
        assignTasks: true
      },
      editor: {
        deleteData: false,
        addData: true,
        addCases: true,
        viewNotifications: true,
        manageMembers: false,
        assignTasks: true
      },
      member: {
        deleteData: false,
        addData: false,
        addCases: false,
        viewNotifications: true,
        manageMembers: false,
        assignTasks: false
      }
    };
  }

  // جلب صلاحيات دور معين
  getRolePermissions(role) {
    const allPermissions = this.getPermissions();
    return allPermissions[role] || {};
  }

  // التحقق من صلاحية معينة لدور معين
  hasPermission(role, permission) {
    const rolePermissions = this.getRolePermissions(role);
    return rolePermissions[permission] || false;
  }

  // تحديث صلاحية معينة لدور معين
  updatePermission(role, permission, value) {
    const permissions = this.getPermissions();
    if (permissions[role]) {
      permissions[role][permission] = value;
      this.savePermissions(permissions);
    }
  }

  // جلب دور المستخدم الحالي من البيانات الموجودة
  getCurrentUserRole(userId, groups = []) {
    try {
      // إذا كان المستخدم منشئ أي مجموعة، فهو مدير
      const hasCreatedGroup = groups.some(group => group.isCreator);
      if (hasCreatedGroup) {
        return 'admin';
      }

      // إذا كان المستخدم عضو في مجموعات، استخدم أعلى دور لديه
      const userRoles = groups.filter(group => group.memberRole).map(group => group.memberRole);
      
      if (userRoles.includes('admin')) return 'admin';
      if (userRoles.includes('editor')) return 'editor';
      if (userRoles.includes('member')) return 'member';

      // كحل احتياطي، اعتبره مدير إذا لم يكن في أي مجموعة
      return 'admin';
    } catch (error) {
      console.error('خطأ في جلب دور المستخدم:', error);
      return 'admin';
    }
  }

  // تحديد دور المستخدم
  setUserRole(userId, role) {
    try {
      const userRoles = localStorage.getItem('user_roles');
      let roles = {};
      if (userRoles) {
        roles = JSON.parse(userRoles);
      }
      roles[userId] = role;
      localStorage.setItem('user_roles', JSON.stringify(roles));
    } catch (error) {
      console.error('خطأ في حفظ دور المستخدم:', error);
    }
  }

  // التحقق من صلاحية المستخدم الحالي
  currentUserHasPermission(userId, permission, groups = []) {
    const userRole = this.getCurrentUserRole(userId, groups);
    return this.hasPermission(userRole, permission);
  }
}

// إنشاء instance واحد لاستخدامه في كامل التطبيق
const permissionsService = new PermissionsService();

export default permissionsService;