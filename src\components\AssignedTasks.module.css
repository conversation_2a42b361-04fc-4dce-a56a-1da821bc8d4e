/* شبكة المهام - مطابقة لشبكة القضايا */
.tasksGrid {
  column-count: 3;
  column-gap: 20px;
  column-fill: balance;
  margin-top: 20px;
}

/* للشاشات المتوسطة - عمودين */
@media (max-width: 999px) and (min-width: 600px) {
  .tasksGrid {
    column-count: 2;
  }
}

/* للشاشات الصغيرة - عمود واحد */
@media (max-width: 599px) {
  .tasksGrid {
    column-count: 1;
  }
}

.taskCard {
  background-color: var(--page-background);
  border-radius: 20px;
  padding: 2rem;
  box-shadow:
    0 20px 60px var(--shadow-medium),
    0 10px 30px var(--shadow-light);
  cursor: pointer;
  transition: all var(--transition-normal);
  text-align: right;
  position: relative;
  display: block;
  width: 100%;
  border: none;
  animation: var(--animation-fade-in);
  overflow: hidden;
  margin-bottom: 20px;
  break-inside: avoid;
}

.taskCard:hover {
  transform: translateY(-4px);
  box-shadow:
    0 25px 70px var(--shadow-medium),
    0 15px 40px var(--shadow-light);
}

.cardHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  border-bottom: 1px solid var(--neutral-200);
  padding-bottom: 12px;
}

.taskIcon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background: var(--primary-color);
  border-radius: 12px;
  color: white;
  font-size: 1.2rem;
  flex-shrink: 0;
  transition: all 0.3s ease;
}

.taskContent {
  flex: 1;
}

.taskTitle {
  margin: 0 0 16px 0;
  color: var(--current-text-primary);
  font-size: 1.3rem;
  font-weight: 600;
  line-height: 1.4;
  word-wrap: break-word;
}

.taskMeta {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.assignedBy,
.taskDate {
  display: flex;
  align-items: center;
  gap: 8px;
  color: var(--current-text-secondary);
  font-size: 0.9rem;
  font-weight: 500;
}

.metaIcon {
  font-size: 1rem;
  color: var(--primary-color);
}

.taskBadge {
  flex-shrink: 0;
}

.typeBadge {
  display: inline-block;
  padding: 6px 12px;
  border-radius: var(--radius-sm);
  font-size: 0.8rem;
  font-weight: 600;
  text-align: center;
  min-width: 60px;
  border: none;
}

.deferralBadge {
  background: linear-gradient(135deg, #ff9800, #f57c00);
  color: white;
  box-shadow: 0 2px 8px rgba(255, 152, 0, 0.3);
}

.actionBadge {
  background: linear-gradient(135deg, #4caf50, #388e3c);
  color: white;
  box-shadow: 0 2px 8px rgba(76, 175, 80, 0.3);
}



.offlineMessage {
  text-align: center;
  padding: 40px 20px;
  color: rgba(42, 46, 112, 0.6);
  background: rgba(42, 46, 112, 0.02);
  border-radius: 0;
  border: none;
  margin: 20px;
  border-radius: 16px;
}

.offlineIcon {
  font-size: 2.5rem;
  color: rgba(42, 46, 112, 0.2);
  margin-bottom: 16px;
}

.offlineMessage p {
  margin: 0;
  font-size: 1rem;
  font-weight: 500;
}

.loadingContainer {
  text-align: center;
  padding: 40px 20px;
  color: rgba(42, 46, 112, 0.6);
}

.loadingSpinner {
  width: 32px;
  height: 32px;
  border: 3px solid rgba(42, 46, 112, 0.1);
  border-top: 3px solid #2a2e70;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loadingContainer p {
  margin: 0;
  font-size: 1rem;
  font-weight: 500;
}

/* تحسينات للشاشات الصغيرة */
@media (max-width: 768px) {
  .tasksGrid {
    column-count: 1;
    column-gap: 16px;
  }

  .taskCard {
    padding: 1.5rem;
    border-radius: var(--radius-md);
  }

  .taskTitle {
    font-size: 1.1rem;
  }

  .taskIcon {
    width: 36px;
    height: 36px;
    font-size: 1.1rem;
  }

  .typeBadge {
    padding: 4px 10px;
    font-size: 0.75rem;
    min-width: 50px;
  }

  .assignedBy,
  .taskDate {
    font-size: 0.85rem;
  }

  .offlineMessage, .loadingContainer {
    padding: 30px 16px;
  }

  .offlineIcon {
    font-size: 2.5rem;
  }

  .loadingSpinner {
    width: 28px;
    height: 28px;
  }
}