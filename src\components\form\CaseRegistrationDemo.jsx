// Demo Component to Test the Enhanced Case Registration
import React, { useState } from 'react';
import { useCaseForm } from '../../hooks/useCaseForm';
import <PERSON>Field from './FormField';
import ProgressIndicator from './ProgressIndicator';
import { caseDegrees, caseCategoriesByDegree, courtLocations, caseStatuses } from '../../utils/CaseFilters';

const CaseRegistrationDemo = () => {
  const {
    caseData,
    currentStep,
    nextStep,
    prevStep,
    goToStep,
    isStepValid,
    formProgress,
    handleFieldChange,
    handleFieldBlur,
    getStepTitle,
    getStepStatus,
    getFieldState,
    isFormReady,
    resetForm,
    totalSteps,
    isFirstStep,
    isLastStep
  } = useCaseForm();

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitSuccess, setSubmitSuccess] = useState(false);

  const handleSubmit = async (e) => {
    e.preventDefault();
    if (!isFormReady) return;

    setIsSubmitting(true);
    
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    setIsSubmitting(false);
    setSubmitSuccess(true);
    
    // Reset after success
    setTimeout(() => {
      setSubmitSuccess(false);
      resetForm();
    }, 3000);
  };

  const renderStepContent = () => {
    switch (currentStep) {
      case 1:
        return (
          <div className="step-content">
            <h3>نوع القضية والرقم</h3>
            
            <div className="status-cards">
              {caseStatuses.map((status) => (
                <div
                  key={status.value}
                  className={`status-card ${caseData.caseStatus === status.value ? 'active' : ''}`}
                  onClick={() => handleFieldChange('caseStatus', status.value)}
                >
                  <div className="status-icon">{status.icon}</div>
                  <div className="status-title">{status.label}</div>
                </div>
              ))}
            </div>

            {(caseData.caseStatus === 'دعوى قضائية' || caseData.caseStatus === 'محضر') && (
              <div className="case-number-container">
                <div className="number-inputs">
                  <FormField
                    type="number"
                    name={caseData.caseStatus === 'دعوى قضائية' ? 'caseNumber' : 'reportNumber'}
                    value={caseData.caseStatus === 'دعوى قضائية' ? caseData.caseNumber : caseData.reportNumber}
                    onChange={handleFieldChange}
                    onBlur={handleFieldBlur}
                    placeholder="رقم"
                    {...getFieldState(caseData.caseStatus === 'دعوى قضائية' ? 'caseNumber' : 'reportNumber')}
                  />
                  <span className="separator">/</span>
                  <FormField
                    type="number"
                    name="caseYear"
                    value={caseData.caseYear}
                    onChange={handleFieldChange}
                    onBlur={handleFieldBlur}
                    placeholder="السنة"
                    {...getFieldState('caseYear')}
                  />
                </div>
                <div className="generated-display">
                  <strong>
                    {caseData.caseStatus === 'دعوى قضائية' 
                      ? `${caseData.caseNumber || '___'}/${caseData.caseYear}` 
                      : `${caseData.reportNumber || '___'}/${caseData.caseYear}`
                    }
                  </strong>
                </div>
              </div>
            )}
          </div>
        );

      case 2:
        return (
          <div className="step-content">
            <h3>أطراف القضية</h3>
            
            <div className="input-row">
              <FormField
                label="اسم الموكل"
                name="clientName"
                value={caseData.clientName}
                onChange={handleFieldChange}
                onBlur={handleFieldBlur}
                placeholder="أدخل الاسم كاملاً"
                required
                {...getFieldState('clientName')}
              />
              
              <FormField
                label="اسم الخصم"
                name="opponentName"
                value={caseData.opponentName}
                onChange={handleFieldChange}
                onBlur={handleFieldBlur}
                placeholder="أدخل اسم الطرف الآخر"
                {...getFieldState('opponentName')}
              />
            </div>
          </div>
        );

      case 3:
        return (
          <div className="step-content">
            <h3>التفاصيل القانونية</h3>
            
            {caseData.caseStatus === 'دعوى قضائية' && (
              <div className="input-row">
                <FormField
                  label="رقم الدائرة"
                  name="circleNumber"
                  type="number"
                  value={caseData.circleNumber}
                  onChange={handleFieldChange}
                  onBlur={handleFieldBlur}
                  placeholder="رقم الدائرة"
                  required
                  {...getFieldState('circleNumber')}
                />
                
                <FormField
                  label="المحكمة"
                  name="courtLocation"
                  type="select"
                  value={caseData.courtLocation}
                  onChange={handleFieldChange}
                  onBlur={handleFieldBlur}
                  options={courtLocations.map(court => ({ value: court.name, label: court.name }))}
                  required
                  {...getFieldState('courtLocation')}
                />
                
                <FormField
                  label="درجة الدعوى"
                  name="caseDegree"
                  type="select"
                  value={caseData.caseDegree}
                  onChange={handleFieldChange}
                  onBlur={handleFieldBlur}
                  options={caseDegrees}
                  required
                  {...getFieldState('caseDegree')}
                />
                
                <FormField
                  label="نوع الدعوى"
                  name="caseCategory"
                  type="select"
                  value={caseData.caseCategory}
                  onChange={handleFieldChange}
                  onBlur={handleFieldBlur}
                  options={caseData.caseDegree ? caseCategoriesByDegree[caseData.caseDegree] || [] : []}
                  required
                  disabled={!caseData.caseDegree}
                  {...getFieldState('caseCategory')}
                />
                
                <FormField
                  label="تاريخ رفع الدعوى"
                  name="caseDate"
                  type="date"
                  value={caseData.caseDate}
                  onChange={handleFieldChange}
                  onBlur={handleFieldBlur}
                  required
                  {...getFieldState('caseDate')}
                />
              </div>
            )}
            
            {(caseData.caseStatus === 'محضر' || caseData.caseStatus === 'قيد النظر') && (
              <div className="input-row">
                <FormField
                  label="الجهة المختصة"
                  name="reportLocation"
                  value={caseData.reportLocation}
                  onChange={handleFieldChange}
                  onBlur={handleFieldBlur}
                  placeholder="اسم الجهة أو المؤسسة"
                  required
                  {...getFieldState('reportLocation')}
                />
                
                {caseData.caseStatus === 'محضر' && (
                  <FormField
                    label="تاريخ المحضر"
                    name="caseDate"
                    type="date"
                    value={caseData.caseDate}
                    onChange={handleFieldChange}
                    onBlur={handleFieldBlur}
                    required
                    {...getFieldState('caseDate')}
                  />
                )}
              </div>
            )}
          </div>
        );

      case 4:
        return (
          <div className="step-content">
            <h3>الوصف والملاحظات</h3>
            
            <FormField
              label="وصف القضية"
              name="caseDescription"
              type="textarea"
              value={caseData.caseDescription}
              onChange={handleFieldChange}
              onBlur={handleFieldBlur}
              placeholder="اكتب وصفاً مفصلاً للقضية..."
              required
              maxLength={1000}
              rows={6}
              helperText="يرجى كتابة وصف واضح ومفصل للقضية لتسهيل المتابعة"
              {...getFieldState('caseDescription')}
            />
          </div>
        );

      default:
        return null;
    }
  };

  if (submitSuccess) {
    return (
      <div className="case-registration-page">
        <div className="main-container">
          <div className="registration-card">
            <div className="form-success-state">
              <div className="success-icon">
                <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                  <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
                  <polyline points="22 4 12 14.01 9 11.01"></polyline>
                </svg>
              </div>
              <h3>تم حفظ القضية بنجاح!</h3>
              <p>سيتم توجيهك إلى الصفحة الرئيسية قريباً...</p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="case-registration-page">
      <div className="main-container">
        <div className="registration-card">
          <div className="card-header">
            <div className="header-content">
              <h1>تسجيل قضية جديدة</h1>
              <p>املأ البيانات المطلوبة لإنشاء ملف قضية</p>
            </div>
          </div>

          <ProgressIndicator
            currentStep={currentStep}
            totalSteps={totalSteps}
            onStepClick={goToStep}
            getStepStatus={getStepStatus}
            getStepTitle={getStepTitle}
            progress={formProgress}
          />

          <form onSubmit={handleSubmit} className="registration-form">
            <div className="form-body">
              {renderStepContent()}
            </div>

            <div className="form-navigation">
              <div className="nav-buttons">
                {!isFirstStep && (
                  <button type="button" className="nav-btn prev-btn" onClick={prevStep}>
                    ← السابق
                  </button>
                )}
                
                {!isLastStep ? (
                  <button 
                    type="button" 
                    className={`nav-btn next-btn ${!isStepValid ? 'disabled' : ''}`}
                    onClick={nextStep}
                    disabled={!isStepValid}
                  >
                    التالي →
                  </button>
                ) : (
                  <div className="final-buttons">
                    <button 
                      type="submit" 
                      className={`submit-btn ${!isFormReady || isSubmitting ? 'disabled' : ''}${isSubmitting ? ' loading' : ''}`}
                      disabled={!isFormReady || isSubmitting}
                    >
                      {isSubmitting ? 'جاري الحفظ...' : 'حفظ القضية'}
                    </button>
                    <button
                      type="button"
                      onClick={resetForm}
                      className="cancel-btn"
                      disabled={isSubmitting}
                    >
                      إعادة تعيين
                    </button>
                  </div>
                )}
              </div>
            </div>
          </form>
          
          {isSubmitting && (
            <div className="form-loading-overlay">
              <div className="loading-spinner"></div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default CaseRegistrationDemo;