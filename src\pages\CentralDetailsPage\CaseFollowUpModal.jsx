import React, { useState, useCallback, useEffect } from 'react';
import { FaChevronDown, FaChevronUp } from 'react-icons/fa';
import { getActiveAccount } from '../../services/StorageService';
import permissionsService from '../../services/PermissionsService';
import { getGroups } from '../../services/GroupsService';
import styles from './CaseFollowUpModal.module.css';

const CaseFollowUpModal = React.memo(({ onClose, onSave, caseId, userId, savedNotes = [] }) => {
  const [analysis, setAnalysis] = useState('');
  const [selectedType, setSelectedType] = useState('');
  const [loading, setLoading] = useState(false);
  const [showDetails, setShowDetails] = useState(false);
  const [longPressedNote, setLongPressedNote] = useState(null);
  const [pressTimer, setPressTimer] = useState(null);
  const [showDeleteConfirmation, setShowDeleteConfirmation] = useState(false);
  const [noteToDelete, setNoteToDelete] = useState(null);
  const [canDeleteData, setCanDeleteData] = useState(false);
  const [userGroups, setUserGroups] = useState([]);

  // التحقق من صلاحيات المستخدم
  useEffect(() => {
    const fetchPermissions = async () => {
      if (userId && getActiveAccount() === 'online') {
        try {
          const groups = await getGroups(userId);
          setUserGroups(groups);
          
          const userRole = permissionsService.getCurrentUserRole(userId, groups);
          const hasDeletePermission = permissionsService.hasPermission(userRole, 'deleteData');
          setCanDeleteData(hasDeletePermission);
          
          console.log('🔍 CaseFollowUpModal - User Role:', userRole);
          console.log('🔍 CaseFollowUpModal - Can Delete Data (Notes):', hasDeletePermission);
        } catch (error) {
          console.error('خطأ في جلب صلاحيات المستخدم:', error);
          setCanDeleteData(false);
        }
      } else {
        // في الوضع المحلي، السماح بالحذف افتراضياً
        setCanDeleteData(true);
      }
    };

    fetchPermissions();
  }, [userId]);

  // دالة الضغط المطول
  const handleLongPress = useCallback((noteIndex) => {
    if (!canDeleteData) {
      console.log('🚫 المستخدم لا يملك صلاحية حذف البيانات');
      return;
    }
    
    const timer = setTimeout(() => {
      setNoteToDelete(noteIndex);
      setShowDeleteConfirmation(true);
    }, 800);
    setPressTimer(timer);
  }, [canDeleteData]);

  const clearLongPress = useCallback(() => {
    if (pressTimer) {
      clearTimeout(pressTimer);
      setPressTimer(null);
    }
  }, [pressTimer]);

  // حفظ الملاحظة
  const handleSave = async () => {
    if (!selectedType || !analysis.trim()) return;
    setLoading(true);
    try {
      const noteId = `${caseId}-note-${Date.now()}`;
      const newNote = {
        id: noteId,
        type: selectedType,
        analysis: analysis.trim(),
        isDeleted: false,
        userId: userId,
        createdAt: new Date().toISOString()
      };
      
      if (onSave) {
        await onSave(newNote);
      }
      
      setAnalysis('');
      setSelectedType('');
      alert('تم حفظ الملاحظة بنجاح');
    } catch (error) {
      console.error('خطأ في حفظ الملاحظة:', error);
      alert('حدث خطأ أثناء حفظ الملاحظة. يرجى المحاولة مرة أخرى.');
    } finally {
      setLoading(false);
    }
  };

  // دالة تأكيد الحذف
  const confirmDelete = async () => {
    if (noteToDelete === null) return;
    
    setLoading(true);
    try {
      const activeAccount = getActiveAccount();
      const accountType = activeAccount === 'online' ? 'أونلاين' : 'محلي';
      
      if (onSave) {
        const updatedNotes = savedNotes.filter((_, index) => index !== noteToDelete);
        await onSave({ notes: updatedNotes, deleted: true });
      }
      
      setShowDeleteConfirmation(false);
      setNoteToDelete(null);
      alert(`تم حذف الملاحظة بنجاح من الحساب ${accountType}`);
    } catch (error) {
      const activeAccount = getActiveAccount();
      const accountType = activeAccount === 'online' ? 'الأونلاين' : 'المحلي';
      console.error('خطأ في حذف الملاحظة:', error);
      alert(`حدث خطأ أثناء حذف الملاحظة من الحساب ${accountType}. يرجى المحاولة مرة أخرى.`);
    } finally {
      setLoading(false);
    }
  };

  // دالة إلغاء الحذف
  const cancelDelete = () => {
    setShowDeleteConfirmation(false);
    setNoteToDelete(null);
  };

  const renderTypeButton = (label) => (
    <button
      className={`${styles.styledButton} ${selectedType === label ? styles.activeButton : ''}`}
      onClick={() => setSelectedType(label)}
    >
      {label}
    </button>
  );

  return (
    <div className={styles.modalContent}>
      <div className={styles.notesHeader}>
        <div className={styles.notesHeaderLeft}>
          <h2 className={styles.notesHeaderTitle}>الملاحظات</h2>
        </div>
      </div>
      <div className={styles.notesContent}>
        <div className={styles.notesInnerContent}>
          {savedNotes.length > 0 ? (
            <div className={styles.savedNotesSection}>
              <h3 className={styles.textareaTitle}>سجل البيانات</h3>
              <ul className={styles.savedNotesList}>
                {savedNotes.map((note, index) => (
                  <li
                    key={index}
                    className={`${styles.savedNoteItem} ${canDeleteData ? styles.deletable : styles.nonDeletable}`}
                    {...(canDeleteData && {
                      onMouseDown: () => handleLongPress(index),
                      onMouseUp: clearLongPress,
                      onMouseLeave: clearLongPress,
                      onTouchStart: () => handleLongPress(index),
                      onTouchEnd: clearLongPress,
                    })}
                  >
                    <div className={styles.noteHeader}>
                      <span className={styles.noteType}>{note.type}</span>
                    </div>
                    <div className={styles.noteContent}>{note.analysis}</div>
                  </li>
                ))}
              </ul>
            </div>
          ) : (
            <div className={styles.emptyNotesSpace}>
              <div className={styles.emptyNotesMessage}>
                <h3 className={styles.textareaTitle}>سجل البيانات</h3>
                <p className={styles.emptyText}>لا توجد ملاحظات محفوظة بعد</p>
              </div>
            </div>
          )}
          {showDetails && (
            <>
              <div className={styles.typeSection}>
                <h3 className={styles.textareaTitle}>اختيار نوع الملاحظة</h3>
                <div className={styles.buttonRow}>
                  {renderTypeButton('رقم صادر')}
                  {renderTypeButton('رقم وارد')}
                  {renderTypeButton('رقم إعلان')}
                  {renderTypeButton('أخرى')}
                </div>
              </div>
              <div className={styles.textareaSection}>
                <h3 className={styles.textareaTitle}>تحرير ملاحظات</h3>
                <div className={styles.textareaContainer}>
                  <textarea
                    className={styles.styledInput}
                    value={analysis}
                    onChange={(e) => setAnalysis(e.target.value)}
                    placeholder={`اكتب ملاحظة ${selectedType || ''}`}
                    disabled={!selectedType}
                  />
                </div>
              </div>
              <div className={styles.buttonGroup}>
                <button 
                  onClick={handleSave} 
                  disabled={!selectedType || !analysis.trim() || loading}
                  className={styles.saveButton}
                >
                  {loading ? 'جاري الحفظ...' : 'حفظ'}
                </button>
              </div>
            </>
          )}
        </div>
      </div>
      <div
        className={styles.expandToggle}
        onClick={() => setShowDetails((prev) => !prev)}
      >
        {showDetails ? 'عرض أقل' : 'عرض المزيد'}
        {showDetails ? <FaChevronUp /> : <FaChevronDown />}
      </div>

      {/* نافذة تأكيد الحذف */}
      {showDeleteConfirmation && (
        <div className={styles.deleteModalOverlay}>
          <div className={styles.deleteModal}>
            <div className={styles.deleteModalHeader}>
              <h3>تأكيد الحذف</h3>
            </div>
            <div className={styles.deleteModalContent}>
              <p>هل أنت متأكد من حذف هذه الملاحظة؟</p>
              {noteToDelete !== null && savedNotes[noteToDelete] && (
                <div className={styles.notePreview}>
                  <strong>{savedNotes[noteToDelete].type}: </strong>
                  <span>{savedNotes[noteToDelete].analysis}</span>
                </div>
              )}
            </div>
            <div className={styles.deleteModalActions}>
              <button 
                className={styles.cancelButton}
                onClick={cancelDelete}
                disabled={loading}
              >
                إلغاء
              </button>
              <button 
                className={styles.confirmDeleteButton}
                onClick={confirmDelete}
                disabled={loading}
              >
                {loading ? 'جاري الحذف...' : 'حذف'}
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
});

export default CaseFollowUpModal;
