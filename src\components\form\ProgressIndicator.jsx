// Enhanced Progress Indicator Component
import React from 'react';

const ProgressIndicator = ({ 
  currentStep, 
  totalSteps = 4, 
  onStepClick,
  getStepStatus = () => 'pending',
  getStepTitle = () => '',
  showLabels = true,
  showProgressBar = true,
  progress = 0
}) => {
  const steps = Array.from({ length: totalSteps }, (_, i) => i + 1);

  const getStepIcon = (step, status) => {
    switch (status) {
      case 'completed':
        return (
          <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
            <polyline points="20 6 9 17 4 12"></polyline>
          </svg>
        );
      case 'current':
        return step;
      case 'error':
        return (
          <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
            <line x1="18" y1="6" x2="6" y2="18"></line>
            <line x1="6" y1="6" x2="18" y2="18"></line>
          </svg>
        );
      default:
        return step;
    }
  };

  const handleStepClick = (step) => {
    if (onStepClick && step <= currentStep) {
      onStepClick(step);
    }
  };

  return (
    <div className="progress-container">
      <div className="progress-steps">
        {steps.map((step) => {
          const status = getStepStatus(step);
          const isClickable = step <= currentStep && onStepClick;
          
          return (
            <div 
              key={step} 
              className={`progress-step ${status}`}
              onClick={() => isClickable && handleStepClick(step)}
              style={{ cursor: isClickable ? 'pointer' : 'default' }}
            >
              <div className="step-number">
                {getStepIcon(step, status)}
              </div>
              {showLabels && (
                <div className="step-label">
                  {getStepTitle(step)}
                </div>
              )}
            </div>
          );
        })}
      </div>
      
      {showProgressBar && (
        <div className="progress-bar">
          <div 
            className="progress-fill"
            style={{ width: `${Math.max(0, Math.min(100, progress))}%` }}
          />
        </div>
      )}
      
      <div className="progress-info">
        <span className="current-step">الخطوة {currentStep} من {totalSteps}</span>
        <span className="progress-percentage">{Math.round(progress)}%</span>
      </div>
    </div>
  );
};

export default ProgressIndicator;