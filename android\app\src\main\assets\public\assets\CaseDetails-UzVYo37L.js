var Qe=s=>{throw TypeError(s)};var Te=(s,a,o)=>a.has(s)||Qe("Cannot "+o);var W=(s,a,o)=>(Te(s,a,"read from private field"),o?o.call(s):a.get(s)),xe=(s,a,o)=>a.has(s)?Qe("Cannot add the same private member more than once"):a instanceof WeakSet?a.add(s):a.set(s,o),Ie=(s,a,o,i)=>(Te(s,a,"write to private field"),i?i.call(s,o):a.set(s,o),o),je=(s,a,o)=>(Te(s,a,"access private method"),o);import{S as rt,s as lt,h as Je,b as dt,n as tt,e as nt,r as v,j as e,f as ie,d as K,i as ut,k as fe,l as _e,m as ve,c as Ne,u as ht,o as st,p as ft,q as Pe,w as qe,g as Ge}from"./index-Bd3HN_hN.js";import{F as mt,a as pt,b as _t,c as ke,d as we,e as xt,f as Ye,g as It,h as We,i as Ae,j as Fe,k as Ee,l as Oe,m as jt,n as vt,o as Dt}from"./index-DQWGoQ3q.js";import{n as yt,s as At,T as Le}from"./TopBar-DgdxUhb0.js";import{a as Nt,c as bt,d as gt,b as St}from"./CaseFilters-DVgljPUG.js";import kt from"./AddDeferral-DQVzJPXD.js";import wt from"./AddAction-D3bkMDS2.js";import"./iconBase-BmtohqY9.js";import"./ReportDetails.module-BodVG5Gz.js";var ue,he,Z,le,de,Ce,Ve,et,Ct=(et=class extends rt{constructor(a,o){super();xe(this,de);xe(this,ue);xe(this,he);xe(this,Z);xe(this,le);Ie(this,ue,a),this.setOptions(o),this.bindMethods(),je(this,de,Ce).call(this)}bindMethods(){this.mutate=this.mutate.bind(this),this.reset=this.reset.bind(this)}setOptions(a){var i;const o=this.options;this.options=W(this,ue).defaultMutationOptions(a),lt(this.options,o)||W(this,ue).getMutationCache().notify({type:"observerOptionsUpdated",mutation:W(this,Z),observer:this}),o!=null&&o.mutationKey&&this.options.mutationKey&&Je(o.mutationKey)!==Je(this.options.mutationKey)?this.reset():((i=W(this,Z))==null?void 0:i.state.status)==="pending"&&W(this,Z).setOptions(this.options)}onUnsubscribe(){var a;this.hasListeners()||(a=W(this,Z))==null||a.removeObserver(this)}onMutationUpdate(a){je(this,de,Ce).call(this),je(this,de,Ve).call(this,a)}getCurrentResult(){return W(this,he)}reset(){var a;(a=W(this,Z))==null||a.removeObserver(this),Ie(this,Z,void 0),je(this,de,Ce).call(this),je(this,de,Ve).call(this)}mutate(a,o){var i;return Ie(this,le,o),(i=W(this,Z))==null||i.removeObserver(this),Ie(this,Z,W(this,ue).getMutationCache().build(W(this,ue),this.options)),W(this,Z).addObserver(this),W(this,Z).execute(a)}},ue=new WeakMap,he=new WeakMap,Z=new WeakMap,le=new WeakMap,de=new WeakSet,Ce=function(){var o;const a=((o=W(this,Z))==null?void 0:o.state)??dt();Ie(this,he,{...a,isPending:a.status==="pending",isSuccess:a.status==="success",isError:a.status==="error",isIdle:a.status==="idle",mutate:this.mutate,reset:this.reset})},Ve=function(a){tt.batch(()=>{var o,i,I,h,m,D,y,r;if(W(this,le)&&this.hasListeners()){const p=W(this,he).variables,O=W(this,he).context;(a==null?void 0:a.type)==="success"?((i=(o=W(this,le)).onSuccess)==null||i.call(o,a.data,p,O),(h=(I=W(this,le)).onSettled)==null||h.call(I,a.data,null,p,O)):(a==null?void 0:a.type)==="error"&&((D=(m=W(this,le)).onError)==null||D.call(m,a.error,p,O),(r=(y=W(this,le)).onSettled)==null||r.call(y,void 0,a.error,p,O))}this.listeners.forEach(p=>{p(W(this,he))})})},et);function Bt(s,a){const o=nt(),[i]=v.useState(()=>new Ct(o,s));v.useEffect(()=>{i.setOptions(s)},[i,s]);const I=v.useSyncExternalStore(v.useCallback(m=>i.subscribe(tt.batchCalls(m)),[i]),()=>i.getCurrentResult(),()=>i.getCurrentResult()),h=v.useCallback((m,D)=>{i.mutate(m,D).catch(yt)},[i]);if(I.error&&At(i.options.throwOnError,[I.error]))throw I.error;return{...I,mutate:h,mutateAsync:I.mutate}}const $t="_pageWrapper_133h5_51",Tt="_mainContainer_133h5_69",Ft="_mainContent_133h5_91",Et="_caseTitle_133h5_131",Ot="_caseTitlePending_133h5_177",Lt="_caseTitleReport_133h5_185",Rt="_caseTitleLawsuit_133h5_193",Mt="_caseInfoGrid_133h5_203",Pt="_infoGroup_133h5_217",qt="_fadeIn_133h5_1",Gt="_group0_133h5_259",Yt="_group1_133h5_261",Wt="_group2_133h5_263",Vt="_groupTitle_133h5_267",Ht="_titleText_133h5_295",Kt="_infoItems_133h5_305",Qt="_infoItem_133h5_305",Jt="_infoLabel_133h5_343",Ut="_infoValue_133h5_355",zt="_valueContainer_133h5_373",Xt="_editFieldContainer_133h5_391",Zt="_editInput_133h5_421",en="_inputError_133h5_455",tn="_errorText_133h5_465",nn="_editIconButton_133h5_479",sn="_editActions_133h5_525",an="_saveEditButton_133h5_535",on="_cancelEditButton_133h5_535",cn="_editForm_133h5_609",rn="_formGroup_133h5_633",ln="_inputField_133h5_657",dn="_formButtons_133h5_691",un="_buttonsSection_133h5_707",hn="_editButton_133h5_723",fn="_saveButton_133h5_723",mn="_backButton_133h5_723",pn="_cancelButton_133h5_723",_n="_buttonIcon_133h5_843",xn="_errorMessage_133h5_853",In="_caseInfoWrapper_133h5_907",jn="_statusSection_133h5_921",vn="_additionalFieldsSection_133h5_945",Dn="_sectionTitle_133h5_969",yn="_pageTitle_133h5_987",An="_actionsSection_133h5_1237",Nn="_addOptions_133h5_1245",bn="_addDeferralButton_133h5_1259",gn="_addActionOptionButton_133h5_1261",Sn="_retryButton_133h5_1389",kn="_promptDialog_133h5_1423",wn="_previewDialog_133h5_1459",Cn="_previewAction_133h5_1487",Bn="_promptButtons_133h5_1505",$n="_confirmButton_133h5_1519",d={pageWrapper:$t,mainContainer:Tt,mainContent:Ft,caseTitle:Et,caseTitlePending:Ot,caseTitleReport:Lt,caseTitleLawsuit:Rt,caseInfoGrid:Mt,infoGroup:Pt,fadeIn:qt,group0:Gt,group1:Yt,group2:Wt,groupTitle:Vt,titleText:Ht,infoItems:Kt,infoItem:Qt,infoLabel:Jt,infoValue:Ut,valueContainer:zt,editFieldContainer:Xt,editInput:Zt,inputError:en,errorText:tn,editIconButton:nn,editActions:sn,saveEditButton:an,cancelEditButton:on,editForm:cn,formGroup:rn,inputField:ln,formButtons:dn,buttonsSection:un,editButton:hn,saveButton:fn,backButton:mn,cancelButton:pn,buttonIcon:_n,errorMessage:xn,caseInfoWrapper:In,statusSection:jn,additionalFieldsSection:vn,sectionTitle:Dn,pageTitle:yn,actionsSection:An,addOptions:Nn,addDeferralButton:bn,addActionOptionButton:gn,retryButton:Sn,promptDialog:kn,previewDialog:wn,previewAction:Cn,promptButtons:Bn,confirmButton:$n},Tn=({caseData:s,currentUser:a})=>{const o=nt(),[i,I]=v.useState(null),[h,m]=v.useState(""),[D,y]=v.useState(""),[r,p]=v.useState(""),[O,L]=v.useState(!1),S=gt,N=s!=null&&s.caseDegree?Nt[s.caseDegree]||[]:[],M=St,b=[{label:"اسم الموكل",value:s.clientName,field:"clientName",required:!0,type:"text"},{label:"اسم الخصم",value:s.opponentName,field:"opponentName",required:!1,type:"text"},{label:"الوصف العام للقضية",value:s.caseDescription||"غير محدد",field:"caseDescription",required:!1,type:"textarea"},...s.caseStatus!=="قيد النظر"?[{label:s.caseStatus==="محضر"?"رقم المحضر":"رقم القضية الكامل",value:s.fullCaseNumber||"—",field:"fullCaseNumber",required:!0,readOnly:!0,type:"text"}]:[]],C=s.caseStatus==="قيد النظر"?[{label:"مكان الجهة المختصة",value:s.reportLocation||"غير محدد",field:"reportLocation",required:!0,type:"text"}]:s.caseStatus==="محضر"?[{label:"مكان الجهة المختصة",value:s.reportLocation||"غير محدد",field:"reportLocation",required:!0,type:"text"},{label:"رقم المحضر",value:s.reportNumber||"—",field:"reportNumber",required:!0,type:"text"}]:s.caseStatus==="دعوى قضائية"?[{label:"مكان المحكمة",value:s.courtLocation||"غير محدد",field:"courtLocation",required:!0,type:"select",options:bt.map(u=>u.name)},{label:"رقم الدائرة",value:s.circleNumber||"غير محدد",field:"circleNumber",required:!1,type:"text"},{label:"درجة الدعوى",value:s.caseDegree||"غير محدد",field:"caseDegree",required:!0,type:"select",options:S},{label:"نوع الدعوى",value:s.caseCategory||"غير محدد",field:"caseCategory",required:!0,type:"select",options:N},{label:"تاريخ أول جلسة",value:s.firstSessionDate||"",field:"firstSessionDate",required:!1,type:"date"},{label:"رقم القضية لدى المحكمة",value:s.caseNumber||"غير محدد",field:"caseNumber",required:!0,type:"text"},{label:"سنة القضية",value:s.caseYear||"غير محدد",field:"caseYear",required:!0,type:"text"}]:[],k=[{label:"حالة القضية",value:s.caseStatus||"غير محدد",field:"caseStatus",required:!0,type:"select",options:M}],$=u=>{if(u==="المعلومات الأساسية")return s.caseStatus==="محضر"?"تفاصيل المحضر":s.caseStatus==="دعوى قضائية"?"تفاصيل الدعوى":"المعلومات الأساسية";if(u==="المعلومات القضائية"){if(s.caseStatus==="قيد النظر"||s.caseStatus==="محضر")return"تفاصيل الجهة المختصة";if(s.caseStatus==="دعوى قضائية")return"تفاصيل الدعوى القضائية"}return u==="تفاصيل إضافية"?"تفاصيل إضافية":u},T=[{title:$("المعلومات الأساسية"),icon:e.jsx(mt,{}),items:b},{title:$("المعلومات القضائية"),icon:e.jsx(pt,{}),items:C},{title:$("تفاصيل إضافية"),icon:e.jsx(_t,{}),items:k}].filter(u=>u.items.length>0),V=Bt({mutationFn:async({caseId:u,updateData:f})=>{const _=ie(K,"cases",u);await ut(_,f)},onSuccess:(u,{caseId:f,updateData:_})=>{o.setQueryData(["case",f],R=>({...R,..._})),I(null),m(""),y(""),p(""),alert("تم حفظ التعديل بنجاح.")},onError:u=>{console.error("Error saving field:",u),p("حدث خطأ أثناء حفظ التعديل.")},onSettled:()=>{L(!1)}}),j=(u,f)=>{I({...u,groupIndex:T.findIndex(_=>_.items.some(R=>R.field===u.field))}),m(f==="غير محدد"||f==="—"?"":f||""),y(""),p("")},l=()=>{if(!i||!s||!a){p("لا يمكن حفظ التعديل حالياً. يرجى تحديث الصفحة.");return}if(i.required&&!h.trim()&&i.type!=="select"){p(`${i.label} مطلوب`);return}let u=h.trim();if(i.type==="select"){if(h==="أخرى"){if(!D.trim()){p('يرجى إدخال قيمة مخصصة لـ "أخرى"');return}u=D.trim()}if(i.required&&!u){p(`${i.label} مطلوب`);return}}if(i.field==="reportNumber"&&u&&!/^\d+$/.test(u)){p("رقم المحضر يجب أن يحتوي على أرقام فقط");return}if(i.field==="caseNumber"&&u&&!/^\d+$/.test(u)){p("رقم القضية يجب أن يحتوي على أرقام فقط");return}if(i.field==="caseYear"&&u&&!/^\d{4}$/.test(u)){p("سنة القضية يجب أن تتكون من 4 أرقام فقط");return}p(""),L(!0);const f={[i.field]:u};if(i.field==="caseStatus"){const _=u;let R={};if(_==="محضر"||_==="قيد النظر")R={caseNumber:"",caseYear:"",circleNumber:"",caseDegree:"",caseCategory:"",courtLocation:"",firstSessionDate:"",fullCaseNumber:""};else if(_==="دعوى قضائية"){R={reportLocation:"",reportNumber:""};const Y=f.hasOwnProperty("caseNumber")?f.caseNumber:s.caseNumber,A=f.hasOwnProperty("caseYear")?f.caseYear:s.caseYear;Y&&A&&Y!=="غير محدد"&&A!=="غير محدد"?f.fullCaseNumber=`${Y}/${A}`:f.fullCaseNumber="",(!s.caseYear||s.caseYear==="غير محدد")&&(f.caseYear=new Date().getFullYear().toString())}Object.assign(f,R)}else if(i.field==="reportNumber"&&s.caseStatus==="محضر")f.fullCaseNumber=u;else if((i.field==="caseNumber"||i.field==="caseYear")&&s.caseStatus==="دعوى قضائية"){const _=i.field==="caseNumber"?u:s.caseNumber,R=i.field==="caseYear"?u:s.caseYear;_&&R&&_!=="غير محدد"&&R!=="غير محدد"?f.fullCaseNumber=`${_}/${R}`:f.fullCaseNumber=""}i.field==="caseDegree"&&s.caseStatus==="دعوى قضائية"&&(f.caseCategory=""),V.mutate({caseId:s.id,updateData:f})},w=()=>{I(null),m(""),y(""),p("")};return s?e.jsx("div",{className:d.caseInfoGrid,children:T.map((u,f)=>e.jsxs("div",{className:`${d.infoGroup} ${d[`group${f}`]}`,children:[e.jsxs("h3",{className:d.groupTitle,children:[u.icon,e.jsx("span",{className:d.titleText,children:u.title})]}),e.jsx("div",{className:d.infoItems,children:u.items.map(_=>{const R=i&&i.field===_.field&&i.groupIndex===f,Y=_.value&&_.value!=="غير محدد"&&_.value!=="—"&&_.value!=="";return e.jsxs("div",{className:d.infoItem,children:[e.jsxs("span",{className:d.infoLabel,children:[_.label,":"]}),R?e.jsxs("div",{className:d.editFieldContainer,children:[(()=>{switch(_.type){case"select":return e.jsxs(e.Fragment,{children:[e.jsxs("select",{value:h,onChange:A=>{m(A.target.value),p("")},className:`${d.editInput} ${r?d.inputError:""}`,children:[e.jsxs("option",{value:"",children:["اختر ",_.label]}),(_.options||[]).map(A=>e.jsx("option",{value:A,children:A},A))]}),h==="أخرى"&&e.jsx("input",{type:"text",value:D,onChange:A=>{y(A.target.value),p("")},className:`${d.editInput} ${r?d.inputError:""}`,placeholder:"أدخل قيمة مخصصة"})]});case"textarea":return e.jsx("textarea",{value:h,onChange:A=>{m(A.target.value),p("")},className:`${d.editInput} ${d.textareaInput} ${r?d.inputError:""}`,placeholder:`أدخل ${_.label}`});case"date":return e.jsx("input",{type:"date",value:h,onChange:A=>{m(A.target.value),p("")},className:`${d.editInput} ${r?d.inputError:""}`});default:return e.jsx("input",{type:"text",value:h,onChange:A=>{m(A.target.value),p("")},className:`${d.editInput} ${r?d.inputError:""}`,placeholder:`أدخل ${_.label}`,disabled:_.readOnly})}})(),r&&e.jsx("span",{className:d.errorText,children:r}),e.jsxs("div",{className:d.editActions,children:[e.jsx("button",{onClick:l,className:d.saveEditButton,disabled:O,children:O?"...":e.jsx(ke,{})}),e.jsx("button",{onClick:w,className:d.cancelEditButton,disabled:O,children:e.jsx(we,{})})]})]}):e.jsxs("div",{className:d.valueContainer,children:[e.jsx("span",{className:d.infoValue,children:_.value}),!_.readOnly&&e.jsx("button",{onClick:()=>j(_,_.value),className:d.editIconButton,title:Y?"تعديل":"إضافة",children:Y?e.jsx(xt,{}):e.jsx(Ye,{})})]})]},_.field)})})]},u.title))}):e.jsx("div",{children:"جاري تحميل بيانات القضية..."})},Fn="_combinedPane_11ost_3",En="_fadeIn_11ost_1",On="_sectionTitle_11ost_45",Ln="_sectionIcon_11ost_67",Rn="_archiveSection_11ost_77",Mn="_singleBackButton_11ost_89",Pn="_archiveButton_11ost_149",qn="_buttonIcon_11ost_173",Gn="_infoRow_11ost_181",Yn="_combinedItem_11ost_201",Wn="_reportItem_11ost_209",Vn="_reportContent_11ost_243",Hn="_reportText_11ost_257",Kn="_linkIndicator_11ost_271",Qn="_linkIcon_11ost_283",Jn="_reportActions_11ost_295",Un="_editButton_11ost_305",zn="_deleteButton_11ost_307",Xn="_historyButton_11ost_309",Zn="_strikeActionButton_11ost_311",es="_actionIcon_11ost_329",ts="_historyIcon_11ost_331",ns="_noReports_11ost_507",ss="_historyDropdown_11ost_535",is="_historyItem_11ost_545",as="_historyAction_11ost_563",os="_historyTimestamp_11ost_575",cs="_actionsSection_11ost_591",rs="_addReportForm_11ost_599",ls="_addActionForm_11ost_601",ds="_dateReasonSection_11ost_635",us="_dateField_11ost_655",hs="_dateInput_11ost_683",fs="_reasonSection_11ost_721",ms="_selectedReasons_11ost_749",ps="_noSelection_11ost_781",_s="_reasonButtons_11ost_793",xs="_reasonButton_11ost_793",Is="_selected_11ost_749",js="_addOptions_11ost_859",vs="_addDeferralButton_11ost_873",Ds="_addActionOptionButton_11ost_875",ys="_addButton_11ost_959",As="_saveButton_11ost_961",Ns="_cancelButton_11ost_963",bs="_addActionButton_11ost_965",gs="_reportFormButtons_11ost_1095",Ss="_actionFormButtons_11ost_1097",ks="_actionField_11ost_1113",ws="_actionInput_11ost_1137",Cs="_linkButtons_11ost_1177",Bs="_linkButton_11ost_1177",$s="_linkButtonActive_11ost_1227",Ts="_actionItem_11ost_1251",Fs="_actionContent_11ost_1285",Es="_actionDetails_11ost_1303",Os="_actionDescription_11ost_1319",Ls="_actionMeta_11ost_1343",Rs="_errorMessage_11ost_1355",Ms="_mainContainer_11ost_1389",Ps="_caseInfo_11ost_1413",qs="_combinedSection_11ost_1415",Gs="_pageTitle_11ost_1429",Ys="_clientTitle_11ost_1437",Ws="_infoGroup_11ost_1461",Vs="_infoCard_11ost_1471",Hs="_actionPriority_11ost_1725",Ks="_actionBar_11ost_1815",Qs="_mainActions_11ost_1831",Js="_primaryButton_11ost_1841",Us="_secondaryButton_11ost_1877",zs="_loadMoreButtons_11ost_1915",Xs="_deleted_11ost_1937",Zs="_deletedAction_11ost_1939",ei="_deletionTimestamp_11ost_1959",t={combinedPane:Fn,fadeIn:En,sectionTitle:On,sectionIcon:Ln,archiveSection:Rn,singleBackButton:Mn,archiveButton:Pn,buttonIcon:qn,infoRow:Gn,combinedItem:Yn,reportItem:Wn,reportContent:Vn,reportText:Hn,linkIndicator:Kn,linkIcon:Qn,reportActions:Jn,editButton:Un,deleteButton:zn,historyButton:Xn,strikeActionButton:Zn,actionIcon:es,historyIcon:ts,noReports:ns,historyDropdown:ss,historyItem:is,historyAction:as,historyTimestamp:os,actionsSection:cs,addReportForm:rs,addActionForm:ls,dateReasonSection:ds,dateField:us,dateInput:hs,reasonSection:fs,selectedReasons:ms,noSelection:ps,reasonButtons:_s,reasonButton:xs,selected:Is,addOptions:js,addDeferralButton:vs,addActionOptionButton:Ds,addButton:ys,saveButton:As,cancelButton:Ns,addActionButton:bs,reportFormButtons:gs,actionFormButtons:Ss,actionField:ks,actionInput:ws,linkButtons:Cs,linkButton:Bs,linkButtonActive:$s,actionItem:Ts,actionContent:Fs,actionDetails:Es,actionDescription:Os,actionMeta:Ls,errorMessage:Rs,mainContainer:Ms,caseInfo:Ps,combinedSection:qs,pageTitle:Gs,clientTitle:Ys,infoGroup:Ws,infoCard:Vs,actionPriority:Hs,actionBar:Ks,mainActions:Qs,primaryButton:Js,secondaryButton:Us,loadMoreButtons:zs,deleted:Xs,deletedAction:Zs,deletionTimestamp:ei},Re=4,Be=async(s,a,o,i)=>{const I=[...s.map((r,p)=>({...r,type:"deferral",originalIndex:p})),...a.map((r,p)=>({...r,type:"action",originalIndex:p}))];if(I.length<=Re)return{deferrals:s,actions:a};I.sort((r,p)=>new Date(r.createdAt)-new Date(p.createdAt));const h=I.slice(0,I.length-Re),m=I.slice(I.length-Re);for(const r of h){const p=r.type==="action"?"actions_archive":"deferrals_archive",O=Ne(K,`cases/${o}/${p}`),L={...r,userId:fe.currentUser.uid,archivedAt:new Date().toISOString()},S=ie(O);i.set(S,L)}const D=m.filter(r=>r.type==="deferral").map(r=>({id:r.id||`${o}-defer-${r.originalIndex}`,date:r.date,reasons:r.reasons,content:r.content,createdAt:r.createdAt,updatedAt:r.updatedAt,isDeleted:r.isDeleted,userId:r.userId})),y=m.filter(r=>r.type==="action").map(r=>({id:r.id||`${o}-action-${r.originalIndex}`,description:r.description,deadline:r.deadline,priority:r.priority,timestamp:r.timestamp,isDeleted:r.isDeleted,struckAt:r.struckAt,linkedDeferralId:r.linkedDeferralId,linkedActionId:r.linkedActionId,userId:r.userId,createdAt:r.createdAt}));return{deferrals:D,actions:y}},it=(s,a,o,i)=>{const h=Ne(K,`cases/${i}/${o==="action"?"actions_archive":"deferrals_archive"}`),m={...a,userId:fe.currentUser.uid,struckAt:new Date().toISOString()},D=ie(h);s.set(D,m)},at=async s=>{if(!fe.currentUser||!s)return null;const a=fe.currentUser.uid;try{const o=ie(K,"cases",s),i=await _e(o);return i.exists()&&i.data().userId===a?{id:i.id,...i.data()}:null}catch(o){throw console.error("خطأ في جلب بيانات القضية:",o),o}},ti=async(s,a,o,i,I,h,m,D,y,r,p,O,L)=>{if((s==null?void 0:s.caseStatus)==="قيد النظر"){alert('لا يمكن إضافة تأجيلة لأن حالة القضية "قيد النظر"');return}if(a&&o.length>0){const S=new Date;if(new Date(a)<S){alert("لا يمكن إضافة تأجيلة بتاريخ مضى");return}const M=o.join("، "),b=`${a} - ${M}`,C=s.id,k=fe.currentUser.uid;try{const $=ie(K,"cases",C),T=ve(K),j=(await _e($)).data();let l=j.deferrals||[],w=j.actions||[],u=j.history||[];if(i!==null){const _=I.findIndex((R,Y)=>Y===i);if(_!==-1){const R=l[_];l[_]={id:R.id||`${C}-defer-${_}`,date:a,reasons:o,content:b,updatedAt:new Date().toISOString(),isDeleted:!1,userId:k,createdAt:R.createdAt||new Date().toISOString()},u.push({deferralId:_,timestamp:new Date().toLocaleString(),action:`تم تعديل: "${R.content}" إلى "${b}"`,type:"edit",userId:k});const Y=u.findIndex(A=>A.deferralId===_&&A.type==="add");Y!==-1&&(u[Y]={...u[Y],content:b,date:a,type:"deferral"})}}else{const _={id:`${C}-defer-${l.length}`,date:a,reasons:o,content:b,createdAt:new Date().toISOString(),isDeleted:!1,userId:k};l.push(_),u.push({deferralId:l.length-1,timestamp:new Date().toLocaleString(),action:`تم إضافة: "${b}"`,type:"add",userId:k,content:b,date:a,type:"deferral"})}const f=await Be(l,w,C,T);l=f.deferrals,w=f.actions,T.update($,{deferrals:l,actions:w,history:u,updatedAt:new Date().toISOString()}),await T.commit(),h(l),O(u),m(""),D([]),y(!1),r(null),alert("تم حفظ التأجيلة بنجاح"),o.length===1&&L&&L()}catch($){alert("خطأ في حفظ التأجيلة: "+$.message)}}else alert("يرجى إدخال تاريخ التأجيلة واختيار سبب واحد على الأقل")},ni=async(s,a,o,i,I,h,m,D,y,r,p,O,L,S,N,M,b,C=[])=>{if(!s){alert("يرجى إدخال وصف الإجراء.");return}let k=a||new Date().toISOString().split("T")[0];if(i==="custom"){if(!a){alert("يرجى إدخال تاريخ الموعد النهائي.");return}if(new Date(a)<new Date){alert("لا يمكن إضافة إجراء بموعد نهائي مضى");return}k=a}else if(i==="deferral"){if(!I){alert("يرجى اختيار تأجيل للربط.");return}const j=m.deferrals.find((l,w)=>l.id===I||`${m.id}-defer-${w}`===I);if(!j){console.log("Deferrals available:",m.deferrals),console.log("Looking for linkedDeferralId:",I),alert("التأجيل المحدد غير موجود.");return}if(k=j.date,C.length>0&&new Date(k)>=new Date(j.date)){alert(`تاريخ الإجراء (${k}) يجب أن يكون قبل تاريخ التأجيل (${j.date})`);return}}else if(i==="action"){if(!h){alert("يرجى اختيار إجراء للربط.");return}const j=D.find(l=>l.id===h);if(!j){alert("الإجراء المحدد غير موجود.");return}k=j.deadline}const $=m.id,T=fe.currentUser.uid,V={id:`${$}-action-${D.length}`,description:s,deadline:k,priority:o||"متوسط",timestamp:new Date().toLocaleString(),isDeleted:!1,struckAt:null,linkedDeferralId:i==="deferral"?I:"",linkedActionId:i==="action"?h:"",userId:T,createdAt:new Date().toISOString()};try{const j=ie(K,"cases",$),l=ve(K),u=(await _e(j)).data();let f=u.actions||[],_=u.deferrals||[],R=u.history||[];f.push(V),R.push({actionId:f.length-1,timestamp:new Date().toLocaleString(),action:`تم إضافة إجراء: "${s}"`,type:"add",userId:T,description:s,deadline:k,type:"action"});const Y=await Be(_,f,$,l);_=Y.deferrals,f=Y.actions,l.update(j,{deferrals:_,actions:f,history:R,updatedAt:new Date().toISOString()}),await l.commit(),y(f),r(""),p(""),O("متوسط"),L(""),S(""),N(""),M(!1),b&&b(A=>[...A,{action:`تم إضافة إجراء: ${s}`,timestamp:new Date().toLocaleString()}]),alert("تم إضافة الإجراء بنجاح")}catch(j){console.error("خطأ في إضافة الإجراء:",j),alert("فشل في إضافة الإجراء. حاول مرة أخرى.")}},Me=async(s,a,o,i,I)=>{const h=i.id;try{const m=ie(K,"cases",h),D=ve(K),r=(await _e(m)).data();let p=r.actions||[],O=r.deferrals||[],L=r.history||[];const S=a.findIndex(N=>N.id===s);if(S!==-1){const N=a[S];if(N.linkedDeferralId){const b=O.findIndex((C,k)=>C.id===N.linkedDeferralId||`${i.id}-defer-${k}`===N.linkedDeferralId);b!==-1&&a.filter((k,$)=>k.linkedDeferralId===N.linkedDeferralId&&$!==S).length===0&&(O[b]={...O[b]})}else if(N.linkedActionId){const b=a.findIndex(C=>C.id===N.linkedActionId);b!==-1&&(a[b]={...a[b],linkedActionId:""})}it(D,N,"action",h),p.splice(S,1);const M=L.findIndex(b=>b.actionId===S&&b.type==="action");M!==-1&&L.splice(M,1),D.update(m,{deferrals:O,actions:p,history:L,updatedAt:new Date().toISOString()}),await D.commit(),o(p),I(L),alert("تم شطب الإجراء ونقله إلى الأرشيف بنجاح")}}catch(m){alert("خطأ في شطب الإجراء: "+m.message)}},si=(s,a,o,i)=>{const I=a.findIndex((y,r)=>r===s),h=a[I],m=new Date;if(new Date(h.date)<m){alert("لا يمكن تعديل تأجيلة مضى تاريخها");return}o(I),i({date:h.date,reasons:h.reasons||[]})},Ue=(s,a,o,i)=>{const I=a.findIndex(y=>y.id===s),h=a[I],m=new Date;if(new Date(h.deadline)<m){alert("لا يمكن تعديل إجراء مضى موعده النهائي");return}o(s),i({description:h.description,deadline:h.deadline,priority:h.priority,linkedDeferralId:h.linkedDeferralId||"",linkedActionId:h.linkedActionId||""})},ii=async(s,a,o,i,I,h,m,D,y,r)=>{if(!o||i.length===0){alert("يرجى إدخال تاريخ التأجيلة واختيار سبب واحد على الأقل");return}const p=new Date;if(new Date(o)<p){alert("لا يمكن تعديل التأجيلة بتاريخ مضى");return}const L=i.join("، "),S=`${o} - ${L}`,N=s.id,M=fe.currentUser.uid;try{const b=ie(K,"cases",N),C=ve(K),$=(await _e(b)).data();let T=$.deferrals||[],V=$.actions||[],j=$.history||[];const l=I.findIndex((u,f)=>f===a);if(l!==-1){const u=T[l];T[l]={id:u.id||`${N}-defer-${l}`,date:o,reasons:i,content:S,updatedAt:new Date().toISOString(),isDeleted:!1,userId:M,createdAt:u.createdAt||new Date().toISOString()},j.push({deferralId:l,timestamp:new Date().toLocaleString(),action:`تم تعديل: "${u.content}" إلى "${S}"`,type:"edit",userId:M});const f=j.findIndex(_=>_.deferralId===l&&_.type==="add");f!==-1&&(j[f]={...j[f],content:S,date:o,type:"deferral"})}const w=await Be(T,V,N,C);T=w.deferrals,V=w.actions,C.update(b,{deferrals:T,actions:V,history:j,updatedAt:new Date().toISOString()}),await C.commit(),h(T),r(j),m(null),D(null),alert("تم تعديل التأجيلة بنجاح")}catch(b){alert("خطأ في تعديل التأجيلة: "+b.message)}},ze=async(s,a,o,i,I,h,m,D,y,r,p,O,L)=>{if(!o){alert("يرجى إدخال وصف الإجراء");return}const S=new Date;if(new Date(i)<S){alert("لا يمكن تعديل الإجراء بموعد نهائي مضى");return}const M=s.id,b=fe.currentUser.uid;try{const C=ie(K,"cases",M),k=ve(K),T=(await _e(C)).data();let V=T.actions||[],j=T.deferrals||[],l=T.history||[];const w=D.findIndex(f=>f.id===a);if(w!==-1){const f=D[w];V[w]={...f,description:o,deadline:i,priority:I,linkedDeferralId:h||"",linkedActionId:m||"",updatedAt:new Date().toISOString()},l.push({actionId:w,timestamp:new Date().toLocaleString(),action:`تم تعديل الإجراء: "${f.description}" إلى "${o}"`,type:"edit",userId:b})}const u=await Be(j,V,M,k);j=u.deferrals,V=u.actions,k.update(C,{deferrals:j,actions:V,history:l,updatedAt:new Date().toISOString()}),await k.commit(),y(V),L(l),r(null),p(null),alert("تم تعديل الإجراء بنجاح")}catch(C){alert("خطأ في تعديل الإجراء: "+C.message)}},Xe=async(s,a,o,i,I,h,m,D)=>{const y=a.findIndex((S,N)=>N===s),r=a[y],p=new Date;if(new Date(r.date)<p){alert("لا يمكن شطب تأجيلة مضى تاريخها");return}const L=h.id;try{const S=ie(K,"cases",L),N=ve(K),b=(await _e(S)).data();let C=b.deferrals||[],k=b.actions||[],$=b.history||[];if(y>=0&&y<C.length){const T=C[y];k=k.map((j,l)=>j.linkedDeferralId===(T.id||`${h.id}-defer-${y}`)?{...j,linkedDeferralId:""}:j),it(N,T,"deferral",L),C.splice(y,1);const V=$.findIndex(j=>j.deferralId===y&&j.type==="deferral");V!==-1&&$.splice(V,1),N.update(S,{deferrals:C,actions:k,history:$,updatedAt:new Date().toISOString()}),await N.commit(),o(C),D(k),I($),alert("تم شطب التأجيلة ونقلها إلى الأرشيف بنجاح")}}catch(S){alert("خطأ في شطب التأجيلة: "+S.message)}},Ze=["عاجل","متوسط","غير عاجل"],ai=({currentUser:s})=>{ht();const{caseNumber:a}=st(),o=decodeURIComponent(a||""),[i,I]=v.useState(null),[h,m]=v.useState([]),[D,y]=v.useState([]),[r,p]=v.useState([]),[O,L]=v.useState([]),[S,N]=v.useState([]),[M,b]=v.useState(null),[C,k]=v.useState(null),[$,T]=v.useState(null),[V,j]=v.useState(null),[l,w]=v.useState(null),[u,f]=v.useState(!0),[_,R]=v.useState(!1),[Y,A]=v.useState(null),[$e,De]=v.useState(null),[re,ye]=v.useState(!1),He=async()=>{if(!s||!o){A("المستخدم أو رقم القضية غير متوفر. يرجى تسجيل الدخول أو التحقق من رقم القضية."),f(!1);return}f(!0),A(null),De(null);try{const Q=await at(o);if(Q){I(Q);const z=ie(K,"cases",o),ne=ft(z,async J=>{if(!J.exists()){A("القضية غير موجودة."),f(!1);return}const X={id:J.id,...J.data()};let n=(X.deferrals||[]).map((B,ee)=>({...B,id:B.id||`${o}-defer-${ee}`,date:B.date||new Date().toISOString().split("T")[0],createdAt:B.createdAt||new Date().toISOString()})),F=(X.actions||[]).map((B,ee)=>({...B,id:B.id||`${o}-action-${ee}`,createdAt:B.createdAt||new Date().toISOString()}));const P=X.history||[],E=new Date,se=n.filter(B=>new Date(B.date)<E&&!B.isDeleted&&!B.isArchived);se.forEach(B=>{Xe(B.index,n,m,P,N,i,F,y)}),n=n.filter(B=>!se.includes(B));const pe=F.filter(B=>{const ee=new Date(B.deadline);if(B.linkedDeferralId){const c=n.find(G=>G.id===B.linkedDeferralId)||se.find(G=>G.id===B.linkedDeferralId);if(c)return new Date(c.date)<E&&!B.isDeleted&&!B.isArchived}return ee<E&&!B.isDeleted&&!B.isArchived});pe.forEach(B=>{Me(B.id,F,y,i,N)}),F=F.filter(B=>!pe.includes(B)),m(n),y(F),N(P)},J=>{A("خطأ في تحديث البيانات: "+J.message)});return()=>ne()}else A("لم يتم العثور على بيانات القضية أو ليس لديك إذن للوصول إليها.")}catch(Q){A("خطأ في جلب بيانات القضية: "+Q.message)}finally{f(!1)}};v.useEffect(()=>{He()},[o,s]);const be=async()=>{if(!s||!s.uid||!o){A("المستخدم غير مسجل الدخول أو رقم القضية غير متوفر.");return}R(!0),A(null),De(null);try{const Q=Ne(K,`cases/${o}/deferrals_archive`),z=Pe(Q,qe("userId","==",s.uid)),J=(await Ge(z)).docs.map(E=>({id:E.id,...E.data()})),X=Ne(K,`cases/${o}/actions_archive`),n=Pe(X,qe("userId","==",s.uid)),P=(await Ge(n)).docs.map(E=>({id:E.id,...E.data()}));p(J),L(P),ye(!0)}catch(Q){console.error("خطأ في جلب الأرشيف:",Q),A(`خطأ في جلب الأرشيف: ${Q.message}`)}finally{R(!1)}},ge=()=>{const Q=r.length>0||O.length>0;return e.jsxs(e.Fragment,{children:[_?e.jsxs("div",{style:{textAlign:"center",marginBottom:"15px"},children:[e.jsx("div",{style:{border:"4px solid rgba(0, 0, 0, 0.1)",borderLeft:"4px solid #000",borderRadius:"50%",width:"30px",height:"30px",animation:"spin 1s linear infinite",margin:"0 auto"}}),e.jsx("p",{children:"جاري تحميل البيانات المؤرشفة..."})]}):Q?e.jsx("div",{className:t.combinedContainer,children:me()}):e.jsx("div",{className:t.noReports,children:"لا توجد بيانات مؤرشفة متاحة حالياً"}),e.jsx("div",{className:t.loadMoreButtons,children:e.jsxs("button",{onClick:()=>ye(!1),className:t.singleBackButton,children:[e.jsx(We,{className:t.buttonIcon}),e.jsx("span",{children:"رجوع إلى السجل الحالي"})]})})]})},me=()=>{const Q=(re?[...h,...r]:h).map((n,F)=>{var se;const P=n.date?new Date(n.date):new Date,E=n.createdAt?n.createdAt:new Date().toISOString();return{type:"deferral",id:n.id||`${i.id}-defer-${F}`,content:n.content||(n.date&&n.reasons?`${n.date} - ${(se=n.reasons)==null?void 0:se.join("، ")}`:"تأجيل بدون تفاصيل"),isDeleted:n.isDeleted||!1,date:P,createdAt:E,index:F,isArchived:re&&F>=h.length,raw:n}}),z=(re?[...D,...O]:D).map((n,F)=>{const P=n.deadline?new Date(n.deadline):new Date,E=n.createdAt?n.createdAt:new Date().toISOString();return{type:"action",id:n.id,content:n,isDeleted:n.isDeleted||!1,date:P,createdAt:E,index:F,isArchived:re&&F>=D.length}}),ne=[],J=new Set,X=new Set;return z.forEach(n=>{if(n.isDeleted||X.has(n.id))return;const F={action:n,isLinked:!1};if(n.content.linkedDeferralId){const P=Q.find(E=>E.id===n.content.linkedDeferralId&&!E.isDeleted);P&&!J.has(P.id)&&(F.deferral=P,F.isLinked=!0,F.linkType="deferral",J.add(P.id))}else if(n.content.linkedActionId){const P=z.find(E=>E.id===n.content.linkedActionId&&!E.isDeleted&&!X.has(E.id));P&&(F.linkedAction=P,F.isLinked=!0,F.linkType="action",X.add(P.id))}ne.push(F),X.add(n.id)}),Q.forEach(n=>{!n.isDeleted&&!J.has(n.id)&&(ne.push({deferral:n,isLinked:!1}),J.add(n.id))}),z.forEach(n=>{!n.isDeleted&&!n.content.linkedDeferralId&&!n.content.linkedActionId&&!X.has(n.id)&&(ne.push({action:n,isLinked:!1}),X.add(n.id))}),ne.sort((n,F)=>{const P=n.action?n.action.date:n.deferral.date;return(F.action?F.action.date:F.deferral.date)-P}),ne.length===0?e.jsxs(e.Fragment,{children:[e.jsxs("h3",{className:t.sectionTitle,children:[e.jsx(Ae,{className:t.sectionIcon}),re?"الأرشيف":"سجل التقارير"]}),e.jsx("div",{className:t.noReports,children:"لا توجد تقارير أو إجراءات مسجلة..."})]}):e.jsxs(e.Fragment,{children:[e.jsxs("h3",{className:t.sectionTitle,children:[e.jsx(Ae,{className:t.sectionIcon}),re?"الأرشيف":"سجل التقارير"]}),ne.map((n,F)=>{var pe,B,ee;const P=n.deferral?S.filter(c=>c.deferralId===n.deferral.index&&c.type==="edit"):[],E=n.action?S.filter(c=>String(c.actionId)===String(n.action.id)&&c.type==="edit"):[],se=n.linkedAction?S.filter(c=>String(c.actionId)===String(n.linkedAction.id)&&c.type==="edit"):[];return e.jsx("div",{className:`${t.combinedItem} ${n.isLinked?t.linkedGroup:""}`,children:(n.action||n.deferral)&&e.jsxs("div",{className:t.mergedItem,children:[n.action&&(V===n.action.id?e.jsxs("div",{className:t.editForm,children:[e.jsxs("div",{className:t.actionField,children:[e.jsx("label",{children:"وصف الإجراء:"}),e.jsx("input",{type:"text",value:l.description,onChange:c=>w({...l,description:c.target.value}),className:t.actionInput})]}),e.jsxs("div",{className:t.actionField,children:[e.jsx("label",{children:"الموعد النهائي:"}),e.jsx("input",{type:"date",value:l.deadline,onChange:c=>{const G=c.target.value,ae=h.find(g=>g.id===l.linkedDeferralId),oe=D.find(g=>g.id===l.linkedActionId);let x;if(ae&&(x=new Date(ae.date),x.setDate(x.getDate()-1),new Date(G)>x)){A("موعد الإجراء يجب أن يكون قبل تاريخ التأجيل المرتبط.");return}if(oe){const g=new Date(oe.deadline);if(new Date(G)<=g){A("موعد الإجراء يجب أن يكون بعد تاريخ الإجراء المرتبط.");return}const H=h.find(q=>q.id===oe.linkedDeferralId);if(H&&(x=new Date(H.date),x.setDate(x.getDate()-1),new Date(G)>x)){A("موعد الإجراء يجب أن يكون قبل تاريخ التأجيل المرتبط بالإجراء المتسلسل.");return}}w({...l,deadline:G}),A(null)},className:t.actionInput,min:new Date().toISOString().split("T")[0]})]}),e.jsxs("div",{className:t.actionField,children:[e.jsx("label",{children:"الأولوية:"}),e.jsx("select",{value:l.priority,onChange:c=>w({...l,priority:c.target.value}),className:t.actionInput,children:Ze.map(c=>e.jsx("option",{value:c,children:c},c))})]}),e.jsxs("div",{className:t.actionField,children:[e.jsx("label",{children:"ربط بتأجيل:"}),e.jsxs("select",{value:l.linkedDeferralId,onChange:c=>w({...l,linkedDeferralId:c.target.value,linkedActionId:""}),className:t.actionInput,children:[e.jsx("option",{value:"",children:"لا ربط"}),Q.filter(c=>!c.isDeleted).map(c=>e.jsx("option",{value:c.id,children:c.content},c.id))]})]}),e.jsxs("div",{className:t.actionField,children:[e.jsx("label",{children:"ربط بإجراء:"}),e.jsxs("select",{value:l.linkedActionId,onChange:c=>w({...l,linkedActionId:c.target.value,linkedDeferralId:""}),className:t.actionInput,children:[e.jsx("option",{value:"",children:"لا ربط"}),z.filter(c=>!c.isDeleted&&c.id!==n.action.id).map(c=>e.jsx("option",{value:c.id,children:c.content.description},c.id))]})]}),e.jsxs("div",{className:t.actionFormButtons,children:[e.jsxs("button",{onClick:()=>ze(i,n.action.id,l.description,l.deadline,l.priority,l.linkedDeferralId,l.linkedActionId,D,y,j,w,S,N),className:t.addActionButton,disabled:Y,children:[e.jsx(ke,{className:t.buttonIcon}),e.jsx("span",{children:"حفظ"})]}),e.jsxs("button",{onClick:()=>{j(null),w(null),A(null)},className:t.cancelButton,children:[e.jsx(we,{className:t.buttonIcon}),e.jsx("span",{children:"إلغاء"})]})]})]}):e.jsxs("div",{className:`${t.actionItem} ${n.action.isDeleted?t.deletedAction:""}`,children:[e.jsxs("div",{className:t.actionContent,children:[e.jsxs("div",{className:t.actionDetails,children:[e.jsxs("div",{className:t.actionDescription,children:[n.action.isDeleted?e.jsx("s",{children:n.action.content.description}):n.action.content.description,n.isLinked&&e.jsxs("span",{className:t.linkIndicator,children:[e.jsx(Fe,{className:t.linkIcon}),n.linkType==="deferral"?" (مرتبط بتأجيل)":" (مرتبط بإجراء آخر)"]}),n.action.isArchived&&e.jsx("span",{className:t.archivedIndicator,children:"(مؤرشف)"})]}),e.jsx("div",{className:t.actionMeta,children:e.jsx("span",{className:t.actionDeadline,children:n.action.isDeleted?e.jsxs("s",{children:["الموعد النهائي: ",n.action.content.deadline]}):`الموعد النهائي: ${n.action.content.deadline}`})}),e.jsx("div",{className:t.actionMeta,children:e.jsx("span",{className:`${t.actionPriority} ${t[n.action.content.priority.toLowerCase()]}`,children:n.action.isDeleted?e.jsxs("s",{children:["الأولوية: ",n.action.content.priority]}):`الأولوية: ${n.action.content.priority}`})}),e.jsx("div",{className:t.actionMeta,children:e.jsxs("span",{children:["تاريخ التدوين: ",new Date(n.action.createdAt).toLocaleString()]})}),n.action.content.isDeleted&&n.action.content.struckAt&&e.jsx("div",{className:t.actionMeta,children:e.jsxs("span",{className:t.deletionTimestamp,children:["شُطب في: ",n.action.content.struckAt]})})]}),!n.action.isDeleted&&!n.action.isArchived&&e.jsxs("div",{className:t.reportActions,children:[e.jsx("button",{onClick:()=>Ue(n.action.id,D,j,w),className:t.editButton,title:"تعديل",children:e.jsx(Ee,{className:t.actionIcon})}),e.jsx("button",{onClick:()=>Me(n.action.id,D,y,i,N),className:t.strikeActionButton,title:"شطب",children:e.jsx(Oe,{className:t.actionIcon})}),E.length>0&&e.jsx("button",{onClick:()=>b(M===n.action.id?null:n.action.id),className:t.historyButton,title:"سجل التعديلات",children:e.jsx(Ae,{className:t.historyIcon})})]})]}),M===((pe=n.action)==null?void 0:pe.id)&&E.length>0&&e.jsx("div",{className:t.historyDropdown,children:e.jsx("ul",{className:t.historyList,children:E.map((c,G)=>e.jsxs("li",{className:t.historyItem,children:[e.jsx("span",{className:t.historyAction,children:c.action}),e.jsx("span",{className:t.historyTimestamp,children:new Date(c.timestamp).toLocaleString()})]},G))})})]})),n.deferral&&(C===n.deferral.index?e.jsxs("div",{className:t.editForm,children:[e.jsxs("div",{className:t.actionField,children:[e.jsx("label",{children:"تاريخ التأجيل:"}),e.jsx("input",{type:"date",value:$.date,onChange:c=>T({...$,date:c.target.value}),className:t.actionInput,min:new Date().toISOString().split("T")[0]})]}),e.jsxs("div",{className:t.actionField,children:[e.jsx("label",{children:"أسباب التأجيل:"}),e.jsx("input",{type:"text",value:$.reasons.join("، "),onChange:c=>T({...$,reasons:c.target.value.split("، ")}),className:t.actionInput,placeholder:"أدخل الأسباب مفصولة بفواصل"})]}),e.jsxs("div",{className:t.actionFormButtons,children:[e.jsxs("button",{onClick:()=>ii(i,C,$.date,$.reasons,h,m,k,T,S,N),className:t.addActionButton,children:[e.jsx(ke,{className:t.buttonIcon}),e.jsx("span",{children:"حفظ"})]}),e.jsxs("button",{onClick:()=>{k(null),T(null)},className:t.cancelButton,children:[e.jsx(we,{className:t.buttonIcon}),e.jsx("span",{children:"إلغاء"})]})]})]}):e.jsxs("div",{className:`${t.reportItem} ${n.deferral.isDeleted?t.deleted:""}`,children:[e.jsxs("div",{className:t.reportContent,children:[e.jsxs("div",{className:t.reportText,children:[n.deferral.isDeleted?e.jsx("s",{children:n.deferral.content}):n.deferral.content,n.isLinked&&e.jsxs("span",{className:t.linkIndicator,children:[e.jsx(Fe,{className:t.linkIcon})," (مرتبط بإجراء)"]}),n.deferral.isArchived&&e.jsx("span",{className:t.archivedIndicator,children:"(مؤرشف)"})]}),e.jsx("div",{className:t.actionMeta,children:e.jsxs("span",{children:["تاريخ التدوين: ",new Date(n.deferral.createdAt).toLocaleString()]})}),!n.deferral.isDeleted&&!n.deferral.isArchived&&e.jsxs("div",{className:t.reportActions,children:[e.jsx("button",{onClick:()=>si(n.deferral.index,h,k,T),className:t.editButton,title:"تعديل",children:e.jsx(Ee,{className:t.actionIcon})}),e.jsx("button",{onClick:()=>Xe(n.deferral.index,h,m,S,N,i,D,y),className:t.deleteButton,title:"شطب",children:e.jsx(Oe,{className:t.actionIcon})}),P.length>0&&e.jsx("button",{onClick:()=>b(M===n.deferral.index?null:n.deferral.index),className:t.historyButton,title:"سجل التعديلات",children:e.jsx(Ae,{className:t.historyIcon})})]})]}),M===((B=n.deferral)==null?void 0:B.index)&&P.length>0&&e.jsx("div",{className:t.historyDropdown,children:e.jsx("ul",{className:t.historyList,children:P.map((c,G)=>e.jsxs("li",{className:t.historyItem,children:[e.jsx("span",{className:t.historyAction,children:c.action}),e.jsx("span",{className:t.historyTimestamp,children:new Date(c.timestamp).toLocaleString()})]},G))})})]})),n.linkedAction&&(V===n.linkedAction.id?e.jsxs("div",{className:t.editForm,children:[e.jsxs("div",{className:t.actionField,children:[e.jsx("label",{children:"وصف الإجراء:"}),e.jsx("input",{type:"text",value:l.description,onChange:c=>w({...l,description:c.target.value}),className:t.actionInput})]}),e.jsxs("div",{className:t.actionField,children:[e.jsx("label",{children:"الموعد النهائي:"}),e.jsx("input",{type:"date",value:l.deadline,onChange:c=>{const G=c.target.value,ae=h.find(g=>g.id===l.linkedDeferralId),oe=D.find(g=>g.id===l.linkedActionId);let x;if(ae&&(x=new Date(ae.date),x.setDate(x.getDate()-1),new Date(G)>x)){A("موعد الإجراء يجب أن يكون قبل تاريخ التأجيل المرتبط.");return}if(oe){const g=new Date(oe.deadline);if(new Date(G)<=g){A("موعد الإجراء يجب أن يكون بعد تاريخ الإجراء المرتبط.");return}const H=h.find(q=>q.id===oe.linkedDeferralId);if(H&&(x=new Date(H.date),x.setDate(x.getDate()-1),new Date(G)>x)){A("موعد الإجراء يجب أن يكون قبل تاريخ التأجيل المرتبط بالإجراء المتسلسل.");return}}w({...l,deadline:G}),A(null)},className:t.actionInput,min:new Date().toISOString().split("T")[0]})]}),e.jsxs("div",{className:t.actionField,children:[e.jsx("label",{children:"الأولوية:"}),e.jsx("select",{value:l.priority,onChange:c=>w({...l,priority:c.target.value}),className:t.actionInput,children:Ze.map(c=>e.jsx("option",{value:c,children:c},c))})]}),e.jsxs("div",{className:t.actionField,children:[e.jsx("label",{children:"ربط بتأجيل:"}),e.jsxs("select",{value:l.linkedDeferralId,onChange:c=>w({...l,linkedDeferralId:c.target.value,linkedActionId:""}),className:t.actionInput,children:[e.jsx("option",{value:"",children:"لا ربط"}),Q.filter(c=>!c.isDeleted).map(c=>e.jsx("option",{value:c.id,children:c.content},c.id))]})]}),e.jsxs("div",{className:t.actionField,children:[e.jsx("label",{children:"ربط بإجراء:"}),e.jsxs("select",{value:l.linkedActionId,onChange:c=>w({...l,linkedActionId:c.target.value,linkedDeferralId:""}),className:t.actionInput,children:[e.jsx("option",{value:"",children:"لا ربط"}),z.filter(c=>!c.isDeleted&&c.id!==n.linkedAction.id).map(c=>e.jsx("option",{value:c.id,children:c.content.description},c.id))]})]}),e.jsxs("div",{className:t.actionFormButtons,children:[e.jsxs("button",{onClick:()=>ze(i,n.linkedAction.id,l.description,l.deadline,l.priority,l.linkedDeferralId,l.linkedActionId,D,y,j,w,S,N),className:t.addActionButton,disabled:Y,children:[e.jsx(ke,{className:t.buttonIcon}),e.jsx("span",{children:"حفظ"})]}),e.jsxs("button",{onClick:()=>{j(null),w(null),A(null)},className:t.cancelButton,children:[e.jsx(we,{className:t.buttonIcon}),e.jsx("span",{children:"إلغاء"})]})]})]}):e.jsxs("div",{className:`${t.actionItem} ${n.linkedAction.isDeleted?t.deletedAction:""}`,children:[e.jsxs("div",{className:t.actionContent,children:[e.jsxs("div",{className:t.actionDetails,children:[e.jsxs("div",{className:t.actionDescription,children:[n.linkedAction.isDeleted?e.jsx("s",{children:n.linkedAction.content.description}):n.linkedAction.content.description,e.jsxs("span",{className:t.linkIndicator,children:[e.jsx(Fe,{className:t.linkIcon})," (مرتبط بإجراء آخر)"]}),n.linkedAction.isArchived&&e.jsx("span",{className:t.archivedIndicator,children:"(مؤرشف)"})]}),e.jsx("div",{className:t.actionMeta,children:e.jsx("span",{className:t.actionDeadline,children:n.linkedAction.isDeleted?e.jsxs("s",{children:["الموعد النهائي: ",n.linkedAction.content.deadline]}):`الموعد النهائي: ${n.linkedAction.content.deadline}`})}),e.jsx("div",{className:t.actionMeta,children:e.jsx("span",{className:`${t.actionPriority} ${t[n.linkedAction.content.priority.toLowerCase()]}`,children:n.linkedAction.isDeleted?e.jsxs("s",{children:["الأولوية: $",n.linkedAction.content.priority]}):`الأولوية: ${n.linkedAction.content.priority}`})}),e.jsx("div",{className:t.actionMeta,children:e.jsxs("span",{children:["تاريخ التدوين: ",new Date(n.linkedAction.createdAt).toLocaleString()]})}),n.linkedAction.content.isDeleted&&n.linkedAction.content.struckAt&&e.jsx("div",{className:t.actionMeta,children:e.jsxs("span",{className:t.deletionTimestamp,children:["شُطب في: ",n.linkedAction.content.struckAt]})})]}),!n.linkedAction.isDeleted&&!n.linkedAction.isArchived&&e.jsxs("div",{className:t.reportActions,children:[e.jsx("button",{onClick:()=>Ue(n.linkedAction.id,D,j,w),className:t.editButton,title:"تعديل",children:e.jsx(Ee,{className:t.actionIcon})}),e.jsx("button",{onClick:()=>Me(n.linkedAction.id,D,y,i,N),className:t.strikeActionButton,title:"شطب",children:e.jsx(Oe,{className:t.actionIcon})}),se.length>0&&e.jsx("button",{onClick:()=>b(M===n.linkedAction.id?null:n.linkedAction.id),className:t.historyButton,title:"سجل التعديلات",children:e.jsx(Ae,{className:t.historyIcon})})]})]}),M===((ee=n.linkedAction)==null?void 0:ee.id)&&se.length>0&&e.jsx("div",{className:t.historyDropdown,children:e.jsx("ul",{className:t.historyList,children:se.map((c,G)=>e.jsxs("li",{className:t.historyItem,children:[e.jsx("span",{className:t.historyAction,children:c.action}),e.jsx("span",{className:t.historyTimestamp,children:new Date(c.timestamp).toLocaleString()})]},G))})})]}))]})},F)})]})};return u?e.jsx("div",{className:t.pageWrapper,children:e.jsx("div",{className:t.mainContainer,children:e.jsx("div",{className:t.mainContent,children:e.jsxs("div",{style:{textAlign:"center",padding:"20px"},children:[e.jsx("div",{style:{border:"4px solid rgba(0, 0, 0, 0.1)",borderLeft:"4px solid #000",borderRadius:"50%",width:"30px",height:"30px",animation:"spin 1s linear infinite",margin:"0 auto"}}),e.jsx("p",{children:"جاري تحميل بيانات القضية..."}),e.jsx("style",{children:`
                  @keyframes spin {
                    0% { transform: rotate(0deg); }
                    100% { transform: rotate(360deg); }
                  }
                `})]})})})}):Y||!i?e.jsx("div",{className:t.pageWrapper,children:e.jsx("div",{className:t.mainContainer,children:e.jsx("div",{className:t.mainContent,children:e.jsx("div",{style:{color:"red",textAlign:"center",padding:"20px"},children:Y||"لم يتم العثور على بيانات القضية. برجاء العودة واختيار قضية صحيحة."})})})}):e.jsx("div",{className:t.pageWrapper,children:e.jsx("div",{className:t.mainContainer,children:e.jsx("div",{className:t.combinedPane,children:re?e.jsx("div",{className:t.archiveSection,children:ge()}):e.jsxs("div",{className:t.combinedSection,children:[$e&&e.jsx("div",{style:{color:"green",marginBottom:"15px"},children:$e}),Y&&e.jsx("div",{style:{color:"red",marginBottom:"15px"},children:Y}),_&&e.jsxs("div",{style:{textAlign:"center",marginBottom:"15px"},children:[e.jsx("div",{style:{border:"4px solid rgba(0, 0, 0, 0.1)",borderLeft:"4px solid #000",borderRadius:"50%",width:"30px",height:"30px",animation:"spin 1s linear infinite",margin:"0 auto"}}),e.jsx("p",{children:"جاري تحميل البيانات المؤرشفة..."})]}),e.jsx("div",{className:t.combinedContainer,children:me()}),e.jsx("div",{className:t.loadMoreButtons,children:!re&&e.jsxs("button",{onClick:be,className:t.archiveButton,title:"عرض الأرشيف",children:[e.jsx(It,{className:t.buttonIcon}),e.jsx("span",{children:"عرض الأرشيف"})]})})]})})})})},oi={SEQUENTIAL:"الإجراءات مترابطة تسلسليًا مع التأجيلة"},_i=({currentUser:s})=>{const{caseNumber:a}=st(),o=a,[i,I]=v.useState(null),[h,m]=v.useState(!0),[D,y]=v.useState(null),[r,p]=v.useState([]),[O,L]=v.useState([]),[S,N]=v.useState([]),[M,b]=v.useState([]),[C,k]=v.useState(null),[$,T]=v.useState(null),[V,j]=v.useState(""),[l,w]=v.useState([]),[u,f]=v.useState(""),[_,R]=v.useState(""),[Y,A]=v.useState("متوسط"),[$e,De]=v.useState(""),[re,ye]=v.useState(""),[He,be]=v.useState(""),[ge,me]=v.useState(!1),[Q,z]=v.useState([]),ne=`case_${o}`,J=5*60*1e3,X=async()=>{if(s)try{const x=Ne(K,"deferralTemplates"),g=Pe(x,qe("userId","==",s.uid)),q=(await Ge(g)).docs.map(te=>({id:te.id,...te.data()}));b(q)}catch(x){console.error("خطأ في جلب القوالب:",x.message)}},n=async(x=!1)=>{if(!navigator.onLine){y("غير متصل بالإنترنت. سيتم تحميل البيانات عند استعادة الاتصال."),m(!1);return}if(!x){const g=localStorage.getItem(ne);if(g){const{data:H,timestamp:q}=JSON.parse(g);if(Date.now()-q<J){I(H),p(H.deferrals||[]),L(H.actions||[]),N(H.history||[]),m(!1);return}}}if(m(!0),y(null),!o){y("لم يتم تحديد معرف القضية في المسار."),m(!1);return}if(!s){y("يجب تسجيل الدخول لعرض تفاصيل القضايا."),m(!1);return}try{const g=await at(o);g?g.userId!==s.uid?(y("لا تمتلك صلاحية عرض تفاصيل هذه القضية."),I(null)):(I(g),p(g.deferrals||[]),L(g.actions||[]),N(g.history||[]),localStorage.setItem(ne,JSON.stringify({data:g,timestamp:Date.now()}))):(y("لم يتم العثور على بيانات القضية."),I(null))}catch(g){console.error("Error fetching case details:",g),g.code==="unavailable"?y("غير متصل بالإنترنت. سيتم تحميل البيانات عند استعادة الاتصال."):y("حدث خطأ أثناء جلب بيانات القضية: "+g.message),I(null)}finally{m(!1)}};v.useEffect(()=>{n(),X();const x=setInterval(()=>{navigator.onLine&&n(!0)},J);return()=>clearInterval(x)},[o,s]);const F=()=>{window.location.href="/dashboard"},P=()=>{n(!0)},E=()=>{k(null),T(null),j(""),w([]),f(""),R(""),A("متوسط"),De(""),ye(""),be(""),me(!1),z([])},se=(x,g)=>{if(!x||typeof x!="string")return console.warn("lastSavedReason غير صالح:",x),[];const H=x.trim().toLowerCase(),q=M.find(U=>U.reason&&typeof U.reason=="string"&&U.reason.trim().toLowerCase()===H);if(!q||!q.actions||q.actions.length===0)return console.warn("لم يتم العثور على قالب مطابق أو القالب فارغ:",q),[];const te=q.actions[0];return!te.linkage||!te.linkage.type?(console.warn("linkage أو type غير موجود في الإجراء الأول:",te),[]):q.actions.map((U,Se)=>{const ce=new Date(g);isNaN(ce.getTime())&&(ce=new Date),ce.setDate(ce.getDate()-(U.linkage.daysBefore||1));const ot=ce.toISOString().split("T")[0],ct=te.linkage.type===oi.SEQUENTIAL;let Ke="";return ct&&Se>0&&(Ke=`${o}-action-${Date.now()}-${Se-1}`),{description:U.description,deadline:ot,priority:"متوسط",linkedDeferralId:`${o}-defer-${r.length-1}`,linkType:"deferral",linkedActionId:Ke}})},pe=async(x,g,H)=>{try{if(!x||isNaN(new Date(x).getTime())||g.length===0){H("يرجى إدخال تاريخ التأجيلة واختيار سبب واحد على الأقل");return}const q=[...g];await ti(i,x,g,$,r,p,()=>j(""),()=>w([]),E,T,S,N,()=>{if(q.length===1){const U=se(q[0],x);U.length>0&&(z(U),me(!0))}})}catch(q){console.error("Error in handleSaveDeferral:",q),H(q.message)}},B=async()=>{try{for(const x of Q)await ee(x.description,x.deadline,x.priority,x.linkType,x.linkedDeferralId,x.linkedActionId,()=>{});alert("تم إضافة الإجراءات الافتراضية بنجاح.")}catch(x){console.error("خطأ أثناء إضافة الإجراءات الافتراضية:",x),alert("حدث خطأ أثناء إضافة الإجراءات الافتراضية: "+x.message)}finally{me(!1),z([])}},ee=async(x,g,H,q,te,U,Se)=>{try{const ce={...i,deferrals:r};await ni(x,g,H,q,te,U,ce,O,L,()=>f(""),()=>R(""),()=>A("متوسط"),()=>De(""),()=>ye(""),()=>be(""),E,N)}catch(ce){console.error("Error in handleSaveAction:",ce),Se(ce.message)}};if(h)return e.jsxs("div",{className:d.pageWrapper,children:[e.jsx(Le,{currentUser:s,casesList:[]}),e.jsx("div",{className:d.mainContainer,children:e.jsx("div",{className:d.mainContent,children:e.jsx("div",{children:"جاري تحميل تفاصيل القضية..."})})})]});if(D)return e.jsxs("div",{className:d.pageWrapper,children:[e.jsx(Le,{currentUser:s,casesList:[]}),e.jsx("div",{className:d.mainContainer,children:e.jsxs("div",{className:d.mainContent,children:[e.jsxs("button",{onClick:F,className:d.backButton,children:[e.jsx(We,{className:d.buttonIcon}),e.jsx("span",{children:"العودة"})]}),e.jsx("div",{className:d.errorMessage,children:D}),e.jsxs("button",{onClick:P,className:d.retryButton,children:[e.jsx(jt,{className:d.buttonIcon}),e.jsx("span",{children:"إعادة المحاولة"})]})]})})]});let c="تفاصيل القضية",G=!0,ae=d.caseTitlePending;i.caseStatus==="قيد النظر"?(c="التفاصيل",G=!1,ae=d.caseTitlePending):i.caseStatus==="محضر"?(c="تفاصيل المحضر",G=!1,ae=d.caseTitleReport):i.caseStatus==="دعوى قضائية"&&(c="تفاصيل الدعوى",ae=d.caseTitleLawsuit);const oe=(i==null?void 0:i.caseStatus)==="قيد النظر";return e.jsxs("div",{className:d.pageWrapper,children:[e.jsx(Le,{currentUser:s,casesList:[]}),e.jsx("div",{className:d.mainContainer,children:e.jsxs("div",{className:d.mainContent,children:[e.jsxs("h2",{className:`${d.caseTitle} ${ae}`,children:[c,G&&i.fullCaseNumber&&i.fullCaseNumber!=="—"?` - ${i.fullCaseNumber}`:""]}),e.jsx(Tn,{caseData:i,currentUser:s}),e.jsx(ai,{currentUser:s,actions:O,deferrals:r,history:S}),e.jsxs("div",{className:d.actionsSection,children:[!C&&!ge&&e.jsxs("div",{className:d.addOptions,children:[!oe&&e.jsxs("button",{onClick:()=>k("deferral"),className:d.addDeferralButton,children:[e.jsx(Ye,{className:d.buttonIcon}),e.jsx("span",{children:"إضافة تأجيلة"})]}),e.jsxs("button",{onClick:()=>k("action"),className:d.addActionOptionButton,children:[e.jsx(Ye,{className:d.buttonIcon}),e.jsx("span",{children:"إضافة إجراء"})]})]}),C==="deferral"&&e.jsx(kt,{currentUser:s,caseItem:i,deferrals:r,setDeferrals:p,history:S,setHistory:N,onSave:pe,onCancel:E,onAddActions:ee,isUnderConsideration:oe}),C==="action"&&e.jsx(wt,{currentUser:s,caseItem:i,deferrals:r,actions:O,setActions:L,history:S,setHistory:N,onSave:ee,onCancel:E}),ge&&e.jsxs("div",{className:d.promptDialog,children:[e.jsx("h3",{children:"هل تريد إضافة إجراءات افتراضية؟"}),e.jsxs("div",{className:d.previewDialog,children:[e.jsx("h4",{children:"الإجراءات الافتراضية:"}),e.jsx("ul",{children:Q.map((x,g)=>{var H,q,te;return e.jsxs("li",{className:d.previewAction,children:[e.jsx("strong",{children:"الإجراء:"})," ",x.description," ",e.jsx("br",{}),e.jsx("strong",{children:"الموعد النهائي:"})," ",x.deadline," ",e.jsx("br",{}),e.jsx("strong",{children:"الأولوية:"})," ",x.priority," ",e.jsx("br",{}),g===0&&e.jsxs(e.Fragment,{children:[e.jsx("strong",{children:"نظام الارتباط:"})," ",((te=(q=(H=M.find(U=>U.reason&&typeof U.reason=="string"&&U.reason.trim().toLowerCase()===(l[0]?l[0].trim().toLowerCase():"")))==null?void 0:H.actions[0])==null?void 0:q.linkage)==null?void 0:te.type)||"غير محدد"," ",e.jsx("br",{})]})]},g)})})]}),e.jsxs("div",{className:d.promptButtons,children:[e.jsxs("button",{onClick:B,className:d.confirmButton,children:[e.jsx(vt,{className:d.buttonIcon}),e.jsx("span",{children:"نعم"})]}),e.jsxs("button",{onClick:()=>{me(!1),z([])},className:d.cancelButton,children:[e.jsx(Dt,{className:d.buttonIcon}),e.jsx("span",{children:"لا"})]})]})]})]}),e.jsx("div",{className:d.buttonsSection,children:e.jsxs("button",{onClick:F,className:d.backButton,children:[e.jsx(We,{className:d.buttonIcon}),e.jsx("span",{children:"العودة إلى لوحة التحكم"})]})}),"Vite"]})})]})};export{_i as default};
