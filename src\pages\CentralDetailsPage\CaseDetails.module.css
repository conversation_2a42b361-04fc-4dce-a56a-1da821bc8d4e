/* متغيرات CSS مخصصة - ثيم أزرق مهني */
@import '../../styles/variables.css';


/* التخطيط العام - تم إزالة الكلاسات غير المستخدمة */

/* حاوية البطاقات - تخطيط أفقي للشاشات الكبيرة (4 أعمدة) */
.coloredCardsContainerHorizontal {
  display: grid;
  grid-template-columns: 1fr; /* عمود واحد افتراضي للهواتف */
  gap: 20px;
  width: 100%;
}

/* تطبيق 4 أعمدة للشاشات الكبيرة (مثل الكمبيوتر) */
@media (min-width: 1200px) {
  .coloredCardsContainerHorizontal {
    grid-template-columns: repeat(4, 1fr);
  }
}

/* تطبيق عمودين للشاشات المتوسطة (مثل التابلت) */
@media (min-width: 769px) and (max-width: 1199px) {
  .coloredCardsContainerHorizontal {
    grid-template-columns: repeat(2, 1fr);
  }
}

/* البطاقات الملونة - مستخدمة في CaseArchiveView */
.coloredCard {
  background: var(--page-background);
  border-radius: 20px;
  box-shadow: 
    0 20px 60px var(--shadow-medium),
    0 10px 30px var(--shadow-light);
  overflow: hidden;
  transition: all var(--transition-normal);
  border: none;
  display: flex;
  flex-direction: column;
  min-height: 200px;
  position: relative;
  min-width: 0;
  word-wrap: break-word;
}

/* كارت بسيط بأسلوب Google Material Card */
.simpleCard {
  background: var(--page-background);
  border-radius: 20px;
  box-shadow: 
    0 20px 60px var(--shadow-medium),
    0 10px 30px var(--shadow-light);
  border: none;
  padding: 18px 14px;
  min-height: 120px;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  transition: all var(--transition-normal);
  overflow: hidden;
}
.simpleCard:hover {
  box-shadow: 
    0 25px 70px var(--shadow-medium),
    0 15px 40px var(--shadow-light);
  transform: translateY(-4px);
}

.simpleCardHeader {
  font-size: 1.08rem;
  font-weight: 600;
  color: #222;
  background: transparent;
  padding-bottom: var(--padding-card); /* المسافة بين العنوان والخط */
  border-bottom: 1px solid #eee;
  margin-bottom: 18px;
  letter-spacing: 0.01em;

}

/* تأثيرات التحويم للبطاقات */
.coloredCard:hover {
  transform: translateY(-4px);
  box-shadow: 
    0 25px 70px var(--shadow-medium),
    0 15px 40px var(--shadow-light);
}

.partiesCard:hover {
  box-shadow: 
    0 25px 70px var(--shadow-medium),
    0 15px 40px var(--shadow-light);
  transform: translateY(-4px);
}

.identificationCard:hover {
  box-shadow: 
    0 25px 70px var(--shadow-medium),
    0 15px 40px var(--shadow-light);
  transform: translateY(-4px);
}

.locationCard:hover {
  box-shadow: 
    0 25px 70px var(--shadow-medium),
    0 15px 40px var(--shadow-light);
  transform: translateY(-4px);
}

/* ألوان البطاقات - ثيم جديد مع تدرجات */
.partiesCard {
  border: none;
  background: var(--page-background);
  position: relative;
  overflow: hidden;
}

.partiesCard::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--parties-color), var(--parties-dark));
}

.identificationCard {
  border: none;
  background: var(--page-background);
  position: relative;
  overflow: hidden;
}

.identificationCard::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--identification-color), var(--identification-dark));
}

.locationCard {
  border: none;
  background: var(--page-background);
  position: relative;
  overflow: hidden;
}

.locationCard::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--location-color), var(--neutral-200));
}

/* إضافة لمعة خفيفة للبطاقات */
.partiesCard::before,
.identificationCard::before,
.locationCard::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: linear-gradient(
    45deg,
    transparent,
    rgba(255, 255, 255, 0.2),
    transparent
  );
  transform: rotate(45deg);
  transition: var(--transition);
  opacity: 0;
  pointer-events: none;
}

.partiesCard:hover::before,
.identificationCard:hover::before,
.locationCard:hover::before {
  opacity: 1;
  animation: shimmer 1.5s ease-in-out;
}

/* البطاقات المستخدمة في Archive */
.timelineCard {
  border: none;
  background: var(--page-background);
}

/* رأس البطاقة */
.cardHeader {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 5px;
  cursor: pointer;
  transition: var(--transition);
  border-bottom: none;
  flex-shrink: 0;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-radius: 0px 0px 0 0;
}

.cardHeaderInSimple {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 15px 20px;
  background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
  color: white;
  border-radius: 20px 20px 0 0;
  margin-bottom: 0;
}

.partiesCard .cardHeader {
  background: linear-gradient(135deg, var(--parties-color), var(--parties-dark));
  color: white;
  box-shadow: 0 4px 15px rgba(26, 54, 93, 0.4);
}

.identificationCard .cardHeader {
  background: linear-gradient(135deg, var(--identification-color), var(--identification-dark));
  color: white;
  box-shadow: 0 4px 15px rgba(116, 66, 16, 0.4);
}

.locationCard .cardHeader {
  background: linear-gradient(135deg, var(--location-color), var(--location-dark));
  color: white;
  box-shadow: 0 4px 15px rgba(26, 32, 44, 0.4);
}

.timelineCard .cardHeader {
  background: var(--timeline-color);
  color: var(--neutral-800);
}

.cardHeaderContent {
  display: flex;
  align-items: center;
  gap: 12px;
}

.cardTitle {
  font-weight: 600;
  font-size: 1.2rem;
  color: black;
}

.cardHeaderInSimple .cardTitle {
  color: black !important;
}

/* محتوى البطاقة الأساسي والموسع */
.cardPrimaryContent,
.cardExpandedContent {
  padding: 15px;
  flex-grow: 1; /* لجعل المحتوى يملأ المساحة المتاحة */
  overflow-y: auto; /* إضافة تمرير إذا لزم الأمر */
  min-width: 0; /* السماح بالانكماش عند الحاجة */
  word-wrap: break-word; /* كسر الكلمات الطويلة */
  overflow-wrap: break-word; /* تحسين كسر الكلمات */
}

.cardExpandedContent {
  background: var(--neutral-50);
  max-height: 0;
  opacity: 0;
  overflow: hidden;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  transform: translateY(-10px);
}

.cardExpandedContent.expanded {
  max-height: 500px; /* ارتفاع كافي للمحتوى */
  opacity: 1;
  transform: translateY(0);
}

.expandedContentDivider {
  height: 1px;
  background: var(--neutral-200);
  margin-bottom: 15px;
  opacity: 0;
  transition: opacity 0.3s ease 0.1s;
}

.cardExpandedContent.expanded .expandedContentDivider {
  opacity: 1;
}

/* حقول البيانات - تخطيط أفقي */
.dataFieldHorizontal {
  display: flex;
  align-items: flex-start; /* محاذاة للبداية */
  justify-content: space-between;
  margin-bottom: 12px;
  padding-bottom: 12px;
  border-bottom: 1px dashed var(--neutral-200);
  gap: 10px; /* مسافة بين العنوان والقيمة */
  min-width: 0; /* السماح بالانكماش عند الحاجة */
  overflow: hidden; /* منع تجاوز المحتوى */
}

/* انيميشن فقط للحقول في المحتوى الموسع */
.cardExpandedContent .dataFieldHorizontal {
  opacity: 0;
  transform: translateX(-20px);
  transition: var(--transition-all);
}

.cardExpandedContent.expanded .dataFieldHorizontal {
  opacity: 1;
  transform: translateX(0);
}

/* تأخير متدرج للحقول في المحتوى الموسع */
.cardExpandedContent.expanded .dataFieldHorizontal:nth-child(2) { transition-delay: 0.1s; }
.cardExpandedContent.expanded .dataFieldHorizontal:nth-child(3) { transition-delay: 0.2s; }
.cardExpandedContent.expanded .dataFieldHorizontal:nth-child(4) { transition-delay: 0.3s; }
.cardExpandedContent.expanded .dataFieldHorizontal:nth-child(5) { transition-delay: 0.4s; }

/* الحقول في المحتوى الأساسي تبقى مرئية دائماً */
.cardPrimaryContent .dataFieldHorizontal {
  opacity: 1;
  transform: translateX(0);
}

.dataFieldHorizontal:last-child {
  margin-bottom: 0;
  padding-bottom: 0;
  border-bottom: none;
}

.fieldLabelHorizontal {
  font-size: 1.1rem;
  color: var(--neutral-600);
  white-space: nowrap; /* منع التفاف العنوان */
  flex-shrink: 0; /* منع العنوان من الانكماش */
  font-weight: 500;
}

.fieldValueContainerHorizontal {
  flex-grow: 1; /* جعل حاوية القيمة تأخذ المساحة المتبقية */
  text-align: right; /* محاذاة القيمة لليمين للنصوص العربية */
  min-width: 0; /* السماح للحاوية بالانكماش عند الحاجة */
  overflow: hidden; /* منع تجاوز المحتوى للحاوية */
}

.valueWithActionHorizontal {
  display: flex;
  justify-content: space-between;
  align-items: flex-start; /* تغيير المحاذاة لتجنب مشاكل النصوص الطويلة */
  width: 100%;
  gap: 8px; /* مسافة بين القيمة وزر التعديل */
  min-width: 0; /* السماح بالانكماش عند الحاجة */
}

.valueTextHorizontal {
  color: var(--neutral-800);
  font-size: 1.15rem;
  font-weight: 600;
  word-break: break-word; /* السماح بكسر الكلمات الطويلة */
  overflow-wrap: break-word; /* تحسين كسر الكلمات */
  hyphens: auto; /* إضافة شرطات عند كسر الكلمات */
  line-height: 1.4; /* تحسين المسافة بين الأسطر */
  white-space: normal; /* السماح بالتفاف النص بشكل طبيعي */
  max-width: 100%; /* منع تجاوز العرض المتاح */
}

/* النصوص الطويلة - عرض مختصر في المحتوى الأساسي */
.longTextPreview {
  color: var(--neutral-800);
  font-size: 1.15rem;
  font-weight: 600;
  word-break: break-word;
  overflow-wrap: break-word; /* تحسين كسر الكلمات */
  hyphens: auto; /* إضافة شرطات عند كسر الكلمات */
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2; /* عرض سطرين فقط */
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  max-height: 2.8em; /* ارتفاع سطرين تقريباً */
  white-space: normal; /* السماح بالتفاف النص بشكل طبيعي */
  max-width: 100%; /* منع تجاوز العرض المتاح */
}

/* النصوص الطويلة - عرض كامل في المحتوى الموسع */
.longTextFull {
  color: var(--neutral-800);
  font-size: 1.1rem;
  font-weight: 500;
  word-break: break-word;
  overflow-wrap: break-word; /* تحسين كسر الكلمات */
  hyphens: auto; /* إضافة شرطات عند كسر الكلمات */
  line-height: 1.5;
  white-space: pre-wrap; /* الحفاظ على فواصل الأسطر */
  margin-top: 8px;
  max-width: 100%; /* منع تجاوز العرض المتاح */
}

/* مؤشر وجود نص إضافي */
.moreTextIndicator {
  color: var(--primary-color);
  font-size: 0.9rem;
  font-weight: 500;
  margin-top: 4px;
  font-style: italic;
  opacity: 0.8;
}

/* حاوية النص الطويل */
.longTextContainer {
  width: 100%;
  min-width: 0; /* السماح بالانكماش عند الحاجة */
  overflow: hidden; /* منع تجاوز المحتوى */
  word-wrap: break-word; /* كسر الكلمات الطويلة */
}

/* زر التوسع */
.expandToggle {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 12px 16px;
  margin-top: 16px;
  background: var(--neutral-100);
  border: 1px solid var(--neutral-200);
  border-radius: 12px;
  cursor: pointer;
  transition: all var(--transition-normal);
  font-size: 0.95rem;
  font-weight: 500;
  color: var(--neutral-700);
  user-select: none;
}

/* قاعدة عامة لضمان عرض النصوص بشكل صحيح في جميع الكروت */
.coloredCard *,
.simpleCard * {
  word-wrap: break-word;
  overflow-wrap: break-word;
  hyphens: auto;
  max-width: 100%;
}

/* تحسين خاص للنصوص العربية */
.coloredCard,
.simpleCard {
  direction: rtl;
  text-align: right;
}

.coloredCard *[dir="ltr"],
.simpleCard *[dir="ltr"] {
  direction: ltr;
  text-align: left;
}

.expandToggle:hover {
  background: var(--neutral-200);
  border-color: var(--primary-color);
  color: var(--primary-color);
  transform: translateY(-1px);
}

/* تحسينات خاصة للشاشات الكبيرة */
@media (min-width: 1200px) {
  /* جعل حقول محددة تعرض القيمة تحت العنوان في الشاشات الكبيرة فقط */
  .dataFieldHorizontal[data-field="caseDescription"],
  .dataFieldHorizontal[data-field="courtLocation"] {
    flex-direction: column !important;
    align-items: flex-start !important;
    gap: 8px !important;
  }
  
  .dataFieldHorizontal[data-field="caseDescription"] .fieldLabelHorizontal,
  .dataFieldHorizontal[data-field="courtLocation"] .fieldLabelHorizontal {
    white-space: normal !important;
    margin-bottom: 4px !important;
    font-weight: 600 !important;
  }
  
  .dataFieldHorizontal[data-field="caseDescription"] .fieldValueContainerHorizontal,
  .dataFieldHorizontal[data-field="courtLocation"] .fieldValueContainerHorizontal {
    width: 100% !important;
  }
}

/* مدخلات الأرشيف الزمني */
.timelineScrollContainer {
  max-height: 300px; /* تحديد ارتفاع أقصى للأرشيف */
  overflow-y: auto; /* إضافة تمرير عمودي إذا تجاوز المحتوى الارتفاع */
  padding-right: 5px; /* مسافة صغيرة لشريط التمرير */
}

.timelineEntry {
  display: flex;
  flex-direction: column; /* تاريخ فوق الوصف */
  margin-bottom: 10px;
  padding-bottom: 10px;
  border-bottom: 1px dashed var(--neutral-200);
}

.timelineEntry:last-child {
  margin-bottom: 0;
  padding-bottom: 0;
  border-bottom: none;
}

.timelineDate {
  font-size: 1rem;
  color: var(--neutral-600);
  margin-bottom: 4px;
  font-weight: 600;
}

.timelineDescription {
  font-size: 1.1rem;
  color: var(--neutral-800);
  word-break: break-word; /* كسر الكلمات الطويلة في الوصف */
  font-weight: 500;
}

/* رسالة عدم وجود بيانات في الأرشيف */
.noTimeline {
  text-align: center;
  color: var(--neutral-500);
  font-size: 0.9rem;
  padding: 20px 0;
}

/* قيم منطقية */
.booleanValue {
  display: inline-flex; /* تغيير لـ inline-flex */
  align-items: center;
  gap: 6px;
  padding: 3px 8px;
  border-radius: var(--border-radius-sm);
  font-size: 0.9rem;
  font-weight: 500;
}

.trueValue {
  background: rgba(127, 182, 133, 0.2);
  color: #2e7d32;
}

.falseValue {
  background: rgba(226, 143, 131, 0.2);
  color: #c62828;
}

/* زر التعديل الأفقي */
.editIconButtonHorizontal {
  padding: 6px 12px; /* padding مناسب للنص */
  border: none;
  border-radius: 8px; /* حواف مدورة ناعمة بدلاً من دائرية */
  background: var(--neutral-200);
  color: var(--neutral-600);
  cursor: pointer;
  transition: var(--transition);
  display: inline-flex;
  align-items: center;
  justify-content: center;
  opacity: 0.7;
  flex-shrink: 0;
  box-sizing: border-box;
  font-size: 14px;
  line-height: 1;
  height: auto; /* ارتفاع تلقائي حسب المحتوى */
  width: auto; /* عرض تلقائي حسب المحتوى */
}


.editIconButtonHorizontal:hover {
  background: var(--primary-dark);
  color: white;
  opacity: 1;
}

/* زر الإحالة الأفقي */
.referralButtonHorizontal {
  padding: 6px 12px; /* padding مناسب ومتسق مع زر التعديل */
  border: none;
  border-radius: 8px; /* حواف مدورة ناعمة */
  background: var(--neutral-200);
  color: var(--neutral-600);;
  cursor: pointer;
  transition: var(--transition);
  display: inline-flex;
  align-items: center;
  justify-content: center;
  opacity: 0.8;
  box-sizing: border-box;
  margin-left: 4px;
  font-size: 14px;
  line-height: 1;
  height: auto; /* ارتفاع تلقائي حسب المحتوى */
  width: auto; /* عرض تلقائي حسب المحتوى */
}

.referralButtonHorizontal:hover {
  background: var(--success-dark);
  opacity: 1;
  transform: translateY(-1px);
}

/* تم إزالة الأزرار والمكونات غير المستخدمة */

/* تم إزالة المزيد من المكونات غير المستخدمة */

/* زر التوسيع */
.expandToggle {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 10px;
  background: var(--neutral-100);
  color: var(--neutral-600);
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border-top: 1px solid var(--neutral-200);
  margin-top: auto; /* دفع الزر لأسفل البطاقة */
  flex-shrink: 0; /* منع الزر من الانكماش */
  position: relative;
  overflow: hidden;
}

.expandToggle:hover {
  background: var(--neutral-200);
  color: var(--neutral-800);
  transform: translateY(-1px);
  box-shadow: var(--box-shadow-md);
}

.expandToggle:active {
  transform: translateY(0);
  transition: all 0.1s ease;
}

/* انيميشن الأيقونة */
.expandToggle svg {
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.expandToggle:hover svg {
  transform: scale(1.1);
}

/* أنماط خاصة لحقول رقم القضية الكامل */
.fullCaseNumberEditContainer {
  width: 100%;
}

.caseNumberFieldsContainer {
  display: flex;
  flex-direction: row; /* جعل الحقول جنب بعض */
  gap: 15px;
  margin-bottom: 15px;
  padding: 15px;
  background: var(--neutral-50);
  border-radius: var(--border-radius);
  border: 2px solid var(--primary-light);
}

/* للشاشات الصغيرة، اجعل الحقول فوق بعض */
@media (max-width: 768px) {
  .caseNumberFieldsContainer {
    flex-direction: column;
    gap: 12px;
  }
}

.caseNumberField {
  display: flex;
  flex-direction: column;
  gap: 6px;
  flex: 1; /* جعل كل حقل يأخذ نفس المساحة */
}

.inlineLabel {
  font-size: 0.9rem;
  color: var(--neutral-700);
  font-weight: 500;
  margin-bottom: 4px;
}

/* تحسين أنماط حقول الإدخال */
.editInput {
  padding: 10px 12px;
  border: 2px solid var(--neutral-300);
  border-radius: var(--border-radius);
  font-size: 0.95rem;
  transition: var(--transition);
  background: white;
  color: var(--neutral-800);
}

.editInput:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(76, 104, 192, 0.1);
}

.editInput.inputError {
  border-color: var(--danger-color);
  box-shadow: 0 0 0 3px rgba(42, 46, 112, 0.1);
}

/* أنماط أزرار التعديل */
.editActionsHorizontal {
  display: flex;
  gap: 8px;
  margin-top: 12px;
  justify-content: flex-end;
}

.saveEditButton,
.cancelEditButton {
  padding: 8px 16px;
  border: none;
  border-radius: var(--border-radius);
  font-size: 0.9rem;
  cursor: pointer;
  transition: var(--transition);
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 80px;
}

.saveEditButton {
  background: var(--primary-color);
  color: white;
}

.saveEditButton:hover:not(:disabled) {
  background: var(--primary-dark);
  transform: translateY(-1px);
}

.saveEditButton:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.cancelEditButton {
  background: var(--neutral-400);
  color: white;
}

.cancelEditButton:hover:not(:disabled) {
  background: var(--neutral-600);
  transform: translateY(-1px);
}

/* رسائل الخطأ */
.errorText {
  color: var(--danger-color);
  font-size: 0.85rem;
  margin-top: 6px;
  display: block;
  font-weight: 500;
}

/* أنماط مكون تحويل الدرجة */
.transferDegreeSection {
  margin-top: 8px;
  padding-top: 0;
  border-top: none;
}

.transferDegreeBox {
  background: linear-gradient(135deg, #f8fafc, #e2e8f0);
  border: 2px solid var(--primary-color);
  border-radius: 8px;
  font-size: 0.85rem;
  box-shadow:
    0 2px 6px rgba(76, 104, 192, 0.15),
    0 0 20px rgba(76, 104, 192, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.8);
  transition: var(--transition-all);
  position: relative;
  overflow: hidden;
}

/* ثيم خاص لكارت الأطراف */
.transferDegreeBox.partiesTheme {
  background: linear-gradient(135deg, #f0f2ff, #e8ebff);
  border: 2px solid var(--parties-color);
  box-shadow:
    0 2px 6px rgba(42, 46, 112, 0.15),
    0 0 20px rgba(42, 46, 112, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.8);
}

.transferDegreeBox.partiesTheme .transferHeader {
  background: linear-gradient(135deg, var(--parties-color), var(--parties-dark));
}

.transferDegreeBox.partiesTheme .transferButton {
  background: linear-gradient(135deg, var(--parties-color), var(--parties-dark));
  box-shadow: 0 2px 4px rgba(42, 46, 112, 0.2);
}

/* ثيم خاص لكارت الترقيم والتصنيف */
.transferDegreeBox.identificationTheme {
  background: linear-gradient(135deg, #f0f4ff, #e6efff);
  border: 2px solid var(--identification-color);
  box-shadow:
    0 2px 6px rgba(76, 104, 192, 0.15),
    0 0 20px rgba(76, 104, 192, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.8);
}

.transferDegreeBox.identificationTheme .transferHeader {
  background: linear-gradient(135deg, var(--identification-color), var(--identification-dark));
}

.transferDegreeBox.identificationTheme .transferButton {
  background: linear-gradient(135deg, var(--identification-color), var(--identification-dark));
  box-shadow: 0 2px 4px rgba(76, 104, 192, 0.2);
}

.transferDegreeBox.identificationTheme .transferButton:hover {
  background: linear-gradient(135deg, var(--identification-dark), var(--identification-color));
  box-shadow: 0 4px 8px rgba(76, 104, 192, 0.3);
}

/* ثيم خاص لكارت المحكمة */
.transferDegreeBox.locationTheme {
  background: linear-gradient(135deg, #f0f8ff, #e6f3ff);
  border: 2px solid var(--location-color);
  box-shadow:
    0 2px 6px rgba(76, 104, 192, 0.15),
    0 0 20px rgba(76, 104, 192, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.8);
}

.transferDegreeBox.locationTheme .transferHeader {
  background: linear-gradient(135deg, var(--location-color), var(--location-dark));
}

/* حاوية أزرار الإحالة */
.referralButtonsContainer {
  display: flex;
  gap: 10px;
  flex-direction: column;
}

@media (min-width: 768px) {
  .referralButtonsContainer {
    flex-direction: row;
  }
}

/* أزرار الإحالة */
.referralButton {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 10px 16px;
  border: none;
  border-radius: var(--border-radius);
  font-size: 0.9rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  flex: 1;
  min-height: 40px;
}

.courtReferralButton {
  background: linear-gradient(135deg, #4c68c0, #2a2e70);
  color: white;
  box-shadow: 0 2px 4px rgba(76, 104, 192, 0.2);
}

.courtReferralButton:hover {
  background: linear-gradient(135deg, #2a2e70, #4c68c0);
  box-shadow: 0 4px 8px rgba(76, 104, 192, 0.3);
  transform: translateY(-1px);
}

.expertReferralButton {
  background: linear-gradient(135deg, #555269, #2a2e70);
  color: white;
  box-shadow: 0 2px 4px rgba(85, 82, 105, 0.2);
}

.expertReferralButton:hover {
  background: linear-gradient(135deg, #2a2e70, #555269);
  box-shadow: 0 4px 8px rgba(85, 82, 105, 0.3);
  transform: translateY(-1px);
}

/* تم إزالة انيميشن shimmer لتجنب الاهتزاز */

/* أنماط نافذة تحويل الحالة المحسنة */
.statusOptionsContainer {
  margin: 20px 0;
  padding: 15px;
  background: var(--neutral-50);
  border-radius: var(--border-radius);
  border: 1px solid var(--neutral-200);
}

.questionText {
  font-size: 1rem;
  font-weight: 500;
  color: var(--neutral-700);
  margin-bottom: 15px;
  text-align: center;
}

.statusOptions {
  display: flex;
  flex-direction: column;
  gap: 10px;
  margin-bottom: 15px;
}

.statusOptionButton {
  padding: 12px 20px;
  border: 2px solid var(--neutral-300);
  border-radius: var(--border-radius);
  background: white;
  color: var(--neutral-700);
  font-size: 0.95rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  text-align: center;
}

.statusOptionButton:hover {
  border-color: var(--primary-color);
  background: var(--primary-light);
  color: var(--primary-dark);
  transform: translateY(-1px);
  box-shadow: var(--box-shadow-primary);
}

.statusOptionButton.selected {
  border-color: var(--primary-color);
  background: var(--primary-color);
  color: white;
  box-shadow: var(--box-shadow-primary);
}

.statusOptionButton.selected:hover {
  background: var(--primary-dark);
  border-color: var(--primary-dark);
}

.dateInputContainer {
  margin-top: 20px;
  padding: 15px;
  background: white;
  border-radius: var(--border-radius);
  border: 1px solid var(--neutral-300);
}

.dateLabel {
  display: block;
  font-size: 0.9rem;
  font-weight: 500;
  color: var(--neutral-700);
  margin-bottom: 8px;
}

.dateInput {
  width: 100%;
  padding: 10px 12px;
  border: 2px solid var(--neutral-300);
  border-radius: var(--border-radius);
  font-size: 0.95rem;
  transition: var(--transition);
  background: white;
  color: var(--neutral-800);
}

.dateInput:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(76, 104, 192, 0.1);
}

/* تحسين أزرار التأكيد */
.transferConfirmButton:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  background: var(--neutral-400);
  border-color: var(--neutral-400);
}

.transferConfirmButton:disabled:hover {
  transform: none;
  box-shadow: none;
}

.transferHeader {
  padding: 6px 10px;
  background: var(--gradient-primary);
  color: white;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.transferHeader:hover {
  background: linear-gradient(135deg, var(--primary-dark), var(--primary-color));
  box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.5);
}

.transferTitleSection {
  display: flex;
  align-items: center;
  gap: 6px;
}

.expandButton {
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 0.8rem;
  transition: transform 0.3s ease;
}

.expandButton:hover {
  transform: scale(1.1);
}

.transferIcon {
  color: white;
  font-size: 0.9rem;
}

.transferTitle {
  font-weight: 600;
  color: white;
  font-size: 0.85rem;
}

.transferContent {
  padding: 8px 10px;
  background: linear-gradient(135deg, #ffffff, #f8fafc);
  box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.8);
}

.transferInfoRow {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 6px;
  font-size: 0.8rem;
  padding: 3px 4px;
  border-radius: 4px;
  transition: all 0.3s ease;
}

.transferInfoRow:last-child {
  margin-bottom: 0;
}

.transferLabel {
  color: var(--neutral-600);
  font-weight: 500;
}

.transferValue {
  color: var(--primary-dark);
  font-weight: 600;
}

.transferButton {
  width: 100%;
  padding: 8px 12px;
  background: var(--gradient-primary);
  color: white;
  border: none;
  border-radius: 6px;
  font-size: 0.8rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
  box-shadow: 0 2px 4px rgba(76, 104, 192, 0.2);
}

.transferButton:hover {
  background: linear-gradient(135deg, var(--primary-dark), var(--primary-color));
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(76, 104, 192, 0.3);
}

.transferButton:active {
  transform: translateY(0);
  transition: all 0.1s ease;
}

/* أنماط نافذة تأكيد تحويل الدرجة */
.transferConfirmationOverlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  animation: fadeIn 0.3s ease;
}

.transferConfirmationDialog {
  background: white;
  border-radius: 12px;
  padding: 24px;
  max-width: 400px;
  width: 90%;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
  text-align: center;
  animation: slideIn 0.3s ease;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.transferConfirmationIcon {
  width: 60px;
  height: 60px;
  background: var(--gradient-primary);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 16px;
  color: white;
  font-size: 1.5rem;
  box-shadow: 0 4px 12px rgba(76, 104, 192, 0.3);
}

.transferConfirmationTitle {
  font-size: 1.2rem;
  font-weight: 600;
  color: var(--neutral-800);
  margin-bottom: 12px;
}

.transferConfirmationMessage {
  color: var(--neutral-600);
  line-height: 1.5;
  margin-bottom: 24px;
  font-size: 0.95rem;
}

.transferConfirmationActions {
  display: flex;
  gap: 12px;
  justify-content: center;
}

.transferConfirmButton,
.transferCancelButton {
  padding: 10px 20px;
  border: none;
  border-radius: 8px;
  font-size: 0.9rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 6px;
  min-width: 120px;
  justify-content: center;
}

.transferConfirmButton {
  background: var(--gradient-primary);
  color: white;
  box-shadow: 0 2px 4px rgba(76, 104, 192, 0.2);
}

.transferConfirmButton:hover {
  background: var(--gradient-primary-reverse);
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(76, 104, 192, 0.3);
}

.transferCancelButton {
  background: var(--neutral-400);
  color: white;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.transferCancelButton:hover {
  background: var(--neutral-600);
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.partiesCard .expandToggle:hover {
  color: var(--parties-dark);
}

.identificationCard .expandToggle:hover {
  color: var(--identification-dark);
}

.locationCard .expandToggle:hover {
  color: var(--location-dark);
}

/* حاوية حقل التعديل الأفقي */
.editFieldContainerHorizontal {
  display: flex;
  flex-direction: column; /* الأزرار تحت الحقل */
  gap: 8px;
  width: 100%;
}

/* حقل الإدخال */
.editInput, .textareaInput {
  padding: 8px 10px;
  border: 1px solid var(--neutral-300);
  border-radius: var(--border-radius-sm);
  font-size: 0.95rem;
  transition: var(--transition);
  background: var(--neutral-50);
  width: 100%;
}

.editInput:focus, .textareaInput:focus {
  outline: none;
  border-color: var(--primary-color);
  background: white;
  box-shadow: 0 0 0 2px rgba(120, 166, 200, 0.1);
}

.textareaInput {
  min-height: 60px;
  resize: vertical;
}

/* حقل خطأ */
.inputError {
  border-color: var(--danger-color);
  background: #fff5f5;
}

/* نص الخطأ */
.errorText {
  color: var(--danger-color);
  font-size: 0.85rem;
  font-weight: 400;
}

/* أزرار التعديل الأفقي */
.editActionsHorizontal {
  display: flex;
  gap: 8px;
  justify-content: flex-end;
}

/* زر الحفظ */
.saveEditButton {
  width: 24px;
  height: 24px;
  border: none;
  border-radius: 50%;
  background: var(--success-color);
  color: white;
  cursor: pointer;
  transition: var(--transition);
  display: flex;
  align-items: center;
  justify-content: center;
}

.saveEditButton:hover {
  background: #3a5ba0;
}

/* زر الإلغاء */
.cancelEditButton {
  width: 24px;
  height: 24px;
  border: none;
  border-radius: 50%;
  background: var(--danger-color);
  color: white;
  cursor: pointer;
  transition: var(--transition);
  display: flex;
  align-items: center;
  justify-content: center;
}

.cancelEditButton:hover {
  background: #1f2356;
}

/* تأثيرات خاصة */
@keyframes fadeIn {
  from { opacity: 0; transform: translateY(5px); }
  to { opacity: 1; transform: translateY(0); }
}

/* تأثير اللمعة */
@keyframes shimmer {
  0% {
    transform: translateX(-100%) translateY(-100%) rotate(45deg);
  }
  50% {
    transform: translateX(0%) translateY(0%) rotate(45deg);
  }
  100% {
    transform: translateX(100%) translateY(100%) rotate(45deg);
  }
}

/* تنسيقات للهواتف المحمولة (أقل من 769px) */
@media (max-width: 768px) {
  .coloredCardsContainerHorizontal {
    grid-template-columns: 1fr; /* عمود واحد للهواتف */
  }

  /* الحفاظ على التخطيط الأفقي للحقول على الهواتف */
  .dataFieldHorizontal {
    flex-direction: row; /* الحفاظ على التخطيط الأفقي */
    align-items: flex-start;
    gap: 8px; /* مسافة أصغر بين العنوان والقيمة */
  }

  .fieldLabelHorizontal {
    margin-bottom: 0; /* إزالة الهامش السفلي */
    white-space: nowrap; /* منع التفاف العنوان */
    min-width: 80px; /* عرض أدنى للعنوان */
    flex-shrink: 0;
  }

  .fieldValueContainerHorizontal {
    flex: 1; /* أخذ المساحة المتبقية */
    text-align: right; /* محاذاة لليمين على الهواتف */
  }
}

/* الحفاظ على تنسيقات الصفحات الأخرى */
.actionsSection {
  margin-top: 20px;
}

.addOptions {
  display: flex;
  gap: 15px;
  justify-content: center;
  margin-bottom: 20px;
}

.addDeferralButton,
.addActionOptionButton {
  padding: 12px 24px;
  border: none;
  border-radius: var(--border-radius-md);
  font-weight: 500;
  cursor: pointer;
  transition: var(--transition-all);
  display: flex;
  align-items: center;
  gap: 10px;
  font-size: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0), inset 0 0 10px rgba(255, 201, 135, 0.454);
}

.addDeferralButton {
  background: linear-gradient(90deg, rgba(42, 46, 112, 0.8), #2a2e70 100%);
  color: white;
}

.addDeferralButton:hover {
  background-color: #1a1d4a;
}

.addActionOptionButton {
  background: linear-gradient(90deg, rgba(76, 104, 192, 0.8), #4c68c0 100%);
  color: white;
}

.addActionOptionButton:hover {
  background-color: #3a5ba0;
}

.promptDialog {
  background: #ffffff;
  padding: 20px;
  border-radius: 8px;
  border: 1px solid #e0e0e0;
  max-width: 450px;
  margin: 15px auto;
  text-align: center;
  direction: rtl;
}

.promptDialog h3 {
  font-size: 1.4rem;
  color: #333;
  margin-bottom: 15px;
  font-weight: 500;
}

.previewDialog {
  background: #f5f5f5;
  padding: 10px;
  border-radius: 4px;
  margin: 10px 0;
}

/* أنماط رأس البطاقة والزر المضاف للتقرير */
.cardHeader {
  background: var(--neutral-100);
  padding: 16px 20px;
  border-bottom: 1px solid #eee;
  border-radius: 20px 20px 0 0;
}

/* رأس البطاقة داخل البطاقة البسيطة */
.cardHeaderInSimple {
  background: var(--neutral-100);
  padding: 12px 16px;
  margin: -18px -14px 16px -14px; /* إلغاء padding من simpleCard */
  border-bottom: 1px solid #eee;
  border-radius: 20px 20px 0 0;
}

.cardHeaderContent {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  gap: 16px;
}

.cardTitle {
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--text-primary);
  flex: 1;
}

/* زر عرض التقرير كامل */
.fullReportButton {
    padding: 6px 12px; /* padding مناسب للنص */
  border: none;
  border-radius: 8px; /* حواف مدورة ناعمة بدلاً من دائرية */
  background: var(--neutral-200);
  color: var(--neutral-600);
  cursor: pointer;
  transition: var(--transition);
  display: inline-flex;
  align-items: center;
  justify-content: center;
  opacity: 0.7;
  flex-shrink: 0;
  box-sizing: border-box;
  font-size: 14px;
  line-height: 1;
  height: auto; /* ارتفاع تلقائي حسب المحتوى */
  width: auto; /* عرض تلقائي حسب المحتوى */
}

.fullReportButton:hover {
  background: var(--primary-dark);
  color: white;
  opacity: 1;
}

.fullReportButton svg {
  font-size: 0.7rem;
}

/* النافذة المنبثقة للتقرير الكامل */
.fullReportModalOverlay {
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  width: 100vw !important;
  height: 100vh !important;
  background: rgba(0, 0, 0, 0.8) !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  z-index: 2147483647 !important; /* أعلى z-index ممكن */
  padding: 20px !important;
  margin: 0 !important;
  transform: none !important;
  backdrop-filter: blur(3px);
  /* إضافة أنماط إضافية لضمان تغطية كامل الشاشة */
  inset: 0 !important;
  pointer-events: all !important;
  box-sizing: border-box !important;
}

.fullReportModal {
  background: white !important;
  border-radius: 20px !important;
  max-width: 800px !important;
  width: 90% !important;
  max-height: 85vh !important;
  display: flex !important;
  flex-direction: column !important;
  box-shadow: 0 25px 80px rgba(0, 0, 0, 0.5) !important;
  animation: modalSlideUp 0.3s ease-out !important;
  position: relative !important;
  margin: auto !important;
  transform: none !important;
  z-index: 2147483647 !important;
  overflow: hidden !important;
}

@keyframes modalSlideUp {
  from {
    opacity: 0;
    transform: translateY(30px) scale(0.9);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.fullReportModalHeader {
  padding: 20px 24px;
  border-bottom: 1px solid var(--border-color);
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: var(--neutral-50);
  border-radius: 20px 20px 0 0;
}

.fullReportModalHeader h3 {
  margin: 0;
  font-size: 1.4rem;
  font-weight: 600;
  color: var(--text-primary);
}

.closeModalButton {
  background: none;
  border: none;
  font-size: 1.8rem;
  color: var(--text-secondary);
  cursor: pointer;
  padding: 4px 8px;
  border-radius: 50%;
  transition: all var(--transition-normal);
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
}

.closeModalButton:hover {
  background: var(--neutral-200);
  color: var(--text-primary);
}

.fullReportModalContent {
  padding: 24px;
  overflow-y: auto;
  flex: 1;
}

.fullReportModalContent .timelineScrollContainer {
  max-height: none;
}

/* تحسينات للهواتف المحمولة */
@media (max-width: 768px) {
  .cardHeaderContent {
    flex-direction: row;
    gap: 8px;
    align-items: center;
    justify-content: space-between;
  }
  
  .fullReportButton {
    margin-left: 0;
    font-size: 0.75rem;
    padding: 3px 6px;
    border-radius: 6px;
    gap: 3px;
  }
  
  .fullReportModalOverlay {
    padding: 10px !important;
  }
  
  .fullReportModal {
    width: 95% !important;
    max-height: 90vh !important;
    margin: 0 !important;
  }
  
  .fullReportModalHeader {
    padding: 16px 20px;
  }
  
  .fullReportModalHeader h3 {
    font-size: 1.2rem;
  }
  
  .fullReportModalContent {
    padding: 20px;
  }
}

/* إضافة تأكيد إضافي للتمركز */
.fullReportModalOverlay * {
  box-sizing: border-box;
}

/* منع التمرير في الخلفية عند فتح النافذة */
body.modal-open {
  overflow: hidden;
}

/* تأكيد على أن الصفحة تدعم النوافذ المنبثقة */
#case-details-page {
  position: relative;
  z-index: 1;
}

/* ضمان أن النافذة المنبثقة فوق جميع العناصر في الصفحة */
#case-details-page .fullReportModalOverlay {
  position: fixed !important;
  z-index: 999999 !important;
}