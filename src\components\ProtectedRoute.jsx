import React, { useEffect, useState } from 'react';
import { Navigate } from 'react-router-dom';

const ProtectedRoute = ({ children, user, loading }) => {
  const [timeoutError, setTimeoutError] = useState(false);

  useEffect(() => {
    const timer = setTimeout(() => {
      if (loading) {
        console.log("ProtectedRoute - Loading timeout exceeded.");
        setTimeoutError(true);
      }
    }, 10000); // 10 ثواني

    return () => clearTimeout(timer);
  }, [loading]);

  console.log("ProtectedRoute - User:", user);
  console.log("ProtectedRoute - Loading:", loading);

  if (timeoutError) {
    console.log("ProtectedRoute - Showing timeout error.");
    return <div>خطأ: لقد استغرق التحقق من حالة المصادقة وقتًا طويلاً. يرجى تحديث الصفحة.</div>;
  }

  if (loading) {
    console.log("ProtectedRoute - Showing loading state...");
    return <div>جاري التحقق من حالة المصادقة...</div>;
  }

  if (user) {
    console.log("ProtectedRoute - User authenticated, showing children...");
    return children;
  } else {
    console.log("ProtectedRoute - User not authenticated, redirecting to login...");
    return <Navigate to="/login" replace />;
  }
};

export default ProtectedRoute;