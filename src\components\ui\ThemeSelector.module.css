@import '../../styles/variables.css';

.themeSelector {
  background: var(--current-bg-primary);
  border-radius: var(--radius-lg);
  padding: var(--space-6);
  border: 1px solid var(--current-border-primary);
  box-shadow: var(--shadow-md);
  transition: all var(--transition-normal);
}

.header {
  margin-bottom: var(--space-6);
  text-align: center;
}

.title {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-semibold);
  color: var(--current-text-primary);
  margin: 0 0 var(--space-2) 0;
}

.subtitle {
  font-size: var(--font-size-base);
  color: var(--current-text-secondary);
  margin: 0;
}

.themesGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--space-4);
  margin-bottom: var(--space-6);
}

.themeCard {
  background: var(--current-bg-secondary);
  border-radius: var(--radius-md);
  border: 2px solid var(--current-border-primary);
  cursor: pointer;
  transition: all var(--transition-normal);
  overflow: hidden;
  position: relative;
}

.themeCard:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
  border-color: var(--primary-color);
}

.themeCard.active {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.themePreview {
  height: 80px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
}

.previewContent {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  backdrop-filter: blur(10px);
}

.themeIcon {
  font-size: 2rem;
  color: var(--current-text-primary);
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
}

.themeInfo {
  padding: var(--space-4);
  text-align: center;
}

.themeName {
  font-size: var(--font-size-md);
  font-weight: var(--font-weight-medium);
  color: var(--current-text-primary);
  margin: 0 0 var(--space-1) 0;
}

.themeDescription {
  font-size: var(--font-size-sm);
  color: var(--current-text-secondary);
  margin: 0;
  line-height: var(--line-height-normal);
}

.activeIndicator {
  position: absolute;
  top: var(--space-2);
  right: var(--space-2);
  background: var(--primary-color);
  border-radius: var(--radius-full);
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  animation: var(--animation-fade-in);
}

.checkmark {
  color: white;
  font-size: 12px;
  font-weight: bold;
}

.currentThemeInfo {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--space-2);
  padding: var(--space-3);
  background: var(--current-bg-tertiary);
  border-radius: var(--radius-sm);
  border: 1px solid var(--current-border-secondary);
}

.currentThemeLabel {
  font-size: var(--font-size-sm);
  color: var(--current-text-secondary);
  font-weight: var(--font-weight-medium);
}

.currentThemeName {
  font-size: var(--font-size-sm);
  color: var(--primary-color);
  font-weight: var(--font-weight-semibold);
}

/* تأثيرات خاصة للثيم الشفاف */
body.theme-liquid .themeSelector {
  backdrop-filter: var(--current-backdrop-blur);
  background: var(--current-bg-primary);
  border: 1px solid var(--current-border-primary);
}

body.theme-liquid .themeCard {
  backdrop-filter: blur(10px);
  background: var(--current-glass-effect);
}

/* تصميم متجاوب */
@media (max-width: 768px) {
  .themesGrid {
    grid-template-columns: 1fr;
    gap: var(--space-3);
  }
  
  .themeCard {
    display: flex;
    align-items: center;
    text-align: right;
  }
  
  .themePreview {
    width: 80px;
    height: 60px;
    flex-shrink: 0;
  }
  
  .themeInfo {
    flex: 1;
    text-align: right;
    padding: var(--space-3);
  }
  
  .activeIndicator {
    position: relative;
    top: auto;
    right: auto;
    margin-left: var(--space-2);
  }
}

@media (max-width: 480px) {
  .themeSelector {
    padding: var(--space-4);
  }
  
  .header {
    margin-bottom: var(--space-4);
  }
  
  .title {
    font-size: var(--font-size-lg);
  }
  
  .subtitle {
    font-size: var(--font-size-xs);
  }
}
