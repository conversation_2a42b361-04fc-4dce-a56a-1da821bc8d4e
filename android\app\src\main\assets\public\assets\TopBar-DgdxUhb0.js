var pt=e=>{throw TypeError(e)};var U=(e,t,s)=>t.has(e)||pt("Cannot "+s);var i=(e,t,s)=>(U(e,t,"read from private field"),s?s.call(e):t.get(e)),b=(e,t,s)=>t.has(e)?pt("Cannot add the same private member more than once"):t instanceof WeakSet?t.add(e):t.set(e,s),p=(e,t,s,n)=>(U(e,t,"write to private field"),n?n.call(e,s):t.set(e,s),s),v=(e,t,s)=>(U(e,t,"access private method"),s);import{S as jt,E as gt,F as N,s as tt,G as K,H as kt,I as et,J as mt,K as Dt,L as Nt,M as Ot,O as yt,n as wt,r as S,e as Et,u as Bt,j as l,P as Lt,k as Ft,q as It,w as Mt,c as Qt,d as Tt,g as zt}from"./index-Bd3HN_hN.js";import{G as H}from"./iconBase-BmtohqY9.js";var _,o,q,C,F,z,E,B,$,P,W,I,M,L,A,h,V,st,it,nt,at,rt,ot,lt,_t,Ct,Pt=(Ct=class extends jt{constructor(t,s){super();b(this,h);b(this,_);b(this,o);b(this,q);b(this,C);b(this,F);b(this,z);b(this,E);b(this,B);b(this,$);b(this,P);b(this,W);b(this,I);b(this,M);b(this,L);b(this,A,new Set);this.options=s,p(this,_,t),p(this,B,null),p(this,E,gt()),this.options.experimental_prefetchInRender||i(this,E).reject(new Error("experimental_prefetchInRender feature flag is not enabled")),this.bindMethods(),this.setOptions(s)}bindMethods(){this.refetch=this.refetch.bind(this)}onSubscribe(){this.listeners.size===1&&(i(this,o).addObserver(this),vt(i(this,o),this.options)?v(this,h,V).call(this):this.updateResult(),v(this,h,at).call(this))}onUnsubscribe(){this.hasListeners()||this.destroy()}shouldFetchOnReconnect(){return ct(i(this,o),this.options,this.options.refetchOnReconnect)}shouldFetchOnWindowFocus(){return ct(i(this,o),this.options,this.options.refetchOnWindowFocus)}destroy(){this.listeners=new Set,v(this,h,rt).call(this),v(this,h,ot).call(this),i(this,o).removeObserver(this)}setOptions(t){const s=this.options,n=i(this,o);if(this.options=i(this,_).defaultQueryOptions(t),this.options.enabled!==void 0&&typeof this.options.enabled!="boolean"&&typeof this.options.enabled!="function"&&typeof N(this.options.enabled,i(this,o))!="boolean")throw new Error("Expected enabled to be a boolean or a callback that returns a boolean");v(this,h,lt).call(this),i(this,o).setOptions(this.options),s._defaulted&&!tt(this.options,s)&&i(this,_).getQueryCache().notify({type:"observerOptionsUpdated",query:i(this,o),observer:this});const c=this.hasListeners();c&&bt(i(this,o),n,this.options,s)&&v(this,h,V).call(this),this.updateResult(),c&&(i(this,o)!==n||N(this.options.enabled,i(this,o))!==N(s.enabled,i(this,o))||K(this.options.staleTime,i(this,o))!==K(s.staleTime,i(this,o)))&&v(this,h,st).call(this);const r=v(this,h,it).call(this);c&&(i(this,o)!==n||N(this.options.enabled,i(this,o))!==N(s.enabled,i(this,o))||r!==i(this,L))&&v(this,h,nt).call(this,r)}getOptimisticResult(t){const s=i(this,_).getQueryCache().build(i(this,_),t),n=this.createResult(s,t);return At(this,n)&&(p(this,C,n),p(this,z,this.options),p(this,F,i(this,o).state)),n}getCurrentResult(){return i(this,C)}trackResult(t,s){const n={};return Object.keys(t).forEach(c=>{Object.defineProperty(n,c,{configurable:!1,enumerable:!0,get:()=>(this.trackProp(c),s==null||s(c),t[c])})}),n}trackProp(t){i(this,A).add(t)}getCurrentQuery(){return i(this,o)}refetch({...t}={}){return this.fetch({...t})}fetchOptimistic(t){const s=i(this,_).defaultQueryOptions(t),n=i(this,_).getQueryCache().build(i(this,_),s);return n.fetch().then(()=>this.createResult(n,s))}fetch(t){return v(this,h,V).call(this,{...t,cancelRefetch:t.cancelRefetch??!0}).then(()=>(this.updateResult(),i(this,C)))}createResult(t,s){var ft;const n=i(this,o),c=this.options,r=i(this,C),a=i(this,F),d=i(this,z),w=t!==n?t.state:i(this,q),{state:g}=t;let u={...g},x=!1,f;if(s._optimisticResults){const k=this.hasListeners(),G=!k&&vt(t,s),T=k&&bt(t,n,s,c);(G||T)&&(u={...u,...Ot(g.data,t.options)}),s._optimisticResults==="isRestoring"&&(u.fetchStatus="idle")}let{error:O,errorUpdatedAt:j,status:R}=u;f=u.data;let Q=!1;if(s.placeholderData!==void 0&&f===void 0&&R==="pending"){let k;r!=null&&r.isPlaceholderData&&s.placeholderData===(d==null?void 0:d.placeholderData)?(k=r.data,Q=!0):k=typeof s.placeholderData=="function"?s.placeholderData((ft=i(this,W))==null?void 0:ft.state.data,i(this,W)):s.placeholderData,k!==void 0&&(R="success",f=yt(r==null?void 0:r.data,k,s),x=!0)}if(s.select&&f!==void 0&&!Q)if(r&&f===(a==null?void 0:a.data)&&s.select===i(this,$))f=i(this,P);else try{p(this,$,s.select),f=s.select(f),f=yt(r==null?void 0:r.data,f,s),p(this,P,f),p(this,B,null)}catch(k){p(this,B,k)}i(this,B)&&(O=i(this,B),f=i(this,P),j=Date.now(),R="error");const J=u.fetchStatus==="fetching",X=R==="pending",Z=R==="error",ut=X&&J,dt=f!==void 0,D={status:R,fetchStatus:u.fetchStatus,isPending:X,isSuccess:R==="success",isError:Z,isInitialLoading:ut,isLoading:ut,data:f,dataUpdatedAt:u.dataUpdatedAt,error:O,errorUpdatedAt:j,failureCount:u.fetchFailureCount,failureReason:u.fetchFailureReason,errorUpdateCount:u.errorUpdateCount,isFetched:u.dataUpdateCount>0||u.errorUpdateCount>0,isFetchedAfterMount:u.dataUpdateCount>w.dataUpdateCount||u.errorUpdateCount>w.errorUpdateCount,isFetching:J,isRefetching:J&&!X,isLoadingError:Z&&!dt,isPaused:u.fetchStatus==="paused",isPlaceholderData:x,isRefetchError:Z&&dt,isStale:ht(t,s),refetch:this.refetch,promise:i(this,E)};if(this.options.experimental_prefetchInRender){const k=Y=>{D.status==="error"?Y.reject(D.error):D.data!==void 0&&Y.resolve(D.data)},G=()=>{const Y=p(this,E,D.promise=gt());k(Y)},T=i(this,E);switch(T.status){case"pending":t.queryHash===n.queryHash&&k(T);break;case"fulfilled":(D.status==="error"||D.data!==T.value)&&G();break;case"rejected":(D.status!=="error"||D.error!==T.reason)&&G();break}}return D}updateResult(){const t=i(this,C),s=this.createResult(i(this,o),this.options);if(p(this,F,i(this,o).state),p(this,z,this.options),i(this,F).data!==void 0&&p(this,W,i(this,o)),tt(s,t))return;p(this,C,s);const n=()=>{if(!t)return!0;const{notifyOnChangeProps:c}=this.options,r=typeof c=="function"?c():c;if(r==="all"||!r&&!i(this,A).size)return!0;const a=new Set(r??i(this,A));return this.options.throwOnError&&a.add("error"),Object.keys(i(this,C)).some(d=>{const m=d;return i(this,C)[m]!==t[m]&&a.has(m)})};v(this,h,_t).call(this,{listeners:n()})}onQueryUpdate(){this.updateResult(),this.hasListeners()&&v(this,h,at).call(this)}},_=new WeakMap,o=new WeakMap,q=new WeakMap,C=new WeakMap,F=new WeakMap,z=new WeakMap,E=new WeakMap,B=new WeakMap,$=new WeakMap,P=new WeakMap,W=new WeakMap,I=new WeakMap,M=new WeakMap,L=new WeakMap,A=new WeakMap,h=new WeakSet,V=function(t){v(this,h,lt).call(this);let s=i(this,o).fetch(this.options,t);return t!=null&&t.throwOnError||(s=s.catch(kt)),s},st=function(){v(this,h,rt).call(this);const t=K(this.options.staleTime,i(this,o));if(et||i(this,C).isStale||!mt(t))return;const n=Dt(i(this,C).dataUpdatedAt,t)+1;p(this,I,setTimeout(()=>{i(this,C).isStale||this.updateResult()},n))},it=function(){return(typeof this.options.refetchInterval=="function"?this.options.refetchInterval(i(this,o)):this.options.refetchInterval)??!1},nt=function(t){v(this,h,ot).call(this),p(this,L,t),!(et||N(this.options.enabled,i(this,o))===!1||!mt(i(this,L))||i(this,L)===0)&&p(this,M,setInterval(()=>{(this.options.refetchIntervalInBackground||Nt.isFocused())&&v(this,h,V).call(this)},i(this,L)))},at=function(){v(this,h,st).call(this),v(this,h,nt).call(this,v(this,h,it).call(this))},rt=function(){i(this,I)&&(clearTimeout(i(this,I)),p(this,I,void 0))},ot=function(){i(this,M)&&(clearInterval(i(this,M)),p(this,M,void 0))},lt=function(){const t=i(this,_).getQueryCache().build(i(this,_),this.options);if(t===i(this,o))return;const s=i(this,o);p(this,o,t),p(this,q,t.state),this.hasListeners()&&(s==null||s.removeObserver(this),t.addObserver(this))},_t=function(t){wt.batch(()=>{t.listeners&&this.listeners.forEach(s=>{s(i(this,C))}),i(this,_).getQueryCache().notify({query:i(this,o),type:"observerResultsUpdated"})})},Ct);function Wt(e,t){return N(t.enabled,e)!==!1&&e.state.data===void 0&&!(e.state.status==="error"&&t.retryOnMount===!1)}function vt(e,t){return Wt(e,t)||e.state.data!==void 0&&ct(e,t,t.refetchOnMount)}function ct(e,t,s){if(N(t.enabled,e)!==!1){const n=typeof s=="function"?s(e):s;return n==="always"||n!==!1&&ht(e,t)}return!1}function bt(e,t,s,n){return(e!==t||N(n.enabled,e)===!1)&&(!s.suspense||e.state.status!=="error")&&ht(e,s)}function ht(e,t){return N(t.enabled,e)!==!1&&e.isStaleByTime(K(t.staleTime,e))}function At(e,t){return!tt(e.getCurrentResult(),t)}var St=S.createContext(!1),Ht=()=>S.useContext(St);St.Provider;function Vt(){let e=!1;return{clearReset:()=>{e=!1},reset:()=>{e=!0},isReset:()=>e}}var qt=S.createContext(Vt()),$t=()=>S.useContext(qt);function Gt(e,t){return typeof e=="function"?e(...t):!!e}function xt(){}var Yt=(e,t)=>{(e.suspense||e.throwOnError||e.experimental_prefetchInRender)&&(t.isReset()||(e.retryOnMount=!1))},Kt=e=>{S.useEffect(()=>{e.clearReset()},[e])},Jt=({result:e,errorResetBoundary:t,throwOnError:s,query:n,suspense:c})=>e.isError&&!t.isReset()&&!e.isFetching&&n&&(c&&e.data===void 0||Gt(s,[e.error,n])),Xt=e=>{const t=e.staleTime;e.suspense&&(e.staleTime=typeof t=="function"?(...s)=>Math.max(t(...s),1e3):Math.max(t??1e3,1e3),typeof e.gcTime=="number"&&(e.gcTime=Math.max(e.gcTime,1e3)))},Zt=(e,t)=>e.isLoading&&e.isFetching&&!t,Ut=(e,t)=>(e==null?void 0:e.suspense)&&t.isPending,Rt=(e,t,s)=>t.fetchOptimistic(e).catch(()=>{s.clearReset()});function te(e,t,s){var u,x,f,O,j;const n=Et(),c=Ht(),r=$t(),a=n.defaultQueryOptions(e);(x=(u=n.getDefaultOptions().queries)==null?void 0:u._experimental_beforeQuery)==null||x.call(u,a),a._optimisticResults=c?"isRestoring":"optimistic",Xt(a),Yt(a,r),Kt(r);const d=!n.getQueryCache().get(a.queryHash),[m]=S.useState(()=>new t(n,a)),w=m.getOptimisticResult(a),g=!c&&e.subscribed!==!1;if(S.useSyncExternalStore(S.useCallback(R=>{const Q=g?m.subscribe(wt.batchCalls(R)):xt;return m.updateResult(),Q},[m,g]),()=>m.getCurrentResult(),()=>m.getCurrentResult()),S.useEffect(()=>{m.setOptions(a)},[a,m]),Ut(a,w))throw Rt(a,m,r);if(Jt({result:w,errorResetBoundary:r,throwOnError:a.throwOnError,query:n.getQueryCache().get(a.queryHash),suspense:a.suspense}))throw w.error;if((O=(f=n.getDefaultOptions().queries)==null?void 0:f._experimental_afterQuery)==null||O.call(f,a,w),a.experimental_prefetchInRender&&!et&&Zt(w,c)){const R=d?Rt(a,m,r):(j=n.getQueryCache().get(a.queryHash))==null?void 0:j.promise;R==null||R.catch(xt).finally(()=>{m.updateResult()})}return a.notifyOnChangeProps?w:m.trackResult(w)}function ee(e,t){return te(e,Pt)}function se(e){return H({attr:{viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},child:[{tag:"path",attr:{d:"M18 8A6 6 0 0 0 6 8c0 7-3 9-3 9h18s-3-2-3-9"},child:[]},{tag:"path",attr:{d:"M13.73 21a2 2 0 0 1-3.46 0"},child:[]}]})(e)}function ie(e){return H({attr:{viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},child:[{tag:"path",attr:{d:"M3 9l9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"},child:[]},{tag:"polyline",attr:{points:"9 22 9 12 15 12 15 22"},child:[]}]})(e)}function ne(e){return H({attr:{viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},child:[{tag:"path",attr:{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4"},child:[]},{tag:"polyline",attr:{points:"16 17 21 12 16 7"},child:[]},{tag:"line",attr:{x1:"21",y1:"12",x2:"9",y2:"12"},child:[]}]})(e)}function ae(e){return H({attr:{viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},child:[{tag:"circle",attr:{cx:"12",cy:"12",r:"10"},child:[]},{tag:"line",attr:{x1:"12",y1:"8",x2:"12",y2:"16"},child:[]},{tag:"line",attr:{x1:"8",y1:"12",x2:"16",y2:"12"},child:[]}]})(e)}function re(e){return H({attr:{viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},child:[{tag:"circle",attr:{cx:"12",cy:"12",r:"3"},child:[]},{tag:"path",attr:{d:"M19.4 15a1.65 1.65 0 0 0 .33 1.82l.06.06a2 2 0 0 1 0 2.83 2 2 0 0 1-2.83 0l-.06-.06a1.65 1.65 0 0 0-1.82-.33 1.65 1.65 0 0 0-1 1.51V21a2 2 0 0 1-2 2 2 2 0 0 1-2-2v-.09A1.65 1.65 0 0 0 9 19.4a1.65 1.65 0 0 0-1.82.33l-.06.06a2 2 0 0 1-2.83 0 2 2 0 0 1 0-2.83l.06-.06a1.65 1.65 0 0 0 .33-1.82 1.65 1.65 0 0 0-1.51-1H3a2 2 0 0 1-2-2 2 2 0 0 1 2-2h.09A1.65 1.65 0 0 0 4.6 9a1.65 1.65 0 0 0-.33-1.82l-.06-.06a2 2 0 0 1 0-2.83 2 2 0 0 1 2.83 0l.06.06a1.65 1.65 0 0 0 1.82.33H9a1.65 1.65 0 0 0 1-1.51V3a2 2 0 0 1 2-2 2 2 0 0 1 2 2v.09a1.65 1.65 0 0 0 1 1.51 1.65 1.65 0 0 0 1.82-.33l.06-.06a2 2 0 0 1 2.83 0 2 2 0 0 1 0 2.83l-.06.06a1.65 1.65 0 0 0-.33 1.82V9a1.65 1.65 0 0 0 1.51 1H21a2 2 0 0 1 2 2 2 2 0 0 1-2 2h-.09a1.65 1.65 0 0 0-1.51 1z"},child:[]}]})(e)}function oe(e){return H({attr:{viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},child:[{tag:"path",attr:{d:"M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"},child:[]},{tag:"circle",attr:{cx:"12",cy:"7",r:"4"},child:[]}]})(e)}const le="_topBar_1z6il_73",ce="_leftSection_1z6il_141",he="_rightSection_1z6il_159",ue="_iconWrapper_1z6il_173",de="_iconButton_1z6il_181",fe="_tooltip_1z6il_229",pe="_notificationBadge_1z6il_237",ge="_profileWrapper_1z6il_385",me="_profileButton_1z6il_393",ye="_userAvatar_1z6il_437",ve="_userName_1z6il_461",be="_profileDropdown_1z6il_485",xe="_dropdownItem_1z6il_521",y={topBar:le,leftSection:ce,rightSection:he,iconWrapper:ue,iconButton:de,tooltip:fe,notificationBadge:pe,profileWrapper:ge,profileButton:me,userAvatar:ye,userName:ve,profileDropdown:be,dropdownItem:xe},Re=async e=>{if(!e)return[];const t=It(Qt(Tt,"cases"),Mt("userId","==",e)),n=(await zt(t)).docs.map(d=>({id:d.id,...d.data()})),c=new Date,r=new Date(c);r.setDate(c.getDate()+1);const a=[];return n.forEach(d=>{(d.deferrals||[]).forEach((g,u)=>{var f;if(g.isDeleted||!g.date)return;const x=new Date(g.date);x.getDate()===r.getDate()&&x.getMonth()===r.getMonth()&&x.getFullYear()===r.getFullYear()&&a.push({id:`${d.id}-defer-${u}`,type:"تأجيل",caseNumber:d.fullCaseNumber||"غير محدد",clientName:d.clientName||"غير محدد",courtLocation:d.courtLocation||"غير محددة",date:g.date,displayDate:new Date(g.date).toLocaleDateString("ar-EG",{day:"numeric",month:"long",year:"numeric"}),reasons:((f=g.reasons)==null?void 0:f.join("، "))||"لا يوجد سبب محدد"})}),(d.actions||[]).forEach((g,u)=>{if(g.isDeleted||!g.deadline)return;const x=new Date(g.deadline);x.getDate()===r.getDate()&&x.getMonth()===r.getMonth()&&x.getFullYear()===r.getFullYear()&&a.push({id:`${d.id}-action-${u}`,type:"إجراء",caseNumber:d.fullCaseNumber||"غير محدد",clientName:d.clientName||"غير محدد",courtLocation:d.courtLocation||"غير محددة",date:g.deadline,displayDate:new Date(g.deadline).toLocaleDateString("ar-EG",{day:"numeric",month:"long",year:"numeric"}),description:g.description||"لا يوجد وصف",linkedDeferralId:g.linkedDeferralId||""})})}),a},je=({casesList:e,currentUser:t})=>{var f,O;const[s,n]=S.useState(!1),c=Bt(),{data:r=[]}=ee({queryKey:["notifications",t==null?void 0:t.uid],queryFn:()=>Re(t==null?void 0:t.uid),enabled:!!t,refetchInterval:3e4}),a=S.useRef(null),d=S.useRef(null),m=S.useRef(null);S.useEffect(()=>{const j=()=>{if(s&&d.current&&a.current){d.current.getBoundingClientRect();const Q=m.current.getBoundingClientRect();a.current.style.top=`${Q.bottom+window.scrollY+2}px`,a.current.style.right=document.dir==="rtl"?"10px":"auto",a.current.style.left=document.dir==="rtl"?"auto":"10px"}},R=setTimeout(j,0);return window.addEventListener("resize",j),window.addEventListener("scroll",j),()=>{clearTimeout(R),window.removeEventListener("resize",j),window.removeEventListener("scroll",j)}},[s]);const w=async()=>{try{await Lt(Ft),n(!1),c("/login")}catch(j){console.error("خطأ أثناء تسجيل الخروج:",j)}},g=()=>{c("/cases")},u=()=>{c("/notifications")},x=()=>{n(!s)};return l.jsxs(l.Fragment,{children:[l.jsxs("header",{ref:m,className:y.topBar,children:[l.jsx("div",{className:y.leftSection}),l.jsxs("div",{className:y.rightSection,children:[l.jsx("div",{className:y.iconWrapper,children:l.jsxs("button",{className:y.iconButton,onClick:g,"aria-label":"إضافة قضية جديدة",children:[l.jsx(ae,{size:20}),l.jsx("span",{className:y.tooltip,children:"إضافة قضية"})]})}),l.jsx("div",{className:y.iconWrapper,children:l.jsxs("button",{className:y.iconButton,"aria-label":"الإشعارات",onClick:u,children:[l.jsx(se,{size:20}),r.length>0&&l.jsx("span",{className:y.notificationBadge,children:r.length}),l.jsx("span",{className:y.tooltip,children:"الإشعارات"})]})}),l.jsx("div",{className:y.iconWrapper,children:l.jsxs("button",{className:y.iconButton,onClick:()=>c("/dashboard"),"aria-label":"الصفحة الرئيسية",children:[l.jsx(ie,{size:20}),l.jsx("span",{className:y.tooltip,children:"الرئيسية"})]})}),l.jsx("div",{className:y.profileWrapper,children:l.jsxs("button",{ref:d,className:y.profileButton,onClick:x,"aria-label":"ملف المستخدم","aria-expanded":s,children:[l.jsx("div",{className:y.userAvatar,children:((f=t==null?void 0:t.displayName)==null?void 0:f.charAt(0))||((O=t==null?void 0:t.email)==null?void 0:O.charAt(0))||"U"}),l.jsx("span",{className:y.userName,children:(t==null?void 0:t.displayName)||(t==null?void 0:t.email)||"المستخدم"})]})})]})]}),s&&l.jsxs("div",{ref:a,className:y.profileDropdown,children:[l.jsxs("div",{className:y.dropdownItem,onClick:()=>{c("/profile"),n(!1)},children:[l.jsx("span",{children:"الملف الشخصي"}),l.jsx(oe,{size:14})]}),l.jsxs("div",{className:y.dropdownItem,onClick:()=>{c("/settings"),n(!1)},children:[l.jsx("span",{children:"إعدادات قوالب التأجيلات"}),l.jsx(re,{size:14})]}),l.jsxs("div",{className:y.dropdownItem,onClick:w,children:[l.jsx("span",{children:"تسجيل الخروج"}),l.jsx(ne,{size:14})]})]})]})};export{je as T,xt as n,Gt as s};
