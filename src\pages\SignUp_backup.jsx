import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import './SignUp.css';
import LoadingSpinner from '../components/ui/LoadingSpinner';
// استخدام الـ utilities الموحدة بدلاً من التكرار
import { signupUser, AUTH_STATES } from '../utils/authUtils';
import { useLoading } from '../hooks/useLoading';
import { useForm, VALIDATION_RULES, FormField } from '../utils/formUtils';
import { checkSignupLockout } from '../utils/LockoutManager';

function SignUp() {
  // استخدام الـ hooks الموحدة بدلاً من التكرار
  const { isLoading, error, setLoading, setError, setSuccess, reset } = useLoading();
  const [successMessage, setSuccessMessage] = useState('');
  const [signupAttempts, setSignupAttempts] = useState(0);
  const [currentStep, setCurrentStep] = useState(1);
  const [completedSteps, setCompletedSteps] = useState([]);
  const navigate = useNavigate();

  // استخدام hook النماذج الموحد
  const {
    values,
    errors,
    handleChange,
    handleSubmit,
    validateForm,
    getFieldProps
  } = useForm(
    {
      username: '',
      email: '',
      password: '',
      confirmPassword: '',
      phone: ''
    },
    {
      username: [
        VALIDATION_RULES.required,
        VALIDATION_RULES.minLength(2)
      ],
      email: [
        VALIDATION_RULES.required,
        VALIDATION_RULES.email
      ],
      password: [
        VALIDATION_RULES.required,
        VALIDATION_RULES.password
      ],
      confirmPassword: [
        VALIDATION_RULES.confirmPassword(values.password)
      ],
      phone: [
        VALIDATION_RULES.phone
      ]
    }
  );

  useEffect(() => {
    const lockoutStatus = checkSignupLockout();
    if (lockoutStatus.isLocked) {
      setError(lockoutStatus.message);
      setLoading(true);
    } else {
      setSignupAttempts(lockoutStatus.attempts);
    }
  }, [setError, setLoading]);

  // التحقق من صحة الخطوة الحالية
  const validateCurrentStep = () => {
    switch (currentStep) {
      case 1:
        return !errors.username && !errors.email && values.username && values.email;
      case 2:
        return !errors.password && !errors.confirmPassword && values.password && values.confirmPassword;
      case 3:
        return true; // رقم الهاتف اختياري
      default:
        return false;
    }
  };

  // الانتقال للخطوة التالية
  const nextStep = () => {
    if (validateCurrentStep()) {
      setCurrentStep(prev => prev + 1);
      setCompletedSteps(prev => [...prev, currentStep]);
    }
  };

  // العودة للخطوة السابقة
  const prevStep = () => {
    setCurrentStep(prev => prev - 1);
  };

  // معالج إرسال النموذج
  const onSubmit = handleSubmit(async (formData) => {
    reset();
    setSuccessMessage('');
    setLoading(true);

    // استخدام الـ utility الموحدة لإنشاء الحساب
    const result = await signupUser(formData, signupAttempts);

    if (result.success) {
      console.log('تم إنشاء الحساب بنجاح:', result.user);
      setSignupAttempts(0);
      setSuccessMessage('تم إنشاء الحساب بنجاح! جاري التوجيه...');
      
      setTimeout(() => {
        navigate('/dashboard', { replace: true });
      }, 2000);
    } else {
      setError(result.error);
      if (result.attempts !== undefined) {
        setSignupAttempts(result.attempts);
      }
      
      // إذا كان الحساب مقفل، إظهار رسالة خاصة
      if (result.state === AUTH_STATES.LOCKED) {
        setLoading(true); // إبقاء النموذج معطل
      }
    }
  });

  // عرض الخطوة الحالية
  const renderStep = () => {
    switch (currentStep) {
      case 1:
        return (
          <div className="step-content">
            <h3>المعلومات الأساسية</h3>
            <FormField
              label="اسم المستخدم"
              name="username"
              placeholder="أدخل اسم المستخدم"
              required
              {...getFieldProps('username')}
            />
            <FormField
              label="البريد الإلكتروني"
              name="email"
              type="email"
              placeholder="أدخل البريد الإلكتروني"
              required
              {...getFieldProps('email')}
            />
          </div>
        );

      case 2:
        return (
          <div className="step-content">
            <h3>كلمة المرور</h3>
            <FormField
              label="كلمة المرور"
              name="password"
              type="password"
              placeholder="أدخل كلمة المرور"
              required
              {...getFieldProps('password')}
            />
            <FormField
              label="تأكيد كلمة المرور"
              name="confirmPassword"
              type="password"
              placeholder="أعد إدخال كلمة المرور"
              required
              {...getFieldProps('confirmPassword')}
            />
          </div>
        );

      case 3:
        return (
          <div className="step-content">
            <h3>معلومات إضافية</h3>
            <FormField
              label="رقم الهاتف (اختياري)"
              name="phone"
              type="tel"
              placeholder="أدخل رقم الهاتف"
              {...getFieldProps('phone')}
            />
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <div className="signup-container">
      <div className="signup-card">
        <div className="signup-header">
          <div className="logo-container">
            <div className="logo-icon">⚖️</div>
            <h1 className="logo-text">إنشاء حساب جديد</h1>
          </div>
          <p className="signup-subtitle">انضم إلى نظام إدارة القضايا القانونية</p>
        </div>

        {/* مؤشر التقدم */}
        <div className="progress-indicator">
          <div className="progress-bar">
            <div 
              className="progress-fill" 
              style={{ width: `${(currentStep / 3) * 100}%` }}
            ></div>
          </div>
          <div className="step-indicators">
            {[1, 2, 3].map(step => (
              <div 
                key={step}
                className={`step-indicator ${
                  step === currentStep ? 'active' : 
                  completedSteps.includes(step) ? 'completed' : ''
                }`}
              >
                {completedSteps.includes(step) ? '✓' : step}
              </div>
            ))}
          </div>
        </div>

        <div className="signup-content">
          {/* عرض رسائل الخطأ والنجاح */}
          {error && <div className="error-message">{error}</div>}
          {successMessage && <div className="success-message">{successMessage}</div>}

          <form onSubmit={onSubmit}>
            {renderStep()}

            <div className="form-actions">
              {currentStep > 1 && (
                <button
                  type="button"
                  className="btn-secondary"
                  onClick={prevStep}
                  disabled={isLoading}
                >
                  السابق
                </button>
              )}

              {currentStep < 3 ? (
                <button
                  type="button"
                  className="btn-primary"
                  onClick={nextStep}
                  disabled={!validateCurrentStep() || isLoading}
                >
                  التالي
                </button>
              ) : (
                <button
                  type="submit"
                  className="btn-primary"
                  disabled={isLoading || !validateForm()}
                >
                  {isLoading ? (
                    <>
                      <LoadingSpinner />
                      <span>جاري إنشاء الحساب...</span>
                    </>
                  ) : (
                    'إنشاء الحساب'
                  )}
                </button>
              )}
            </div>

            {signupAttempts > 0 && (
              <div className="attempts-warning">
                <span>⚠️ عدد المحاولات المتبقية: {5 - signupAttempts}</span>
              </div>
            )}
          </form>

          <div className="signup-footer">
            <p>لديك حساب بالفعل؟</p>
            <button
              className="login-link"
              onClick={() => navigate('/login')}
              disabled={isLoading}
            >
              تسجيل الدخول
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}

export default SignUp;
