@import '../../styles/variables.css';

.pageWrapper {
  min-height: 100vh;
  background: var(--white);
  display: flex;
  flex-direction: column;
  color: var(--neutral-800);
  font-family: var(--font-family-primary);
}

.mainContainer {
  flex: 1;
  display: flex;
  padding: 0;
  gap: 24px;
  flex-direction: row;
  width: 100%;
  overflow-y: auto;
}

/* Google-style Right Sidebar */
.accountManagementSection {
  width: 300px;
  flex-shrink: 0;
  background: #ffffff;
  border-radius: 0;
  padding: 40px 0 0 0;
  display: flex;
  flex-direction: column;
  overflow-y: auto;
  height: calc(100vh - 70px);
  position: fixed;
  right: 0;
  box-shadow: none;
  z-index: 100;
}

/* Main Content Area */
.mainContentArea {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 20px;
  overflow-y: auto;
  padding: 20px 20px 20px 20px;
  padding-right: 300px;
  background: #ffffff;
}

/* Mobile styles for header text and profile image */
@media (max-width: 768px) {
  h1 {
    margin-right: 0 !important;
    font-size: 1.3rem !important;
    text-align: right !important;
  }
  
  p {
    margin-right: 0 !important;
    font-size: 0.85rem !important;
    text-align: right !important;
  }
  
  .profileHeaderImage {
    width: 150px !important;
    height: 150px !important;
  }
  
  .profileHeaderContainer {
    gap: 20px !important;
  
  }
}

.pageHeader {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 8px 0 16px 0;
  flex-shrink: 0; /* Prevent header from shrinking */
  height: auto; /* Auto height based on content */
  margin-bottom: 16px;
  width: calc(100% - 250px); /* تعديل العرض ليتناسب مع عرض البار الجديد */
  position: relative;
}

/* Info Header */
.infoHeader {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 15px 20px;
  margin-bottom: 20px;
  max-width: 800px;
  width: 100%;
  margin-left: auto;
  margin-right: 0;
}

.infoHeader p {
  margin: 5px 0;
  color: #444746;
  font-size: 14px;
}

.pageTitle {
  color: #202124;
  margin: 0;
  font-size: 1.75rem;
  font-weight: 600;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
}

/* Personal Info Section */
.personalInfoSection {
  background: var(--white);
  border-radius: var(--radius-md);
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--neutral-200);
  padding: 30px;
  display: flex;
  flex-direction: column;
  gap: 20px;
  position: relative; /* For positioning the edit button */
  margin-bottom: 30px;
  max-width: 800px; /* Increased width */
  width: 100%;
  animation: var(--animation-fade-in);
  margin-left: 10px; /* للتحكم في الموضع - القيمة auto تدفعه لليمين */
  margin-right: 40px; /* للتحكم في الموضع - القيمة 0 تثبته من اليمين */
}

.infoMessage, .editModeMessage {
  margin-bottom: 20px;
  padding: 10px 15px;
  background-color: var(--neutral-50);
  border-radius: var(--radius-sm);
  font-size: 0.95rem;
  color: var(--neutral-500);
}

.editModeMessage {
  background-color: #e8f0fe;
  color: #1a73e8;
}

/* استخدام أنيميشن fadeIn من variables.css */

.profileDetails {
  display: flex;
  flex-direction: column;
  gap: 8px; /* Reduced gap */
  overflow: hidden; /* Ensure no scroll here */
}

.profileField {
  display: grid;
  grid-template-columns: 150px 1fr; /* Fixed width for label, rest for content */
  gap: 16px;
  align-items: center;
  padding: 16px 0;
  min-height: 50px;
  border-bottom: 1px solid #dadce0;
  width: 100%;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

/* Form Group for Edit Field */
.formGroup {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-bottom: 16px;
}

.formGroup label {
  color: #5f6368;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 8px;
}

.formInput {
  width: 100%;
  padding: 10px 12px;
  border: 1px solid #dadce0;
  border-radius: 4px;
  font-size: 16px;
  color: #202124;
  background: #ffffff;
}

.formInput:focus {
  outline: none;
  border-color: #1a73e8;
  box-shadow: 0 1px 2px 0 rgba(26,115,232,0.3);
}

.profileField:hover {
  background-color: #f8f9fa;
}

.profileField label {
  color: #5f6368;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 1rem;
  white-space: nowrap;
}

.fieldIcon {
  color: #5f6368;
  font-size: 1.2rem;
}

.profileField span,
.profileField input {
  color: #202124;
  text-align: right;
  word-break: break-word;
  font-size: 1.1rem;
  background: transparent;
  padding: 8px 0;
  border-radius: 4px;
  border: none;
  width: 100%;
}

/* أنماط صورة الملف الشخصي */
.profilePhotoContainer {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 8px;
}

.profilePhoto {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  object-fit: cover;
  border: 2px solid #dadce0;
}

.profilePhotoPlaceholder {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  background-color: #1a73e8;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 2rem;
  font-weight: bold;
}

.changePhotoText {
  color: #1a73e8;
  font-size: 0.85rem;
  cursor: pointer;
  display: inline-block;
}

/* أنماط صفحة تغيير الصورة */
.photoUploadContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 20px;
  width: 100%;
}

.photoPreviewArea {
  width: 200px;
  height: 200px;
  border-radius: 50%;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 3px solid #dadce0;
  margin-bottom: 20px;
}

.photoPreview {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.photoPlaceholder {
  width: 100%;
  height: 100%;
  background-color: #f1f3f4;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #5f6368;
}

.photoActions {
  display: flex;
  gap: 10px;
  margin-bottom: 20px;
}

.selectPhotoButton {
  background-color: #1a73e8;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 4px;
  cursor: pointer;
  font-weight: 500;
}

.selectPhotoButton:hover {
  background-color: #1765cc;
}

.profileField input {
  background: #ffffff;
  border: 1px solid #dadce0;
  padding: 8px 12px;
}

.profileField input:focus {
  outline: none;
  border-color: #1a73e8;
  box-shadow: 0 1px 2px 0 rgba(26,115,232,0.3);
}

/* Buttons */
.buttonRow {
  display: flex;
  gap: 12px;
  justify-content: flex-end; /* Align buttons to the right */
  padding: 16px 0 0 0;
  flex-shrink: 0; /* Prevent button row from shrinking */
  border-top: 1px solid #dadce0;
  margin-top: 16px;
}

.editButton,
.saveButton,
.cancelButton {
  padding: 0 var(--spacing-xl);
  border: none;
  border-radius: var(--radius-sm);
  cursor: pointer;
  font-size: 0.875rem;
  font-weight: var(--font-weight-medium);
  transition: var(--transition-all);
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  height: 36px;
  text-transform: uppercase;
  letter-spacing: 0.25px;
}

.editButton {
  background: #1a73e8;
  color: #fff;
}
.editButton:hover { background: #1765cc; box-shadow: 0 1px 2px 0 rgba(26,115,232,0.3); }

.saveButton {
  background: #1a73e8;
  color: #fff;
}
.saveButton:hover { background: #1765cc; box-shadow: 0 1px 2px 0 rgba(26,115,232,0.3); }

.cancelButton {
  background: #ffffff;
  color: #5f6368;
  border: 1px solid #dadce0;
}
.cancelButton:hover { background: #f1f3f4; }

/* Account Management Specific Styles */
.accountButton {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 10px 16px;
  border: none;
  border-radius: 4px;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  background: #ffffff;
  color: #5f6368;
  border: 1px solid #dadce0;
  width: 100%;
  margin-bottom: 12px;
}
.accountButton:hover { 
  background: #f1f3f4;
}
.accountButton.activeOnline { 
  background: #e8f0fe;
  color: #1a73e8;
  border-color: #d2e3fc;
}
.accountButton.activeLocal { 
  background: #e6f4ea;
  color: #137333;
  border-color: #ceead6;
}

.accountIcon { font-size: 1.1rem; }

.accountStatus {
  display: flex;
  align-items: center;
  justify-content: space-between; /* Space out elements */
  gap: 8px;
  font-size: 0.875rem;
  background: #ffffff;
  padding: 12px 16px;
  border-radius: 0;
  margin-bottom: 0;
  color: #5f6368;
  border-bottom: 1px solid #f9f9f9;
}
.accountStatus span { display: flex; align-items: center; gap: 6px; }
.onlineText { color: #1a73e8; font-weight: 500; }
.localText { color: #137333; font-weight: 500; }

.statusOnline, .statusOffline {
  width: 8px;
  height: 8px;
  border-radius: 50%;
}
.statusOnline { background-color: #caa5cb; }
.statusOffline { background-color: #ea4335; }

.accountInfoBox {
  background: #ffffff;
  border-radius: 4px;
  padding: 16px;
  border: 1px solid #dadce0;
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-bottom: 16px;
}

.accountInfoField {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 0.875rem;
  padding: 4px 0;
}
.accountInfoField label { font-weight: 500; color: #5f6368; }
.accountInfoField span { color: #202124; }

.accountActions {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-top: 0;
  padding: 16px;
  background: #ffffff;
}

.manageMembersButton,
.viewOfflineButton,
.deleteAccountButton {
  background: #ffffff;
  color: #000000;
  padding: 8px 16px;
  border: 1px solid #020304;
  cursor: pointer;
  font-size: 0.875rem;
  transition: all 0.2s ease;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  gap: 12px;
  margin-bottom: 8px;
  font-weight: 500;
  height: 36px;
    border-radius: 24px !important;
}
.manageMembersButton:hover { 
  background: #f1f3f4;
}
.viewOfflineButton { 
  color: #1a73e8;
}
.viewOfflineButton:hover { 
  background: #f1f3f4;
}
.deleteAccountButton {
  color: #d93025;
}
.deleteAccountButton:hover {
  background: #f1f3f4;
}

/* Section Titles */
.sectionTitle {
  color: #202124;
  margin: 0 0 20px 0;
  font-size: 1.4rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 12px;
  padding-bottom: 12px;
  border-bottom: 1px solid #dadce0;
}

.sidebarNavItem {
  display: flex;
  align-items: center;
  padding: 16px 24px;
  font-size: 1rem;
  color: #5f6368;
  cursor: pointer;
  transition: all 0.2s ease;
  border-radius: 0;
  font-weight: 600;
  gap: 16px;
  border-bottom: 1px solid #f9f9f9;
  user-select: none;
}

.sidebarNavItem:hover {
  background-color: #fafafa;
}

.sidebarNavItem.active {
  color: #1a73e8;
  background-color: #ffffff;
  font-weight: 500;
  border-left: 4px solid #1a73e8;
}

.sidebarNavIcon {
  font-size: 24px;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.sectionIcon { font-size: 1.2rem; }
/* Adjust icon colors for sections */
.accountManagementSection .sectionIcon { color: #81c784; } /* Greenish for account */
.personalInfoSection .sectionIcon { color: #a5b4fc; } /* Lighter blue for personal */

/* Loading and Error States */
.loadingContainer,
.errorContainer {
  position: absolute; /* Center in the pageWrapper */
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
  padding: 30px;
  background: rgba(0, 0, 0, 0.5);
  border-radius: 12px;
  backdrop-filter: blur(10px);
  z-index: 10;
}

.spinner {
  border: 4px solid rgba(255, 255, 255, 0.2);
  border-left: 4px solid #fff;
  border-radius: 50%;
  width: 30px;
  height: 30px;
  animation: spin 1s linear infinite;
  margin: 0 auto 15px;
}
@keyframes spin { 0% { transform: rotate(0deg); } 100% { transform: rotate(360deg); } }

.errorContainer { color: #ff8a80; background: rgba(255, 138, 128, 0.2); border: 1px solid rgba(255, 138, 128, 0.4); }
.errorMessage { color: #ff8a80; background: rgba(255, 138, 128, 0.1); padding: 10px; border-radius: 6px; margin-bottom: 10px; border: 1px solid rgba(255, 138, 128, 0.3); text-align: center; font-size: 0.9rem; }

/* Modal Styles */
.modalOverlay { position: fixed; top: 0; left: 0; right: 0; bottom: 0; background: rgba(0, 0, 0, 0.7); display: flex; justify-content: center; align-items: center; z-index: 1000; backdrop-filter: blur(5px); }
.modal { background: rgba(40, 40, 40, 0.9); border-radius: 12px; padding: 25px; max-width: 450px; width: 90%; text-align: center; box-shadow: 0 8px 30px rgba(0, 0, 0, 0.4); border: 1px solid rgba(255, 255, 255, 0.1); }
.modal h3 { margin-top: 0; margin-bottom: 15px; color: #fff; font-size: 1.3rem; }
.modal p { margin-bottom: 20px; line-height: 1.5; font-size: 0.95rem; color: #eee; }
.modalButtons { display: flex; gap: 15px; margin-top: 20px; justify-content: center; }
.confirmButton { background: #3b82f6; color: #fff; padding: 10px 20px; border-radius: 8px; font-size: 0.95rem; cursor: pointer; transition: all 0.2s ease; border: none; }
.confirmButton:hover { background: #2563eb; }
/* Use the same cancelButton style */

/* Responsive Design */
@media (max-width: 768px) {
  .mainContainer {
    flex-direction: column; /* Stack sidebar and content vertically */
    height: auto; /* Allow container to grow */
    padding: 10px;
    overflow-y: auto; /* Allow scroll on the container for small screens */
    overflow-x: hidden;
    margin-top: 110px; /* إضافة هامش علوي للمحتوى لإفساح المجال للبار العلوي والبار الثاني */
  }

  .accountManagementSection {
    width: 100%; /* Full width */
    height: 40px; /* ارتفاع ثابت */
    position: fixed;
    top: 96px; /* وضع البار ملتصق بالبار الثاني */
    right: 0;
    left: 0;
    padding: 0;
    margin: 0;
    display: flex;
    flex-direction: row; /* تغيير اتجاه العناصر إلى أفقي */
    overflow-x: visible; /* إظهار جميع العناصر */
    background-color: #ffffff;
    box-shadow: none;
    z-index: 99;
    border-bottom: 1px solid #f1f3f4;
  }

  .sidebarNavItem {
    padding: 0 5px;
    border: none;
    flex: 1;
    min-width: auto;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.85rem;
    height: 40px;
    text-align: center;
    line-height: 1.2;
    background-color: #ffffff;
  }

  .sidebarNavIcon {
    display: none; /* إخفاء الأيقونات في وضع الهاتف */
  }

  .sidebarNavItem.active {
    border-left: none;
    border-bottom: 3px solid #1a73e8;
    color: #1a73e8;
    font-weight: 600;
  }

  .mainContentArea {
    width: 100%;
    height: auto; /* Adjust height based on content */
    padding: 15px;
    padding-right: 15px; /* إزالة الهامش الأيمن الكبير */
  }

  .pageHeader {
    padding: 10px 15px;
    width: 100%;
    text-align: center;
  }

  .pageTitle {
    font-size: 1.4rem;
  }

  .personalInfoSection {
    margin-right: 0;
    margin-left: 0;
    width: 100%;
    padding: 20px 15px;
  }

  .profileField {
    grid-template-columns: 1fr; /* Stack label and input */
    gap: 5px;
    padding: 10px;
  }

  .profileField label {
    justify-content: flex-start; /* Align label left */
    font-size: 0.8rem;
  }

  .profileField span,
  .profileField input {
    text-align: left; /* Align text left */
    font-size: 0.85rem;
  }

  .buttonRow {
    justify-content: center; /* Center buttons */
  }
}
.profileInfoWithImage {
  display: flex;
  flex-direction: row;
  align-items: flex-start;
  gap: 18px;
  font-size: 1.07rem;
  color: #444;
  line-height: 1.9;
  margin: 0 0 18px 0;
  direction: rtl;
  text-align: right;
}

@media (max-width: 600px) {
  .profileInfoWithImage {
    flex-direction: column-reverse;
    align-items: stretch;
    text-align: right;
  }

}
@media (max-width: 400px) {
  .profileHeaderContainer {
    flex-direction: column;
    gap: 24px;
    margin-left: 0;
    text-align: center;
     margin-right: '10px';
  }
}

.accountCardsDivider {
  width: 100%;
  height: 1px;
  background: #e0e7ef;
  margin: 24px 0 0 0;
  border: none;
}
.accountCardsContainer {
  display: flex;
  gap: 24px;
  margin: 24px 0;
}

@media (max-width: 600px) {
  .accountCardsContainer {
    flex-direction: column;
    gap: 16px;
    margin: 16px 0;
  }
  .accountCardsContainer > div {
    min-width: 0 !important;
    width: 100% !important;
    max-width: 100% !important;
  }
}

/* Modal Styles */
.modalOverlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 10000;
  animation: fadeIn 0.3s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideIn {
  from {
    transform: translateY(-30px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

.modal {
  background: white;
  border-radius: 12px;
  padding: 24px;
  max-width: 400px;
  width: 90%;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
  text-align: center;
  direction: rtl;
  animation: slideIn 0.3s ease-out;
}

.modal h3 {
  margin: 0 0 16px 0;
  font-size: 1.25rem;
  font-weight: 600;
}

.modal p {
  margin: 0 0 16px 0;
  color: #666;
  line-height: 1.5;
}

.modalButtons {
  display: flex;
  gap: 12px;
  justify-content: center;
  margin-top: 20px;
  flex-wrap: wrap;
}

.confirmButton,
.cancelButton,
.deleteButton {
  padding: 10px 20px;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.2s ease;
}

.confirmButton {
  background: #1976d2;
  color: white;
}

.confirmButton:hover {
  background: #1565c0;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(25, 118, 210, 0.3);
}

.cancelButton {
  background: #f5f5f5;
  color: #333;
  border: 1px solid #ddd;
}

.cancelButton:hover {
  background: #e0e0e0;
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.deleteButton {
  background: #d32f2f;
  color: white;
}

.deleteButton:hover {
  background: #c62828;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(211, 47, 47, 0.3);
}

@media (max-width: 480px) {
  .modal {
    width: 95%;
    padding: 20px;
    margin: 10px;
  }
  
  .modalButtons {
    flex-direction: column;
    gap: 12px;
  }
  
  .confirmButton,
  .cancelButton,
  .deleteButton {
    width: 100%;
    padding: 14px 20px;
    font-size: 16px;
  }
  
  /* تحسين النافذة على الشاشات الصغيرة */
  .modal h3 {
    font-size: 1.2rem;
  }
  
  .modal ul {
    font-size: 0.9rem;
    padding-right: 15px;
  }
}

/* Delete Account Button */
.deleteAccountButton {
  background: #f44336;
  color::rgba(244, 54, 54, 0.85);
  border: none;
  border-radius: 8px;
  padding: 8px 16px;
  font-size: 13px;
  cursor: pointer;
  transition: background-color 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
  font-weight: 500;
}

.deleteAccountButton:hover {
  background: #d32f2f;
}

.deleteAccountButton:active {
  background: #c62828;
}

