.taskAssignmentContainer {
  position: relative;
  display: inline-block;
}

.assignedMember {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.25rem 0.5rem;
  background-color: #e8f5e8;
  border: 1px solid #4caf50;
  border-radius: 20px;
  font-size: 0.75rem;
  color: #2e7d32;
  height: 44px;
  box-sizing: border-box;
}

.memberName {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  font-weight: 500;
}

.memberIcon {
  font-size: 0.7rem;
}

.removeButton {
  background: none;
  border: none;
  color: #d32f2f;
  cursor: pointer;
  padding: 0.1rem;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.7rem;
  transition: background-color 0.2s;
  width: 20px;
  height: 20px;
}

.removeButton:hover {
  background-color: rgba(211, 47, 47, 0.1);
}

.assignmentButton {
  position: relative;
}

.assignButton {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.25rem;
  width: 44px;
  height: 44px;
  padding: 0;
  background-color: #e8f4fd;
  border: 1px solid #2196f3;
  border-radius: 50%;
  cursor: pointer;
  font-size: 0.75rem;
  color: #1976d2;
  transition: all 0.2s;
  flex-shrink: 0;
  box-sizing: border-box;
}

.assignButton:hover {
  background-color: #2196f3;
  border-color: #1976d2;
  color: white;
}

.assignIcon {
  font-size: 1rem;
}

.membersDropdown {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: white;
  border: 1px solid #ddd;
  border-radius: 4px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  z-index: 1000;
  min-width: 200px;
  max-height: 300px;
  overflow-y: auto;
}

.dropdownHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem;
  background-color: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
  font-size: 0.8rem;
  font-weight: 500;
  color: #495057;
}

.closeButton {
  background: none;
  border: none;
  color: #6c757d;
  cursor: pointer;
  padding: 0.1rem;
  border-radius: 2px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.8rem;
}

.closeButton:hover {
  background-color: rgba(108, 117, 125, 0.1);
}

.loadingMessage {
  padding: 1rem;
  text-align: center;
  color: #6c757d;
  font-size: 0.8rem;
}

.membersList {
  max-height: 200px;
  overflow-y: auto;
}

.memberOption {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  width: 100%;
  padding: 0.5rem;
  background: none;
  border: none;
  text-align: right;
  cursor: pointer;
  font-size: 0.8rem;
  color: #495057;
  transition: background-color 0.2s;
}

.memberOption:hover {
  background-color: #f8f9fa;
}

.memberOption:not(:last-child) {
  border-bottom: 1px solid #e9ecef;
}

.noMembers {
  padding: 1rem;
  text-align: center;
  color: #6c757d;
  font-size: 0.8rem;
  font-style: italic;
}

/* تحسينات للشاشات الصغيرة */
@media (max-width: 768px) {
  .membersDropdown {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 90%;
    max-width: 300px;
    max-height: 60vh;
  }
  
  .assignButton {
    width: 44px;
    height: 44px;
    font-size: 0.9rem;
  }
  
  .assignedMember {
    font-size: 0.7rem;
    padding: 0.2rem 0.4rem;
    height: 44px;
  }
}