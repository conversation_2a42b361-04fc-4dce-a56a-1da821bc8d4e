import React, { useState, useMemo, useCallback } from 'react';
import { FaTrashAlt, FaLink, FaCheck, FaCalendarAlt, FaClock } from 'react-icons/fa';
import styles from "./ReportHistory.module.css";
import { handleStrikeAction, handleDeleteReport, handleCompleteAction, handleCompleteDeferral, handleAddAction } from './ReportDetailsLogic';
import { updateCase, getActiveAccount } from '../../services/StorageService';
import { notifyTaskCreated } from '../../utils/CacheManager';
import { getTaskAssignment, getGroups } from '../../services/GroupsService';
import permissionsService from '../../services/PermissionsService';
import TaskAssignment from '../../components/TaskAssignment';
import AddDeferral from './AddDeferral';
import AddAction from './AddAction';

const ReportHistory = React.memo(({ currentUser, actions, deferrals, history, caseItem }) => {
  const [showForm, setShowForm] = useState(null);
  const [localDeferrals, setLocalDeferrals] = useState(deferrals || []);
  const [localActions, setLocalActions] = useState(actions || []);
  const [localHistory, setLocalHistory] = useState(history || []);
  const [taskAssignments, setTaskAssignments] = useState({});
  const [showDeleteConfirmation, setShowDeleteConfirmation] = useState(false);
  const [itemToDelete, setItemToDelete] = useState(null);
  const [deleteLoading, setDeleteLoading] = useState(false);
  const [canDeleteData, setCanDeleteData] = useState(false);
  const [canAddData, setCanAddData] = useState(false);
  const [canAssignTasks, setCanAssignTasks] = useState(false);
  const [userGroups, setUserGroups] = useState([]);

  // تحسين: hook مخصص للتحقق من صحة البيانات
  const isValidData = useMemo(() => {
    return !!(caseItem?.id && currentUser?.uid);
  }, [caseItem?.id, currentUser?.uid]);

  // تحسين: استخدام useMemo بدلاً من useEffect مع JSON.stringify للأداء الأفضل
  const memoizedDeferrals = useMemo(() => deferrals || [], [deferrals]);
  const memoizedActions = useMemo(() => actions || [], [actions]);
  const memoizedHistory = useMemo(() => history || [], [history]);

  // تحديث القوائم عند تغير props
  React.useEffect(() => {
    setLocalDeferrals(memoizedDeferrals);
  }, [memoizedDeferrals]);

  React.useEffect(() => {
    setLocalActions(memoizedActions);
  }, [memoizedActions]);

  React.useEffect(() => {
    setLocalHistory(memoizedHistory);
  }, [memoizedHistory]);

  // التحقق من صلاحيات المستخدم
  React.useEffect(() => {
    const fetchPermissions = async () => {
      if (currentUser?.uid && getActiveAccount() === 'online') {
        try {
          const groups = await getGroups(currentUser.uid);
          setUserGroups(groups);
          
          const userRole = permissionsService.getCurrentUserRole(currentUser.uid, groups);
          const hasDeletePermission = permissionsService.hasPermission(userRole, 'deleteData');
          const hasAddPermission = permissionsService.hasPermission(userRole, 'addData');
          const hasAssignPermission = permissionsService.hasPermission(userRole, 'assignTasks');
          
          setCanDeleteData(hasDeletePermission);
          setCanAddData(hasAddPermission);
          setCanAssignTasks(hasAssignPermission);
          
          console.log('🔍 ReportHistory - User Role:', userRole);
          console.log('🔍 ReportHistory - Can Delete Data (Tasks):', hasDeletePermission);
          console.log('🔍 ReportHistory - Can Add Data (Tasks):', hasAddPermission);
          console.log('🔍 ReportHistory - Can Assign Tasks:', hasAssignPermission);
        } catch (error) {
          console.error('خطأ في جلب صلاحيات المستخدم:', error);
          setCanDeleteData(false);
          setCanAddData(false);
          setCanAssignTasks(false);
        }
      } else {
        // في الوضع المحلي، السماح بالعمليات افتراضياً
        setCanDeleteData(true);
        setCanAddData(true);
        setCanAssignTasks(true);
      }
    };

    fetchPermissions();
  }, [currentUser?.uid]);

  // جلب التكليفات الحالية
  React.useEffect(() => {
    const fetchTaskAssignments = async () => {
      if (!isValidData) return;
      
      const assignments = {};
      
      // جلب تكليفات التأجيلات
      for (const deferral of localDeferrals) {
        const taskId = deferral.id || `${caseItem.id}-defer-${localDeferrals.indexOf(deferral)}`;
        try {
          const assignment = await getTaskAssignment(taskId);
          if (assignment) {
            assignments[taskId] = assignment;
          }
        } catch (error) {
          console.error('خطأ في جلب تكليف التأجيل:', error);
        }
      }
      
      // جلب تكليفات الإجراءات
      for (const action of localActions) {
        const taskId = action.id || `${caseItem.id}-action-${localActions.indexOf(action)}`;
        try {
          const assignment = await getTaskAssignment(taskId);
          if (assignment) {
            assignments[taskId] = assignment;
          }
        } catch (error) {
          console.error('خطأ في جلب تكليف الإجراء:', error);
        }
      }
      
      setTaskAssignments(assignments);
    };
    
    fetchTaskAssignments();
  }, [localDeferrals, localActions, isValidData, caseItem?.id]);

  // معالجة تغيير التكليف
  const handleAssignmentChange = useCallback((taskId, assignment) => {
    setTaskAssignments(prev => {
      const updated = { ...prev };
      if (assignment) {
        updated[taskId] = assignment;
      } else {
        delete updated[taskId];
      }
      return updated;
    });
  }, []);

  const formatDate = (dateString) => {
    if (!dateString) return '';
    const date = new Date(dateString);
    return date.toLocaleDateString('ar-EG', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  // تحسين معالجة البيانات باستخدام useMemo محسن
  const allItems = useMemo(() => {
    const deferralsItems = localDeferrals.map((deferral, idx) => ({
      type: 'deferral',
      data: deferral,
      date: new Date(deferral.date || new Date()),
      index: idx
    }));
    
    const actionsItems = localActions.map((action, idx) => ({
      type: 'action',
      data: action,
      date: new Date(action.deadline || new Date()),
      index: idx
    }));
    
    return [...deferralsItems, ...actionsItems].sort((a, b) => b.date - a.date);
  }, [localDeferrals, localActions]);

  const handleComplete = useCallback(async (item) => {
    // تحسين: استخدام التحقق الموحد
    if (!isValidData) {
      alert('خطأ: بيانات القضية أو المستخدم غير متوفرة');
      return;
    }
    
    try {
      if (item.type === 'deferral') {
        // العثور على الفهرس الصحيح للتأجيلة في المصفوفة المحلية
        const deferralIndex = localDeferrals.findIndex(deferral => 
          deferral.id === item.data.id || 
          (deferral.date === item.data.date && JSON.stringify(deferral.reasons) === JSON.stringify(item.data.reasons))
        );
        
        if (deferralIndex === -1) {
          alert('خطأ: لم يتم العثور على التأجيلة في القائمة');
          return;
        }

        const onJudgmentDetected = (deferralData) => {
          setTimeout(() => {
            if (window.showJudgmentVerdictModal) {
              window.showJudgmentVerdictModal(deferralData);
            }
          }, 100);
        };
        
        const result = await handleCompleteDeferral(deferralIndex, localDeferrals, setLocalDeferrals, caseItem, setLocalHistory, onJudgmentDetected);
        if (result !== 'judgment_detected') {
          console.log('✅ تم إكمال التأجيلة وإضافتها للأرشيف الزمني');
          // إخطار المستخدم بنجاح العملية
          alert('تم تسجيل الحضور بنجاح وإضافة الحدث للأرشيف الزمني');
        }
      } else {
        // العثور على الفهرس الصحيح للإجراء في المصفوفة المحلية
        const actionIndex = localActions.findIndex(action => 
          action.id === item.data.id || 
          (action.description === item.data.description && action.deadline === item.data.deadline)
        );
        
        if (actionIndex === -1) {
          alert('خطأ: لم يتم العثور على الإجراء في القائمة');
          return;
        }

        // استخدام معرف الإجراء الصحيح
        const actionId = item.data.id || `${caseItem.id}-action-${actionIndex}`;
        
        // دالة handleCompleteAction تتعامل مع تحديث قاعدة البيانات داخلياً
        await handleCompleteAction(actionId, localActions, setLocalActions, caseItem, setLocalHistory);
        console.log('✅ تم إكمال الإجراء وإضافته للأرشيف الزمني');
        // إخطار المستخدم بنجاح العملية
        alert('تم تسجيل إنجاز المهمة بنجاح وإضافة الحدث للأرشيف الزمني');
      }
    } catch (error) {
      console.error('خطأ في إكمال المهمة:', error);
      alert('حدث خطأ أثناء إكمال المهمة: ' + error.message);
    }
  }, [localDeferrals, localActions, caseItem, currentUser, isValidData]);

  const handleDelete = useCallback((item) => {
    // تحسين: استخدام التحقق الموحد
    if (!isValidData) {
      alert('خطأ: بيانات القضية أو المستخدم غير متوفرة');
      return;
    }
    
    // فتح نافذة التأكيد
    setItemToDelete(item);
    setShowDeleteConfirmation(true);
  }, [isValidData]);

  // دالة تأكيد الحذف
  const confirmDelete = useCallback(async () => {
    if (!itemToDelete) return;
    
    setDeleteLoading(true);
    try {
      const activeAccount = getActiveAccount();
      const accountType = activeAccount === 'online' ? 'أونلاين' : 'محلي';
      
      if (itemToDelete.type === 'deferral') {
        // العثور على الفهرس الصحيح للتأجيلة في المصفوفة المحلية
        const deferralIndex = localDeferrals.findIndex(deferral => 
          deferral.id === itemToDelete.data.id || 
          (deferral.date === itemToDelete.data.date && JSON.stringify(deferral.reasons) === JSON.stringify(itemToDelete.data.reasons))
        );
        
        if (deferralIndex === -1) {
          alert('خطأ: لم يتم العثور على التأجيلة في القائمة');
          return;
        }

        // استخدام الدالة المصححة مع المعاملات الصحيحة
        await handleDeleteReport(deferralIndex, localDeferrals, setLocalDeferrals, localHistory, setLocalHistory, caseItem, localActions, setLocalActions);
        console.log('✅ تم حذف التأجيلة بنجاح');
      } else {
        // العثور على الفهرس الصحيح للإجراء في المصفوفة المحلية
        const actionIndex = localActions.findIndex(action => 
          action.id === itemToDelete.data.id || 
          (action.description === itemToDelete.data.description && action.deadline === itemToDelete.data.deadline)
        );
        
        if (actionIndex === -1) {
          alert('خطأ: لم يتم العثور على الإجراء في القائمة');
          return;
        }

        // استخدام معرف الإجراء الصحيح
        const actionId = itemToDelete.data.id || `${caseItem.id}-action-${actionIndex}`;
        
        // دالة handleStrikeAction تتعامل مع تحديث قاعدة البيانات داخلياً
        await handleStrikeAction(actionId, localActions, setLocalActions, caseItem, setLocalHistory);
        console.log('✅ تم حذف الإجراء بنجاح');
      }
      
      setShowDeleteConfirmation(false);
      setItemToDelete(null);
      alert(`تم حذف ${itemToDelete.type === 'deferral' ? 'التأجيلة' : 'الإجراء'} بنجاح من الحساب ${accountType}`);
    } catch (error) {
      const activeAccount = getActiveAccount();
      const accountType = activeAccount === 'online' ? 'الأونلاين' : 'المحلي';
      console.error('خطأ في حذف المهمة:', error);
      alert(`حدث خطأ أثناء حذف ${itemToDelete.type === 'deferral' ? 'التأجيلة' : 'الإجراء'} من الحساب ${accountType}. يرجى المحاولة مرة أخرى.`);
    } finally {
      setDeleteLoading(false);
    }
  }, [itemToDelete, localDeferrals, localActions, localHistory, caseItem]);

  // دالة إلغاء الحذف
  const cancelDelete = useCallback(() => {
    setShowDeleteConfirmation(false);
    setItemToDelete(null);
  }, []);

  const handleSaveDeferral = async (reportDate, selectedReasons, deferralDescription, setError) => {
    try {
      // التحقق من وجود البيانات المطلوبة
      if (!caseItem || !caseItem.id) {
        setError("خطأ: بيانات القضية غير متوفرة");
        return;
      }
      
      if (!currentUser || !currentUser.uid) {
        setError("خطأ: بيانات المستخدم غير متوفرة");
        return;
      }
      
      if (!reportDate || isNaN(new Date(reportDate).getTime()) || selectedReasons.length === 0) {
        setError("يرجى إدخال تاريخ التأجيلة واختيار سبب واحد على الأقل");
        return;
      }
      
      const newDeferral = {
        id: `${caseItem.id}-defer-${Date.now()}`,
        date: reportDate,
        reasons: selectedReasons,
        description: deferralDescription || '',
        createdAt: new Date().toISOString(),
        isDeleted: false,
        isArchived: false
      };
      const updatedDeferrals = [...localDeferrals, newDeferral];
      setLocalDeferrals(updatedDeferrals);
      await updateCase(currentUser.uid, caseItem.id, { deferrals: updatedDeferrals });
      alert('تم إضافة التنبيه بتاريخ الجلسة بنجاح');
      notifyTaskCreated(currentUser.uid);
      setShowForm(null);
    } catch (err) {
      console.error('خطأ في حفظ التأجيل:', err);
      setError(err.message);
    }
  };

  const handleSaveAction = async (newAction, actionDeadline, linkType, linkedDeferralId, linkedActionId, reminderType, setError) => {
    try {
      // التحقق من وجود البيانات المطلوبة
      if (!caseItem || !caseItem.id) {
        setError("خطأ: بيانات القضية غير متوفرة");
        return;
      }
      
      if (!currentUser || !currentUser.uid) {
        setError("خطأ: بيانات المستخدم غير متوفرة");
        return;
      }
      
      const updatedCaseData = { ...caseItem, deferrals: localDeferrals, actions: localActions };
      
      await handleAddAction(
        newAction,
        actionDeadline,
        linkType,
        linkedDeferralId,
        linkedActionId,
        reminderType,
        updatedCaseData,
        localActions,
        setLocalActions,
        () => {},
        () => {},
        () => {},
        () => {},
        () => {},
        () => {},
        setLocalHistory
      );
      
      // إزالة هذا السطر لأن handleAddAction تتعامل مع حفظ البيانات داخلياً
      // await updateCase(currentUser.uid, caseItem.id, { actions: localActions });
      notifyTaskCreated(currentUser.uid);
      setShowForm(null);
    } catch (err) {
      console.error('خطأ في حفظ الإجراء:', err);
      setError(err.message);
    }
  };

  return (
    <div className={styles.pageCardContainer}>
      <div className={styles.historyContainer}>
        <div className={styles.simpleHistoryHeader}>سجل المهام</div>
        <div className={styles.historyContent}>
          <div className={styles.itemsList}>
            {allItems.length > 0 ? (
              allItems.map((item, index) => (
                <div
                  key={index}
                  className={`${styles.historyItem} ${item.type === 'action' ? styles.actionItem : styles.deferralItem}`}
                >
                  <div className={styles.itemContent}>
                    <div className={styles.itemDateAndBadge}>
                      <div className={styles.itemDate}>
                        <FaCalendarAlt className={styles.dateIcon} />
                        {formatDate(item.type === 'deferral' ? item.data.date : item.data.deadline)}
                      </div>
                      <div className={styles.itemBadge}>
                        <span className={`${styles.typeBadge} ${
                          item.type === 'deferral' ? styles.deferralBadge : styles.actionBadge
                        }`}>
                          {item.type === 'deferral' ? 'تأجيل' : 'إجراء'}
                        </span>
                      </div>
                    </div>
                    <div className={styles.itemMain}>
                      <div className={styles.itemText}>
                        {item.type === 'deferral' ? (
                          <>
                            {item.data.reasons?.join('، ') || 'تأجيل بدون تفاصيل'}
                            {item.data.description && ` - ${item.data.description}`}
                          </>
                        ) : (
                          <>
                            {item.data.description}
                            {item.data.linkedDeferralId && (
                              <span className={styles.linkIndicator}>
                                <FaLink className={styles.linkIcon} />
                                مرتبط بتأجيل
                              </span>
                            )}
                          </>
                        )}
                      </div>
                    </div>
                    <div className={styles.itemActions}>
                      {canAssignTasks && (
                        <TaskAssignment
                          currentUser={currentUser}
                          taskId={item.data.id || `${caseItem.id}-${item.type}-${item.index}`}
                          taskType={item.type}
                          taskData={item.data}
                          caseId={caseItem.id}
                          assignedMember={taskAssignments[item.data.id || `${caseItem.id}-${item.type}-${item.index}`]?.assignedTo}
                          onAssignmentChange={(assignment) => 
                            handleAssignmentChange(item.data.id || `${caseItem.id}-${item.type}-${item.index}`, assignment)
                          }
                        />
                      )}
                      <button
                        onClick={() => handleComplete(item)}
                        className={styles.completeButton}
                        title={item.type === 'deferral' ? "تم الحضور" : "تم الإنجاز"}
                      >
                        <FaCheck />
                      </button>
                      {canDeleteData && (
                        <button
                          onClick={() => handleDelete(item)}
                          className={styles.deleteButton}
                          title="حذف"
                        >
                          <FaTrashAlt />
                        </button>
                      )}
                    </div>
                  </div>
                </div>
              ))
            ) : (
              <div className={styles.noReports}>لا توجد مهام مسجلة</div>
            )}
            {!showForm && canAddData && (
              <div className={`${styles.historyItem} ${styles.actionItem}`}>
                <div className={styles.itemContent}>
                  <div className={styles.itemMain}>
                    <button
                      onClick={() => setShowForm('deferral')}
                      className={styles.actionButton}
                    >
                      <FaCalendarAlt className={styles.buttonIcon} />
                      إضافة تنبيه بتاريخ جلسة
                    </button>
                    <button
                      onClick={() => setShowForm('action')}
                      className={styles.actionButton}
                    >
                      <FaClock className={styles.buttonIcon} />
                      إضافة تنبيه بإجراء
                    </button>
                  </div>
                </div>
              </div>
            )}
            {!showForm && !canAddData && (
              <div className={`${styles.historyItem} ${styles.noPermissionsItem}`}>
                <div className={styles.itemContent}>
                  <div className={styles.noPermissionsMessage}>
                    🔒 ليس لديك صلاحية إضافة تنبيهات جديدة
                  </div>
                </div>
              </div>
            )}
            {showForm === 'deferral' && (
              <AddDeferral
                currentUser={currentUser}
                caseItem={caseItem}
                onClose={() => setShowForm(null)}
                onSave={handleSaveDeferral}
                isUnderConsideration={caseItem?.caseStatus === 'قيد النظر'}
              />
            )}
            {showForm === 'action' && (
              <AddAction
                currentUser={currentUser}
                caseItem={caseItem}
                deferrals={localDeferrals}
                actions={localActions}
                onClose={() => setShowForm(null)}
                onSave={handleSaveAction}
              />
            )}
          </div>
        </div>
      </div>

      {/* نافذة تأكيد الحذف */}
      {showDeleteConfirmation && (
        <div className={styles.deleteModalOverlay}>
          <div className={styles.deleteModal}>
            <div className={styles.deleteModalHeader}>
              <h3>تأكيد الحذف</h3>
            </div>
            <div className={styles.deleteModalContent}>
              <p>هل أنت متأكد من حذف هذا {itemToDelete?.type === 'deferral' ? 'التأجيل' : 'الإجراء'}؟</p>
              {itemToDelete && (
                <div className={styles.itemPreview}>
                  <div className={styles.previewHeader}>
                    <span className={`${styles.previewBadge} ${
                      itemToDelete.type === 'deferral' ? styles.deferralPreviewBadge : styles.actionPreviewBadge
                    }`}>
                      {itemToDelete.type === 'deferral' ? 'تأجيل' : 'إجراء'}
                    </span>
                    <span className={styles.previewDate}>
                      {formatDate(itemToDelete.type === 'deferral' ? itemToDelete.data.date : itemToDelete.data.deadline)}
                    </span>
                  </div>
                  <div className={styles.previewContent}>
                    {itemToDelete.type === 'deferral' ? (
                      <>
                        <strong>الأسباب: </strong>
                        {itemToDelete.data.reasons?.join('، ') || 'تأجيل بدون تفاصيل'}
                        {itemToDelete.data.description && (
                          <>
                            <br />
                            <strong>الوصف: </strong>
                            {itemToDelete.data.description}
                          </>
                        )}
                      </>
                    ) : (
                      <>
                        <strong>الوصف: </strong>
                        {itemToDelete.data.description}
                        {itemToDelete.data.linkedDeferralId && (
                          <>
                            <br />
                            <strong>مرتبط بتأجيل</strong>
                          </>
                        )}
                      </>
                    )}
                  </div>
                </div>
              )}
            </div>
            <div className={styles.deleteModalActions}>
              <button 
                className={styles.cancelButton}
                onClick={cancelDelete}
                disabled={deleteLoading}
              >
                إلغاء
              </button>
              <button 
                className={styles.confirmDeleteButton}
                onClick={confirmDelete}
                disabled={deleteLoading}
              >
                {deleteLoading ? 'جاري الحذف...' : 'حذف'}
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
});

export default ReportHistory;
