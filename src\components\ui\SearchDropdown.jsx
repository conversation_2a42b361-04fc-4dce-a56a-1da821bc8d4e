import React, { useState, useEffect, useRef, useMemo } from 'react';
import { useNavigate } from 'react-router-dom';
import { FaSearch, FaFileAlt, FaUser, FaMapMarkerAlt, FaCalendarAlt, FaSpinner } from 'react-icons/fa';
import { getCases } from '../../services/StorageService';
import { getActiveAccount } from '../../services/StorageService';
import styles from './SearchDropdown.module.css';

const SearchDropdown = ({ currentUser, placeholder = "ابحث عن قضية أو موكل..." }) => {
  const navigate = useNavigate();
  const [searchTerm, setSearchTerm] = useState('');
  const [suggestions, setSuggestions] = useState([]);
  const [isOpen, setIsOpen] = useState(false);
  const [loading, setLoading] = useState(false);
  const [allCases, setAllCases] = useState([]);
  const [selectedIndex, setSelectedIndex] = useState(-1);
  const searchRef = useRef(null);
  const dropdownRef = useRef(null);

  // جلب جميع القضايا عند تحميل المكون
  useEffect(() => {
    const fetchAllCases = async () => {
      if (!currentUser) return;
      
      try {
        setLoading(true);
        const activeAccount = getActiveAccount();
        
        // جلب معرفات المجموعات
        let groupIds = [];
        try {
          const groups = await import('../../services/GroupsService').then(mod => mod.getGroups(currentUser.uid));
          const resolvedGroups = await groups;
          groupIds = Array.isArray(resolvedGroups) ? resolvedGroups.map(g => g.id) : [];
        } catch (e) {
          console.log('خطأ في جلب المجموعات:', e);
          groupIds = [];
        }
        
        const cases = await getCases(currentUser.uid, groupIds);
        setAllCases(cases);
      } catch (error) {
        console.error('خطأ في جلب القضايا:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchAllCases();
  }, [currentUser]);

  // فلترة الاقتراحات بناءً على النص المدخل
  const filteredSuggestions = useMemo(() => {
    if (!searchTerm.trim() || searchTerm.length < 2) return [];
    
    const searchTermLower = searchTerm.toLowerCase();
    return allCases
      .filter(caseItem =>
        (caseItem.fullCaseNumber || '').toLowerCase().includes(searchTermLower) ||
        (caseItem.clientName || '').toLowerCase().includes(searchTermLower) ||
        (caseItem.courtLocation || '').toLowerCase().includes(searchTermLower) ||
        (caseItem.reportLocation || '').toLowerCase().includes(searchTermLower)
      )
      .slice(0, 8); // عرض أول 8 نتائج فقط
  }, [allCases, searchTerm]);

  // تحديث الاقتراحات عند تغيير النص
  useEffect(() => {
    setSuggestions(filteredSuggestions);
    setIsOpen(filteredSuggestions.length > 0 && searchTerm.length >= 2);
    setSelectedIndex(-1); // إعادة تعيين الفهرس المحدد
  }, [filteredSuggestions, searchTerm]);

  // إغلاق القائمة عند النقر خارجها
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  const handleInputChange = (e) => {
    const value = e.target.value;
    setSearchTerm(value);
  };

  const handleKeyDown = (e) => {
    if (!isOpen || suggestions.length === 0) {
      if (e.key === 'Enter' && searchTerm.trim()) {
        setIsOpen(false);
        navigate(`/reports?search=${encodeURIComponent(searchTerm.trim())}`);
      }
      return;
    }

    switch (e.key) {
      case 'ArrowDown':
        e.preventDefault();
        setSelectedIndex(prev =>
          prev < suggestions.length - 1 ? prev + 1 : prev
        );
        break;
      case 'ArrowUp':
        e.preventDefault();
        setSelectedIndex(prev => prev > 0 ? prev - 1 : -1);
        break;
      case 'Enter':
        e.preventDefault();
        if (selectedIndex >= 0 && selectedIndex < suggestions.length) {
          handleSuggestionClick(suggestions[selectedIndex]);
        } else if (searchTerm.trim()) {
          setIsOpen(false);
          navigate(`/reports?search=${encodeURIComponent(searchTerm.trim())}`);
        }
        break;
      case 'Escape':
        setIsOpen(false);
        setSelectedIndex(-1);
        break;
    }
  };

  const handleSuggestionClick = (caseItem) => {
    setIsOpen(false);
    setSearchTerm('');
    navigate(`/case-details/${caseItem.id}`);
  };

  const formatDate = (dateString) => {
    if (!dateString) return 'غير محدد';
    try {
      const date = new Date(dateString);
      return date.toLocaleDateString('ar-EG', {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
      });
    } catch {
      return 'غير محدد';
    }
  };

  const getCaseTitle = (caseItem) => {
    return caseItem.fullCaseNumber ||
           `${caseItem.caseNumber || 'غير محدد'} لسنة ${caseItem.caseYear || 'غير محدد'}`;
  };

  // دالة لتمييز النص المطابق
  const highlightText = (text, searchTerm) => {
    if (!text || !searchTerm) return text;

    const regex = new RegExp(`(${searchTerm.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')})`, 'gi');
    const parts = text.split(regex);

    return parts.map((part, index) =>
      regex.test(part) ? (
        <mark key={index} className={styles.highlight}>{part}</mark>
      ) : part
    );
  };

  return (
    <div className={styles.searchContainer} ref={dropdownRef}>
      <div className={styles.searchBox}>
        <FaSearch className={styles.searchIcon} />
        <input
          ref={searchRef}
          type="text"
          placeholder={placeholder}
          value={searchTerm}
          onChange={handleInputChange}
          onKeyDown={handleKeyDown}
          className={styles.searchInput}
          onFocus={() => {
            if (filteredSuggestions.length > 0 && searchTerm.length >= 2) {
              setIsOpen(true);
            }
          }}
        />
        {loading && (
          <div className={styles.loadingSpinner}>
            <FaSpinner className={styles.spinnerIcon} />
          </div>
        )}
      </div>

      {isOpen && searchTerm.length >= 2 && (
        <div className={styles.dropdown}>

          {suggestions.length === 0 ? (
            <div className={styles.noResults}>
              <span>لم يتم العثور على قضايا تطابق البحث "{searchTerm}"</span>
            </div>
          ) : (
            suggestions.map((caseItem, index) => (
              <div
              key={caseItem.id}
              className={`${styles.suggestionItem} ${
                index === selectedIndex ? styles.selected : ''
              }`}
              onClick={() => handleSuggestionClick(caseItem)}
              >
                <div className={styles.suggestionMain}>
                  <div className={styles.suggestionTitle}>
                    <FaFileAlt className={styles.suggestionIcon} />
                    <span className={styles.caseNumber}>
                      {highlightText(getCaseTitle(caseItem), searchTerm)}
                    </span>
                  </div>
                  <div className={styles.suggestionDetails}>
                    {caseItem.clientName && (
                      <div className={styles.detailItem}>
                        <FaUser className={styles.detailIcon} />
                        <span>{highlightText(caseItem.clientName, searchTerm)}</span>
                      </div>
                    )}
                    {caseItem.courtLocation && (
                      <div className={styles.detailItem}>
                        <FaMapMarkerAlt className={styles.detailIcon} />
                        <span>{highlightText(caseItem.courtLocation, searchTerm)}</span>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            ))
          )}
        </div>
      )}
    </div>
  );
};

export default SearchDropdown;
