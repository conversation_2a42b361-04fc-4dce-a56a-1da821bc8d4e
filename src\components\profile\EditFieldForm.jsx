import React from 'react';
import { FaUser, FaPhone, FaBuilding, FaSave, FaTimes } from 'react-icons/fa';
import styles from './ProfilePage.module.css';

const EditFieldForm = ({ 
  editingField, 
  formData, 
  handleChange, 
  handleSave, 
  handleCancel 
}) => {
  return (
    <div className={styles.personalInfoSection}> 
      <h3 className={styles.sectionTitle}>تعديل المعلومات</h3>
      <div className={styles.profileDetails}>
        <div className={styles.editFieldForm}>
          {editingField === 'name' && (
            <div className={styles.formGroup}>
              <label><FaUser className={styles.fieldIcon} /> الاسم:</label>
              <input 
                type="text" 
                name="name" 
                value={formData.name} 
                onChange={handleChange} 
                className={styles.formInput}
                autoFocus
              />
            </div>
          )}
          {editingField === 'phone' && (
            <div className={styles.formGroup}>
              <label><FaPhone className={styles.fieldIcon} /> رقم الهاتف:</label>
              <input 
                type="text" 
                name="phone" 
                value={formData.phone} 
                onChange={handleChange} 
                className={styles.formInput}
                autoFocus
              />
            </div>
          )}
          {editingField === 'company' && (
            <div className={styles.formGroup}>
              <label><FaBuilding className={styles.fieldIcon} /> الشركة:</label>
              <input 
                type="text" 
                name="company" 
                value={formData.company} 
                onChange={handleChange} 
                className={styles.formInput}
                autoFocus
              />
            </div>
          )}
          <div className={styles.buttonRow}>
            <button onClick={handleSave} className={styles.saveButton}><FaSave /> حفظ</button>
            <button onClick={handleCancel} className={styles.cancelButton}><FaTimes /> إلغاء</button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default EditFieldForm;