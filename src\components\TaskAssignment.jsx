import React, { useState, useEffect } from 'react';
import { Fi<PERSON><PERSON><PERSON>, FiUser, <PERSON><PERSON><PERSON><PERSON>, FiX } from 'react-icons/fi';
import styles from './TaskAssignment.module.css';
import { 
  getGroups, 
  getMembers, 
  checkUserGroupPermissions,
  saveTaskAssignment,
  removeTaskAssignment,
  getTaskAssignment
} from '../services/GroupsService';
import { getActiveAccount } from '../services/StorageService';

const TaskAssignment = ({ 
  currentUser, 
  taskId, 
  taskType, // 'action' or 'deferral'
  taskData,
  caseId,
  assignedMember,
  onAssignmentChange 
}) => {
  const [showDropdown, setShowDropdown] = useState(false);
  const [members, setMembers] = useState([]);
  const [loading, setLoading] = useState(false);
  const [canAssignTasks, setCanAssignTasks] = useState(false);
  const [activeAccount] = useState(getActiveAccount());

  // التحقق من صلاحيات المستخدم
  useEffect(() => {
    const checkPermissions = async () => {
      if (!currentUser || activeAccount !== 'online') {
        setCanAssignTasks(false);
        return;
      }

      try {
        const groups = await getGroups(currentUser.uid);
        let hasPermission = false;

        for (const group of groups) {
          if (group.isCreator || group.memberRole === 'admin' || group.memberRole === 'editor') {
            hasPermission = true;
            break;
          }
        }

        setCanAssignTasks(hasPermission);
      } catch (error) {
        console.error('خطأ في التحقق من الصلاحيات:', error);
        setCanAssignTasks(false);
      }
    };

    checkPermissions();
  }, [currentUser, activeAccount]);

  // جلب أعضاء المجموعات
  const fetchMembers = async () => {
    if (!currentUser || activeAccount !== 'online') return;

    setLoading(true);
    try {
      const groups = await getGroups(currentUser.uid);
      const allMembers = await getMembers();
      
      // فلترة الأعضاء للمجموعات التي ينتمي إليها المستخدم
      const filteredMembers = allMembers.filter(member =>
        groups.some(group => group.id === member.groupId)
      );

      setMembers(filteredMembers);
    } catch (error) {
      console.error('خطأ في جلب الأعضاء:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleDropdownToggle = () => {
    if (!canAssignTasks) return;
    
    if (!showDropdown) {
      fetchMembers();
    }
    setShowDropdown(!showDropdown);
  };

  const handleMemberSelect = async (member) => {
    try {
      const assignment = {
        taskId,
        taskType,
        taskData,
        caseId,
        assignedTo: {
          id: member.userId || member.id, // استخدام userId إذا كان متوفراً، وإلا استخدم id
          memberId: member.id, // حفظ معرف العضوية أيضاً للمرجعية
          name: member.name || member.email,
          email: member.email,
          groupId: member.groupId
        },
        assignedBy: {
          id: currentUser.uid,
          name: currentUser.displayName || currentUser.email,
          email: currentUser.email
        },
        assignedAt: new Date().toISOString()
      };

      // حفظ التكليف في Firebase
      await saveTaskAssignment(assignment);
      
      // تحديث الحالة المحلية
      onAssignmentChange(assignment);
      setShowDropdown(false);
    } catch (error) {
      console.error('خطأ في تكليف المهمة:', error);
      alert('حدث خطأ أثناء تكليف المهمة');
    }
  };

  const handleRemoveAssignment = async () => {
    try {
      await removeTaskAssignment(taskId);
      onAssignmentChange(null);
    } catch (error) {
      console.error('خطأ في إلغاء التكليف:', error);
      alert('حدث خطأ أثناء إلغاء التكليف');
    }
  };

  // تم نقل هذه الدوال إلى GroupsService

  // إذا لم يكن لدى المستخدم صلاحيات، لا نعرض الزر
  if (!canAssignTasks || activeAccount !== 'online') {
    return null;
  }

  return (
    <div className={styles.taskAssignmentContainer}>
      {assignedMember ? (
        <div className={styles.assignedMember}>
          <span className={styles.memberName}>
            <FiUser className={styles.memberIcon} />
            {assignedMember.name}
          </span>
          <button
            onClick={handleRemoveAssignment}
            className={styles.removeButton}
            title="إلغاء التكليف"
          >
            <FiX />
          </button>
        </div>
      ) : (
        <div className={styles.assignmentButton}>
          <button
            onClick={handleDropdownToggle}
            className={styles.assignButton}
            title="تكليف عضو"
          >
            <FiUsers className={styles.assignIcon} />
          </button>
          
          {showDropdown && (
            <div className={styles.membersDropdown}>
              <div className={styles.dropdownHeader}>
                <span>اختر عضو للتكليف</span>
                <button
                  onClick={() => setShowDropdown(false)}
                  className={styles.closeButton}
                >
                  <FiX />
                </button>
              </div>
              
              {loading ? (
                <div className={styles.loadingMessage}>جاري تحميل الأعضاء...</div>
              ) : members.length > 0 ? (
                <div className={styles.membersList}>
                  {members.map((member) => (
                    <button
                      key={member.id}
                      onClick={() => handleMemberSelect(member)}
                      className={styles.memberOption}
                    >
                      <FiUser className={styles.memberIcon} />
                      <span>{member.name || member.email}</span>
                    </button>
                  ))}
                </div>
              ) : (
                <div className={styles.noMembers}>لا يوجد أعضاء متاحين</div>
              )}
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default TaskAssignment;