// أداة تحليل الـ CSS في البروفايل

import fs from 'fs';
import path from 'path';

// قراءة ملف CSS
const cssFilePath = './src/components/profile/ProfilePage.module.css';
const cssContent = fs.readFileSync(cssFilePath, 'utf8');

// استخراج كلاسات CSS
const cssClasses = [];
const cssRegex = /\.([a-zA-Z_-][a-zA-Z0-9_-]*)\s*\{/g;
let match;
while ((match = cssRegex.exec(cssContent)) !== null) {
    if (!cssClasses.includes(match[1])) {
        cssClasses.push(match[1]);
    }
}

// البحث عن الكلاسات المستخدمة في ملفات JSX
const jsxFiles = [
    './src/components/profile/ProfilePage.jsx',
    './src/components/profile/EditFieldForm.jsx',
    './src/components/profile/EditFieldPage.jsx',
    './src/components/profile/EditPasswordPage.jsx',
    './src/components/profile/ProfileFields.jsx',
    './src/components/profile/ViewEmailPage.jsx',
    './src/components/profile/ChangePhotoPage.jsx'
];

const usedClasses = new Set();

jsxFiles.forEach(filePath => {
    if (fs.existsSync(filePath)) {
        const content = fs.readFileSync(filePath, 'utf8');
        const usedClassRegex = /styles\.([a-zA-Z_-][a-zA-Z0-9_-]*)/g;
        let match;
        while ((match = usedClassRegex.exec(content)) !== null) {
            usedClasses.add(match[1]);
        }
    }
});

// العثور على الكلاسات غير المستخدمة
const unusedClasses = cssClasses.filter(cls => !usedClasses.has(cls));

// العثور على الكلاسات المكررة
const duplicatedClasses = [];
const seenClasses = new Set();
const cssLines = cssContent.split('\n');

cssLines.forEach((line, index) => {
    const match = line.match(/\.([a-zA-Z_-][a-zA-Z0-9_-]*)\s*\{/);
    if (match) {
        const className = match[1];
        if (seenClasses.has(className)) {
            duplicatedClasses.push({
                name: className,
                line: index + 1
            });
        } else {
            seenClasses.add(className);
        }
    }
});

// إنشاء التقرير
const report = {
    totalCssClasses: cssClasses.length,
    usedClasses: Array.from(usedClasses).sort(),
    unusedClasses: unusedClasses.sort(),
    duplicatedClasses: duplicatedClasses,
    usedClassesCount: usedClasses.size,
    unusedClassesCount: unusedClasses.length,
    duplicatedClassesCount: duplicatedClasses.length
};

console.log('=== تقرير تحليل CSS للبروفايل ===\n');
console.log(`العدد الكلي للكلاسات: ${report.totalCssClasses}`);
console.log(`الكلاسات المستخدمة: ${report.usedClassesCount}`);
console.log(`الكلاسات غير المستخدمة: ${report.unusedClassesCount}`);
console.log(`الكلاسات المكررة: ${report.duplicatedClassesCount}\n`);

if (report.unusedClasses.length > 0) {
    console.log('=== الكلاسات غير المستخدمة ===');
    report.unusedClasses.forEach(cls => console.log(`- .${cls}`));
    console.log('');
}

if (report.duplicatedClasses.length > 0) {
    console.log('=== الكلاسات المكررة ===');
    report.duplicatedClasses.forEach(cls => console.log(`- .${cls.name} في السطر ${cls.line}`));
    console.log('');
}

// حفظ التقرير في ملف
fs.writeFileSync('./profile_css_analysis.json', JSON.stringify(report, null, 2));
console.log('تم حفظ التقرير في ملف profile_css_analysis.json');