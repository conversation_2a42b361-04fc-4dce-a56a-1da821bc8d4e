const n=["ابتدائي","استئنافي","نقض","أخرى"],e={ابتدائي:Array.from(new Set(["مدني","تجاري","عمالي","أحوال شخصية","إيجارات","جنح","جنايات","أخرى"])),استئنافي:Array.from(new Set(["مدني","تجاري","عمالي","أحوال شخصية","إيجارات","جنح","جنايات","أخرى"])),نقض:Array.from(new Set(["نقض مدني","نقض جنائي","أخرى"])),أخرى:["أخرى"]},l=Array.from(new Set([{name:"محكمة القاهرة الجديدة",lat:30.0265,lng:31.4915},{name:"محكمة الجيزة",lat:30.0131,lng:31.2089},{name:"محكمة الإسكندرية",lat:31.2001,lng:29.9187},{name:"محكمة المنصورة",lat:31.0409,lng:31.3785},{name:"محكمة طنطا",lat:30.7845,lng:31.0015},{name:"محكمة أسيوط",lat:27.1809,lng:31.1837},{name:"محكمة الزقازيق",lat:30.5877,lng:31.502},{name:"محكمة بنها",lat:30.4659,lng:31.183},{name:"محكمة دمنهور",lat:31.0341,lng:30.4692},{name:"محكمة سوهاج",lat:26.5571,lng:31.6948},{name:"أخرى",lat:null,lng:null}].map(a=>JSON.stringify(a)))).map(a=>JSON.parse(a)),t=["قيد النظر","محضر","دعوى قضائية"];export{e as a,t as b,l as c,n as d};
